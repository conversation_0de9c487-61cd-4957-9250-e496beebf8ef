import { FC, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';

import { VerifyIcon } from '../../../assets/svgs';
import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import { Publisher } from '../../../types/posts';
import { APP_ROUTES } from '../../../types/routes';

interface UserItemProps extends Publisher {}

export const UserItem: FC<UserItemProps> = ({
  avatar,
  username,
  first_name,
  last_name,
  verified,
  user_id,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { navigate } = useTypeSafeNavigation();

  const handleNavigate = () => {
    navigate(APP_ROUTES.OTHER_USER_PROFILE, {
      userId: user_id,
      otherUserProfile: true,
    });
  };

  return (
    <Pressable onPress={handleNavigate} style={styles.container}>
      <View style={styles.contentContainer}>
        <FastImage
          source={{
            uri: avatar,
          }}
          style={styles.avatar}
        />
        <View>
          <View style={styles.contentContainer}>
            <CustomText style={styles.username}>{username}</CustomText>
            {Number(verified) ? <VerifyIcon width={18} height={18} /> : null}
          </View>
          <CustomText style={styles.fullName}>
            {first_name} {last_name}
          </CustomText>
        </View>
      </View>

      <Icon name="arrow-up-left" size={26} color={colors.secondary} />
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: moderateScale(16),
      paddingVertical: verticalScale(10),
      backgroundColor: colors.background,
    },
    contentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(8),
    },
    avatar: {
      width: moderateScale(45),
      height: moderateScale(45),
      borderRadius: moderateScale(50),
    },
    username: {
      fontSize: moderateScale(16),
      fontWeight: 'semibold',
      color: colors.foreground,
      marginLeft: moderateScale(6),
    },
    fullName: {
      fontSize: moderateScale(14),
      color: colors.secondary,
      marginLeft: moderateScale(6),
    },
  });
