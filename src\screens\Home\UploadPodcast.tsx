import { FC, useLayoutEffect, useMemo, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Toast from 'react-native-toast-message';
import YoutubePlayer from 'react-native-youtube-iframe';
import { yupResolver } from '@hookform/resolvers/yup';
import { ParamListBase } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import CustomButton from '../../components/common/button';
import Input from '../../components/common/input';
import CustomText from '../../components/common/text';
import { OfflineView } from '../../components/OfflineView';
import { ColorScheme } from '../../constants/theme/colors';
import { TOAST_TITLE, TOAST_TYPE } from '../../constants/toast';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import { Header } from '../../navigation/components/Header';
import { createPost } from '../../redux/slices/postsSlice';
import { fetchYTVideoDetails } from '../../services/api/postsService';
import { YTVideoContent } from '../../types/posts';
import { APP_ROUTES } from '../../types/routes';
import { YT_URL_PATTERN } from '../../utils/posts';
import { uploadPodcastValidationSchema } from '../../utils/validationSchema';

export interface IUploadPodcastFields {
  url: string;
}

const INITIAL_VALUES: IUploadPodcastFields = {
  url: '',
};

enum EHeaderTitle {
  UPLOAD_PODCAST = 'Upload Podcast',
  PODCAST_PREVIEW = 'Podcast Preview',
}

interface UploadPodcastProps {
  navigation: StackNavigationProp<ParamListBase, string, undefined>;
}

const UploadPodcast: FC<UploadPodcastProps> = ({ navigation }) => {
  const { isConnected } = useConnectionStatus();

  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const [isLoading, setIsLoading] = useState(false);
  const [headerTitle, setHeaderTitle] = useState<EHeaderTitle>(
    EHeaderTitle.UPLOAD_PODCAST,
  );
  const [videoContent, setVideoContent] = useState<YTVideoContent | undefined>(
    undefined,
  );
  const { userDetails } = useAppSelector(state => state.auth);
  const styles = useMemo(
    () => createStyles(colors, headerTitle),
    [colors, headerTitle],
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      header: () => (
        <Header
          iconName="close"
          routeName={headerTitle}
          navigation={navigation}
          colors={colors}
        />
      ),
    });
  }, [navigation, headerTitle]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<IUploadPodcastFields>({
    mode: 'onSubmit',
    resolver: yupResolver(uploadPodcastValidationSchema),
    defaultValues: INITIAL_VALUES,
  });

  const extractYouTubeVideoId = (url: string): string | null => {
    const match = url.match(YT_URL_PATTERN);

    return match && match[2].length === 11 ? match[2] : null;
  };

  const [videoId, setVideoId] = useState<string | undefined>(undefined);

  const onSubmit: SubmitHandler<IUploadPodcastFields> = async data => {
    setIsLoading(true);
    const id = extractYouTubeVideoId(data.url);
    if (id) {
      setVideoId(id);
      const content: YTVideoContent = await fetchYTVideoDetails({
        src: data.url,
      });
      setVideoContent(content);
      setHeaderTitle(EHeaderTitle.PODCAST_PREVIEW);
    } else {
      console.error('Invalid YouTube URL');
    }
    setIsLoading(false);
  };

  const handleImportEpisode = async () => {
    if (!Number(userDetails?.verified)) {
      Toast.show({
        type: TOAST_TYPE.ERROR,
        text1: TOAST_TITLE.FAILED_TO_POST,
        text2: 'Please verify your account to post.',
      });

      return;
    }

    if (videoContent?.url) {
      await dispatch(createPost(videoContent?.url));

      navigation.navigate(APP_ROUTES.HOME);
    }
  };

  if (!isConnected) return <OfflineView />;

  return (
    <View style={styles.container}>
      {headerTitle === EHeaderTitle.UPLOAD_PODCAST ? (
        <View style={styles.subContainer}>
          <View style={styles.fieldContainer}>
            <CustomText style={styles.label}>
              Enter the link of the video you want to import.
            </CustomText>
            <Controller
              control={control}
              name="url"
              render={({ field: { onChange, value, name } }) => (
                <Input
                  name={name}
                  value={value}
                  placeholder="Enter YouTube URL"
                  onChangeText={_value => onChange(_value)}
                  errors={errors}
                  autoCapitalize="none"
                />
              )}
            />
          </View>
          <CustomButton
            loading={isLoading}
            style={styles.button}
            textSyle={styles.buttonText}
            title="Fetch your Episode"
            onPress={handleSubmit(onSubmit)}
          />
        </View>
      ) : (
        <View style={styles.subContainer}>
          <CustomText style={styles.title}>{videoContent?.title}</CustomText>
          <View style={styles.bgCover}>
            <YoutubePlayer
              height={verticalScale(300)}
              webViewStyle={styles.webView}
              videoId={videoId}
            />
          </View>
          <CustomButton
            loading={isLoading}
            style={styles.button}
            textSyle={styles.buttonText}
            title="Import Episode"
            onPress={handleImportEpisode}
          />
        </View>
      )}
    </View>
  );
};

export default UploadPodcast;

const createStyles = (colors: ColorScheme, headerTitle: EHeaderTitle) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingVertical: verticalScale(20),
      paddingHorizontal: moderateScale(20),
      justifyContent: 'center',
    },
    subContainer: { marginBottom: verticalScale(120) },
    fieldContainer: {
      gap: verticalScale(6),
    },
    label: {
      color: colors.foreground,
      fontSize: moderateScale(14),
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(19),
      fontWeight: '600',
      lineHeight: 24,
    },
    button: {
      paddingVertical: verticalScale(12),
      marginTop:
        headerTitle === EHeaderTitle.UPLOAD_PODCAST
          ? verticalScale(16)
          : verticalScale(26),
    },
    buttonText: { fontSize: moderateScale(16), fontWeight: '600' },
    bgCover: {
      width: '100%',
      height: verticalScale(155),
      borderRadius: moderateScale(10),
      overflow: 'hidden',
      marginTop: verticalScale(14),
    },
    webView: { borderRadius: moderateScale(10) },
  });
