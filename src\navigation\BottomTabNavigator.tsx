/* eslint-disable indent */
import { Fragment, useMemo } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from '@react-navigation/bottom-tabs';
import { RouteProp } from '@react-navigation/native';

import PostDetailModal from '../components/posts/PostDetailModal';
import { isGooglePixel } from '../constants/common';
import { ColorScheme } from '../constants/theme/colors';
import { useAppSelector } from '../hooks/redux';
import { Charts } from '../screens/Charts';
import Explore from '../screens/Explore/Index';
import HomeFeed from '../screens/Home/HomeFeed';
import Saved from '../screens/Saved';
import { APP_ROUTES, AppStackNavigatorParamList } from '../types/routes';

import { Header } from './components/Header';
import { RenderTabBarIcons } from './components/RenderTabBarIcons';
import { ProfileStackNavigator } from './ProfileStackNavigator';

const Tab = createBottomTabNavigator<AppStackNavigatorParamList>();
const { height } = Dimensions.get('window');

const hapticFeedbackOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

export const BottomTabNavigator = () => {
  const { colors } = useAppSelector(state => state.theme);
  const { showMiniPlayer } = useAppSelector(state => state.miniPlayer);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const forGoogleDevices = showMiniPlayer ? height / 11 : height / 12;
  const forSamsungDevices = showMiniPlayer ? height / 13 : height / 15;

  const screenOptions:
    | BottomTabNavigationOptions
    | ((props: {
        route: RouteProp<
          AppStackNavigatorParamList,
          keyof AppStackNavigatorParamList
        >;
      }) => BottomTabNavigationOptions)
    | undefined = ({ route }) => ({
    headerShown: route.name !== APP_ROUTES.PROFILE,
    tabBarIcon: ({ focused }) => RenderTabBarIcons({ route, focused }),
    // eslint-disable-next-line react/no-unstable-nested-components
    tabBarBackground: () => <View style={styles.tabBarBackground} />,
    // eslint-disable-next-line react/no-unstable-nested-components
    header: ({ navigation, route }) => (
      <Header
        showLogo={route.name !== APP_ROUTES.CHARTS}
        routeName={route.name === APP_ROUTES.CHARTS ? 'Top Charts' : route.name}
        colors={colors}
        navigation={navigation}
      />
    ),
    tabBarStyle: {
      paddingBottom: verticalScale(isGooglePixel ? 14 : 4),
      height: isGooglePixel ? forGoogleDevices : forSamsungDevices,
      shadowOffset: {
        height: 0,
        width: 0,
      },
      shadowColor: 'transparent',
      borderTopWidth: 0,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(10),
    },
    tabBarItemStyle: {
      height: verticalScale(35),
      alignSelf: 'flex-end',
    },
    headerBackgroundContainerStyle: {
      borderTopWidth: 0,
    },
    tabBarShowLabel: false,
  });

  return (
    <Fragment>
      <Tab.Navigator
        initialRouteName={APP_ROUTES.HOME}
        screenOptions={screenOptions}
        screenListeners={() => ({
          tabPress: () => {
            ReactNativeHapticFeedback.trigger(
              'impactLight',
              hapticFeedbackOptions,
            );
          },
        })}>
        <Tab.Screen name={APP_ROUTES.EXPLORE} component={Explore} />
        <Tab.Screen name={APP_ROUTES.SAVED} component={Saved} />
        <Tab.Screen name={APP_ROUTES.HOME} component={HomeFeed} />
        <Tab.Screen name={APP_ROUTES.CHARTS} component={Charts} />
        <Tab.Screen
          name={APP_ROUTES.PROFILE}
          component={ProfileStackNavigator}
        />
      </Tab.Navigator>
      {showMiniPlayer ? <PostDetailModal /> : null}
    </Fragment>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    tabBarBackground: {
      backgroundColor: colors.background,
    },
  });
