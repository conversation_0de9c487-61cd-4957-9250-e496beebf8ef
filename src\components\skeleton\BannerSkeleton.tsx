import React, { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

const BannerSkeleton = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      {Array.from({ length: 7 }).map((_, index) => (
        <SkeletonPlaceholder
          key={index}
          backgroundColor={colors.surface}
          highlightColor={colors.highlightSurface}>
          <SkeletonPlaceholder.Item
            width="100%"
            height={verticalScale(80)}
            borderRadius={10}
            marginBottom={verticalScale(10)}
          />
        </SkeletonPlaceholder>
      ))}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: moderateScale(16),
      backgroundColor: colors.background,
    },
  });

export default BannerSkeleton;
