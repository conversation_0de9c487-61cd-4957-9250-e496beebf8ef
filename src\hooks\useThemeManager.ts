import { useCallback } from 'react';
import { useColorScheme } from 'react-native';

import {
  resetToSystemTheme,
  setTheme as setThemeAction,
} from '../redux/slices/themeSlice';
import { getItemFromAS, setItemToAS, STORAGE_KEYS } from '../utils/storage';

import { useAppDispatch } from './redux';

export const useThemeManager = () => {
  const dispatch = useAppDispatch();
  const systemTheme = useColorScheme() ?? 'light';

  const loadTheme = useCallback(async () => {
    const storedTheme = await getItemFromAS(STORAGE_KEYS.THEME);
    if (storedTheme) {
      dispatch(setThemeAction(storedTheme as 'light' | 'dark'));
    } else {
      dispatch(resetToSystemTheme(systemTheme));
    }
  }, [dispatch, systemTheme]);

  const setTheme = (newTheme: 'light' | 'dark') => {
    dispatch(setThemeAction(newTheme));
    setItemToAS(STORAGE_KEYS.THEME, newTheme);
  };

  return {
    setTheme,
    loadTheme,
  };
};
