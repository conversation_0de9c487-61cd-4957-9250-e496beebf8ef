import { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';

import { ColorScheme } from '../constants/theme/colors';
import { useAppSelector } from '../hooks/redux';

import CustomText from './common/text';

export const NotificationBadge: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { unreadNotificationsCount } = useAppSelector(
    state => state.notifications,
  );

  if (unreadNotificationsCount === 0) return null;

  return (
    <View style={styles.container}>
      <CustomText style={styles.text}>
        {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
      </CustomText>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      top: -4,
      right: -10,
    },
    text: {
      backgroundColor: colors.accent,
      color: colors.white,
      fontSize: moderateScale(8),
      textAlign: 'center',
      borderRadius: moderateScale(8),
      overflow: 'hidden',
      width: moderateScale(22),
      paddingVertical: moderateScale(2),
      justifyContent: 'center',
      alignItems: 'center',
    },
  });
