import * as React from 'react';
import Svg, { Mask, Path, SvgProps } from 'react-native-svg';

const SportsIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Mask
      id="a"
      width={23}
      height={20}
      x={-0.5}
      y={-0.24}
      fill="#000"
      maskUnits="userSpaceOnUse">
      <Path fill="#fff" d="M-.5-.24h23v20h-23z" />
      <Path
        fillRule="evenodd"
        d="M7.443.76C3.611.76.5 3.914.5 7.799c0 3.884 3.111 7.038 6.943 7.038.518 0 1.022-.058 1.507-.166-.132 1.022-.064 2.025.211 2.942l.002.005a2.058 2.058 0 0 0 1.322 1.34c.002 0 .003 0 .005.002 2.562.79 5.812-.103 8.222-2.547 2.41-2.443 3.29-5.738 2.512-8.335v-.006a2.062 2.062 0 0 0-1.323-1.34l-.006-.002c-1.688-.52-3.676-.31-5.525.594C14.129 3.66 11.117.76 7.443.76Zm2.185 14.362 3.319 3.364a5.978 5.978 0 0 1-2.243-.227 1.329 1.329 0 0 1-.851-.863 6.218 6.218 0 0 1-.225-2.274Zm6.916-7.787c-1.528.324-3.072 1.157-4.358 2.461-1.278 1.294-2.096 2.845-2.42 4.383 0 .013-.002.027-.004.04l4.076 4.131c0 .***************.005 1.528-.323 3.073-1.157 4.36-2.462 1.285-1.303 2.107-2.87 2.427-4.419l-.005-.003-4.075-4.132-.004-.004Zm.581 3.03-.71.721-.285-.29a.36.36 0 0 0-.513 0 .371.371 0 0 0 0 .52l.286.29-.71.72-.286-.29a.358.358 0 0 0-.512 0 .37.37 0 0 0 0 .519l.286.29-.711.72-.286-.29a.36.36 0 0 0-.512 0 .37.37 0 0 0 0 .518l.286.291-.71.72-.286-.29a.36.36 0 0 0-.512 0 .371.371 0 0 0 0 .52l1.084 1.099c.141.143.37.143.511 0a.37.37 0 0 0 0-.519l-.286-.29.71-.72.288.29c.141.143.37.143.511 0a.371.371 0 0 0 0-.52l-.286-.29.71-.72.287.29a.36.36 0 0 0 .512 0 .371.371 0 0 0 0-.52l-.286-.29.71-.72.287.29c.141.143.37.143.511 0a.37.37 0 0 0 0-.518l-.286-.29.71-.72.288.29c.141.142.37.142.511 0a.371.371 0 0 0 0-.52l-1.084-1.1a.36.36 0 0 0-.512 0 .37.37 0 0 0 0 .519l.285.29Zm-8.045-2.2H7.805v5.928a6.15 6.15 0 0 0 1.288-.215 9.215 9.215 0 0 1 .97-2.474 7.076 7.076 0 0 1-.983-3.238ZM7.08 14.094V8.166H5.806a7.058 7.058 0 0 1-2.105 4.667 6.138 6.138 0 0 0 3.38 1.26Zm-2-5.927H1.235a6.327 6.327 0 0 0 1.91 4.188 6.33 6.33 0 0 0 1.936-4.188Zm7.887 0H9.805c.055.91.3 1.767.697 2.532a10.226 10.226 0 0 1 2.466-2.532Zm7.79 2.402-3.32-3.364a5.978 5.978 0 0 1 2.244.228c.404.132.72.452.85.863a6.23 6.23 0 0 1 .225 2.273ZM3.17 3.219a6.32 6.32 0 0 0-1.935 4.213h3.848A6.322 6.322 0 0 0 3.17 3.219Zm3.91-1.715a6.134 6.134 0 0 0-3.352 1.24 7.069 7.069 0 0 1 2.08 4.688h1.273V1.504Zm.725 0v5.928h1.272a7.067 7.067 0 0 1 2.081-4.688 6.133 6.133 0 0 0-3.353-1.24Zm3.91 1.715a6.322 6.322 0 0 0-1.912 4.213h3.848a6.32 6.32 0 0 0-1.935-4.213Z"
        clipRule="evenodd"
      />
    </Mask>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M7.443.76C3.611.76.5 3.914.5 7.799c0 3.884 3.111 7.038 6.943 7.038.518 0 1.022-.058 1.507-.166-.132 1.022-.064 2.025.211 2.942l.002.005a2.058 2.058 0 0 0 1.322 1.34c.002 0 .003 0 .005.002 2.562.79 5.812-.103 8.222-2.547 2.41-2.443 3.29-5.738 2.512-8.335v-.006a2.062 2.062 0 0 0-1.323-1.34l-.006-.002c-1.688-.52-3.676-.31-5.525.594C14.129 3.66 11.117.76 7.443.76Zm2.185 14.362 3.319 3.364a5.978 5.978 0 0 1-2.243-.227 1.329 1.329 0 0 1-.851-.863 6.218 6.218 0 0 1-.225-2.274Zm6.916-7.787c-1.528.324-3.072 1.157-4.358 2.461-1.278 1.294-2.096 2.845-2.42 4.383 0 .013-.002.027-.004.04l4.076 4.131c0 .***************.005 1.528-.323 3.073-1.157 4.36-2.462 1.285-1.303 2.107-2.87 2.427-4.419l-.005-.003-4.075-4.132-.004-.004Zm.581 3.03-.71.721-.285-.29a.36.36 0 0 0-.513 0 .371.371 0 0 0 0 .52l.286.29-.71.72-.286-.29a.358.358 0 0 0-.512 0 .37.37 0 0 0 0 .519l.286.29-.711.72-.286-.29a.36.36 0 0 0-.512 0 .37.37 0 0 0 0 .518l.286.291-.71.72-.286-.29a.36.36 0 0 0-.512 0 .371.371 0 0 0 0 .52l1.084 1.099c.141.143.37.143.511 0a.37.37 0 0 0 0-.519l-.286-.29.71-.72.288.29c.141.143.37.143.511 0a.371.371 0 0 0 0-.52l-.286-.29.71-.72.287.29a.36.36 0 0 0 .512 0 .371.371 0 0 0 0-.52l-.286-.29.71-.72.287.29c.141.143.37.143.511 0a.37.37 0 0 0 0-.518l-.286-.29.71-.72.288.29c.141.142.37.142.511 0a.371.371 0 0 0 0-.52l-1.084-1.1a.36.36 0 0 0-.512 0 .37.37 0 0 0 0 .519l.285.29Zm-8.045-2.2H7.805v5.928a6.15 6.15 0 0 0 1.288-.215 9.215 9.215 0 0 1 .97-2.474 7.076 7.076 0 0 1-.983-3.238ZM7.08 14.094V8.166H5.806a7.058 7.058 0 0 1-2.105 4.667 6.138 6.138 0 0 0 3.38 1.26Zm-2-5.927H1.235a6.327 6.327 0 0 0 1.91 4.188 6.33 6.33 0 0 0 1.936-4.188Zm7.887 0H9.805c.055.91.3 1.767.697 2.532a10.226 10.226 0 0 1 2.466-2.532Zm7.79 2.402-3.32-3.364a5.978 5.978 0 0 1 2.244.228c.404.132.72.452.85.863a6.23 6.23 0 0 1 .225 2.273ZM3.17 3.219a6.32 6.32 0 0 0-1.935 4.213h3.848A6.322 6.322 0 0 0 3.17 3.219Zm3.91-1.715a6.134 6.134 0 0 0-3.352 1.24 7.069 7.069 0 0 1 2.08 4.688h1.273V1.504Zm.725 0v5.928h1.272a7.067 7.067 0 0 1 2.081-4.688 6.133 6.133 0 0 0-3.353-1.24Zm3.91 1.715a6.322 6.322 0 0 0-1.912 4.213h3.848a6.32 6.32 0 0 0-1.935-4.213Z"
      clipRule="evenodd"
    />
    <Path
      stroke="#fff"
      strokeWidth={0.9}
      d="M7.443.76C3.611.76.5 3.914.5 7.799c0 3.884 3.111 7.038 6.943 7.038.518 0 1.022-.058 1.507-.166-.132 1.022-.064 2.025.211 2.942l.002.005a2.058 2.058 0 0 0 1.322 1.34c.002 0 .003 0 .005.002 2.562.79 5.812-.103 8.222-2.547 2.41-2.443 3.29-5.738 2.512-8.335v-.006a2.062 2.062 0 0 0-1.323-1.34l-.006-.002c-1.688-.52-3.676-.31-5.525.594C14.129 3.66 11.117.76 7.443.76Zm2.185 14.362 3.319 3.364a5.978 5.978 0 0 1-2.243-.227 1.329 1.329 0 0 1-.851-.863 6.218 6.218 0 0 1-.225-2.274Zm6.916-7.787c-1.528.324-3.072 1.157-4.358 2.461-1.278 1.294-2.096 2.845-2.42 4.383 0 .013-.002.027-.004.04l4.076 4.131c0 .***************.005 1.528-.323 3.073-1.157 4.36-2.462 1.285-1.303 2.107-2.87 2.427-4.419l-.005-.003-4.075-4.132-.004-.004Zm.581 3.03-.71.721-.285-.29a.36.36 0 0 0-.513 0 .371.371 0 0 0 0 .52l.286.29-.71.72-.286-.29a.358.358 0 0 0-.512 0 .37.37 0 0 0 0 .519l.286.29-.711.72-.286-.29a.36.36 0 0 0-.512 0 .37.37 0 0 0 0 .518l.286.291-.71.72-.286-.29a.36.36 0 0 0-.512 0 .371.371 0 0 0 0 .52l1.084 1.099c.141.143.37.143.511 0a.37.37 0 0 0 0-.519l-.286-.29.71-.72.288.29c.141.143.37.143.511 0a.371.371 0 0 0 0-.52l-.286-.29.71-.72.287.29a.36.36 0 0 0 .512 0 .371.371 0 0 0 0-.52l-.286-.29.71-.72.287.29c.141.143.37.143.511 0a.37.37 0 0 0 0-.518l-.286-.29.71-.72.288.29c.141.142.37.142.511 0a.371.371 0 0 0 0-.52l-1.084-1.1a.36.36 0 0 0-.512 0 .37.37 0 0 0 0 .519l.285.29Zm-8.045-2.2H7.805v5.928a6.15 6.15 0 0 0 1.288-.215 9.215 9.215 0 0 1 .97-2.474 7.076 7.076 0 0 1-.983-3.238ZM7.08 14.094V8.166H5.806a7.058 7.058 0 0 1-2.105 4.667 6.138 6.138 0 0 0 3.38 1.26Zm-2-5.927H1.235a6.327 6.327 0 0 0 1.91 4.188 6.33 6.33 0 0 0 1.936-4.188Zm7.887 0H9.805c.055.91.3 1.767.697 2.532a10.226 10.226 0 0 1 2.466-2.532Zm7.79 2.402-3.32-3.364a5.978 5.978 0 0 1 2.244.228c.404.132.72.452.85.863a6.23 6.23 0 0 1 .225 2.273ZM3.17 3.219a6.32 6.32 0 0 0-1.935 4.213h3.848A6.322 6.322 0 0 0 3.17 3.219Zm3.91-1.715a6.134 6.134 0 0 0-3.352 1.24 7.069 7.069 0 0 1 2.08 4.688h1.273V1.504Zm.725 0v5.928h1.272a7.067 7.067 0 0 1 2.081-4.688 6.133 6.133 0 0 0-3.353-1.24Zm3.91 1.715a6.322 6.322 0 0 0-1.912 4.213h3.848a6.32 6.32 0 0 0-1.935-4.213Z"
      clipRule="evenodd"
      mask="url(#a)"
    />
  </Svg>
);
export default SportsIcon;
