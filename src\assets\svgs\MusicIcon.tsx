import * as React from 'react';
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const MusicIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      clipPath="url(#a)">
      <Path d="M2.188 1.125h14.624s1.125 0 1.125 1.125v13.5s0 1.125-1.125 1.125H2.188s-1.124 0-1.124-1.125V2.25s0-1.125 1.125-1.125ZM4.063 1.125v15.75" />
      <Path d="M6.493 11.535a1.41 1.41 0 1 0 2.82 0 1.41 1.41 0 0 0-2.82 0ZM12.125 9.848a1.411 1.411 0 1 0 2.822 0 1.411 1.411 0 0 0-2.822 0Z" />
      <Path d="M9.313 11.535V6.562a1.11 1.11 0 0 1 .75-1.065l3.375-.937a1.11 1.11 0 0 1 1.353.552c.081.159.123.334.124.513v4.222" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path d="M.5 0h18v18H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
