import * as React from 'react';
import Svg, { G, <PERSON>, SvgProps } from 'react-native-svg';

interface PodRankingIconProps extends SvgProps {
  isFocused?: boolean;
}

export const PodRankingIcon: React.FC<PodRankingIconProps> = props => {
  return (
    <Svg id="Layer_1" x="0px" y="0px" viewBox="0 0 1059 800" {...props}>
      <G>
        <G>
          {props?.isFocused ? (
            <>
              <Path d="M771,117.6c0,112.3,0,224.7-0.2,337c0,6.5-0.4,13-1.4,19.4c-3.3,19.5-17.3,32.7-37.6,33.8c-19.1,1-38.3,1.1-57.3,0 c-23.3-1.4-37.2-17.4-38.5-42.3c-0.5-9.1-0.5-18.3-0.5-27.5c0-52.6,0-105.2,0-157.8c0,0,0,0,0,0c0-54.9-0.1-109.8,0.1-164.8 c0-8.8,0.4-17.7,1.4-26.4c2.4-20,16.5-34.3,36.6-35.3c19.9-1,40-1.1,59.8,0c22.5,1.3,36.1,17.8,37.4,42.9 C771,103.6,771,110.6,771,117.6z" />
              <Path d="M598.2,446.8c0,9-0.3,18-1.6,26.9c-2.9,19.6-17.3,33.1-37.8,34.2c-18.9,1-37.9,1.1-56.8,0c-23.8-1.4-38-17.9-38.9-43.6 c-0.5-14.3-0.4-28.6-0.5-42.9c-0.1-34.6,0-69.2,0-103.9h0.4c0-48.1-0.1-96.2,0.1-144.3c0-4.8,0.7-9.7,1.8-14.3 c4.1-17.9,17.4-30.2,35.7-31.1c19.9-0.9,40-1.1,59.8,0c22.2,1.2,36.2,18.1,37.5,42.9c0.4,8.1,0.5,16.3,0.5,24.5 C598.3,279,598.3,362.9,598.2,446.8z" />
              <Path d="M425.1,367c0,32,0.1,63.9-0.1,95.8c0,5.4-0.9,11-2.4,16.2c-4.9,17-17,26.6-34.2,28.5c-21.8,2.4-43.9,3.2-65.6-0.9 c-17.8-3.3-29.1-16.1-31.6-34.1c-0.6-4.4-1.1-8.9-1.1-13.4c0-62.4,0-124.8,0.1-187.2c0-5.6,0.8-11.4,2.3-16.7 c4.2-15.4,14.7-25.1,30.2-27.8c22.6-4,45.5-4,68.1-0.3c22.1,3.6,33.8,18.7,34.2,43C425.4,302.4,425,334.7,425.1,367L425.1,367z" />
            </>
          ) : (
            <>
              <Path d="M635.3,280.2c0-54.9-0.1-109.8,0.1-164.7c0-8.8,0.4-17.7,1.4-26.4c2.4-20,16.5-34.3,36.6-35.3c19.9-1,39.9-1.1,59.8,0 c22.5,1.3,36.1,17.8,37.4,42.9c0.4,7,0.4,14,0.4,21c0,112.3,0,224.7-0.2,337c0,6.5-0.4,13-1.4,19.4c-3.3,19.5-17.3,32.7-37.6,33.8 c-19.1,1-38.3,1.1-57.3,0c-23.3-1.4-37.2-17.5-38.5-42.3c-0.5-9.1-0.5-18.3-0.5-27.4C635.3,385.4,635.3,332.8,635.3,280.2 C635.3,280.2,635.3,280.2,635.3,280.2z M672.3,280.6C672.4,280.6,672.4,280.6,672.3,280.6c0.1,55.2,0,110.5,0.1,165.7 c0,6.3,0.3,12.6,0.6,19c0.1,3.6,1.6,5.9,5.7,5.9c13.1,0.2,26.3,1,39.4,0.6c16.7-0.5,15,0.6,15.7-15.9c0.2-3.8,0.1-7.7,0.1-11.5 c0-57.6,0-115.1,0-172.7c0-53.4,0-106.8-0.1-160.2c0-5.2-0.3-10.3-0.5-15.5c-0.1-3.6-1.6-5.8-5.7-5.8c-16.3,0.1-32.6,0.1-48.8,0 c-4.4,0-5.8,2.4-5.9,6.2c-0.2,6.5-0.6,13-0.6,19.5C672.3,170.8,672.3,225.7,672.3,280.6z" />
              <Path d="M462.9,317.4c0-48.1-0.1-96.2,0.1-144.3c0-4.8,0.7-9.7,1.8-14.3c4.1-17.9,17.4-30.2,35.7-31.1c19.9-1,40-1.1,59.8,0 c22.2,1.3,36.2,18.1,37.5,42.9c0.4,8.1,0.5,16.3,0.5,24.5c0,83.9,0.1,167.8-0.1,251.7c0,9-0.3,18-1.6,26.9 c-2.9,19.6-17.3,33.1-37.8,34.2c-18.9,1-37.9,1.1-56.8,0c-23.8-1.4-38-17.9-38.9-43.6c-0.5-14.3-0.4-28.6-0.5-42.9 c-0.1-34.6,0-69.2,0-103.9C462.7,317.4,462.8,317.4,462.9,317.4z M561,318.3C561.1,318.3,561.1,318.3,561,318.3 c0.1-44.1,0.1-88.2,0.1-132.2c0-5.6-0.4-11.3-0.7-16.9c-0.2-3.1-1.8-4.8-5-4.8c-15.8-0.1-31.6-0.6-47.3-0.2 c-7.5,0.2-7.8,1.1-8,8.9c-0.3,9.5-0.3,19-0.3,28.4c0,82.3,0,164.7,0,247c0,5.7,0.3,11.3,0.5,17c0.2,3.6,1.6,5.9,5.7,5.9 c13.3,0.2,26.6,1,39.9,0.6c16.2-0.5,15,0.4,15.1-15.3C561.2,410.4,561,364.3,561,318.3z" />
              <Path d="M425.1,367c0,31.9,0.1,63.9-0.1,95.8c0,5.4-0.9,11-2.4,16.2c-4.9,17.1-17,26.6-34.2,28.5c-21.8,2.4-43.9,3.2-65.6-0.9 c-17.8-3.3-29.1-16.1-31.6-34.1c-0.6-4.4-1.1-8.9-1.1-13.4c0-62.4,0-124.8,0.1-187.2c0-5.6,0.8-11.4,2.3-16.7 c4.2-15.4,14.7-25.1,30.2-27.8c22.6-4,45.5-4,68.1-0.3c22.1,3.6,33.8,18.7,34.2,43C425.4,302.5,425,334.7,425.1,367 C425,367,425.1,367,425.1,367z M388.1,367.3c0.1,0,0.2,0,0.3,0c0-23.8,0-47.6,0-71.4c0-8.8-0.3-17.6-0.5-26.4 c-0.1-4-1.6-6.4-6.2-6.5c-13.1-0.1-26.3-1-39.4-0.5c-16.5,0.6-15.2-0.6-15.2,15.2c-0.3,60.2-0.1,120.4-0.1,180.7 c0,2.5,0.3,5,0.4,7.5c0.2,3.5,1.7,5.5,5.6,5.5c14.5,0.1,28.9,0.6,43.4,0.5c11-0.1,11.6-0.7,11.7-11.7 C388.2,429.1,388.1,398.2,388.1,367.3z" />
            </>
          )}
        </G>
        <G>
          <Path d="M107.6,745.3H84.1c0-92.4,0-140.8,0.1-145.3h33.7c2.1,0,4.4,0.2,7.2,0.5c15.4,2.1,23,15.5,23,40v4.7 c0,9.3-1.1,17.7-3.4,25.3c-2.8,9.9-9.1,15.7-18.9,17.6c-3.4,0.5-6.7,0.8-10,0.8c-1.5,0-3-0.1-4.5-0.2h-3.8V745.3z M111.8,669.1 c1.5,0,3-0.1,4.8-0.3c3.2-0.3,5.4-2.3,6.5-6c1.1-3.6,1.6-9.4,1.6-17.4c0-8.5-0.4-14.8-1.2-18.9c-0.9-4.5-3-7.1-6.6-7.8 c-1.5-0.3-3.1-0.4-5.1-0.4l-4.2,0.1V669L111.8,669.1z" />
          <Path d="M194.3,746.9c-20.3,0-31.1-11.9-32.6-35.7c-0.2-3.8-0.3-7.6-0.3-11.4l0.1-5.6c0-30.7,0-47.9-0.1-51.7 c0-3.7,0.1-7.5,0.4-11.2c1.9-22.1,12.8-33.1,32.8-33.1c11,0.1,19,3.3,24,9.6c4.6,5.8,7.3,14.2,7.8,25.2c0.1,3.6,0.2,7.3,0.2,10.8 v6.1c0,30.1,0,46.9,0,50.4c0,4.6-0.2,9.3-0.5,14c-1.1,10.3-3.9,18.1-8.4,23.3C212.4,743.9,204.6,746.9,194.3,746.9z M193.9,726.4 c0.7,0,1.4-0.1,2.2-0.2c3.6-0.4,5.7-4,6.5-10.7c0.3-3.2,0.5-6.3,0.5-9.3v-69.8c0-2.1-0.1-4.4-0.3-6.8c-0.5-5-1.8-8.1-4.1-9.4 c-1.4-0.8-2.8-1.2-4.4-1.2c-1.8,0-3.4,0.5-4.9,1.6c-1.9,1.7-3.1,4.1-3.6,7.3c-0.5,3.1-0.7,6-0.7,8.6c0,44,0,67.1-0.1,69.3 c0,4.8,0.5,9.4,1.4,13.9C187.4,724.1,189.9,726.4,193.9,726.4z" />
          <Path d="M310.1,682c0,2.3,0,4.6,0,6.9c0,0.3,0,0.5,0,0.8c0.2,6.4,0.1,12.8-0.2,19.4c-0.1,4.4-0.7,9-1.8,13.9 c-1.1,4.9-3.1,9.3-6.1,13.2c-5.1,6.2-13,9.3-23.8,9.3c-3.7,0-7.4,0-11,0c-3,0-6,0-8.9,0c-0.6,0-1.2,0-1.7,0c-2.4,0-4.8,0-7.2,0 h-3.3V606.7c0-0.6,0-1.2,0.1-1.8c0-0.1,0-0.1,0-0.2c0-0.5,0-1,0-1.5V600h18.5c1.7,0,3.5,0,5.4,0c4.8-0.2,9.6-0.1,14.5,0.3 c7.8,0.9,13.9,4,18.2,9.3l0.1,0.1c3.5,5.1,5.6,10.6,6.4,16.4c0.8,5.8,1,11.5,0.8,17.1c0,0.7,0,1.5,0,2.3L310.1,682z M284.2,722.9 c1.2-2,1.9-4.2,2.1-6.8c0.2-2.5,0.3-4.9,0.2-7.1c0-0.9,0-1.7,0-2.6c0-0.5,0-0.9,0-1.4c0-0.4,0-0.8,0-1.3v-24c0-1.6,0-3.2,0-4.8 c0-3.2,0-6.4,0-9.5c0-8.9,0-18,0-27.1c0.1-3-0.1-6.2-0.6-9.6c-0.5-3.4-1.8-6.1-3.9-8c-1.5-1.1-3.1-1.8-5-2 c-1.9-0.2-3.9-0.3-6.1-0.2c-0.5,0-0.9,0-1.4,0v108.2c0.5,0,1,0,1.5,0c1.5,0.1,3,0.1,4.6,0c3.7,0,6.5-1.2,8.3-3.6L284.2,722.9z" />
          <Path d="M425.4,745.3h-23.3c-0.5-7.2-0.7-23-0.7-47.3v-4.3c0-7.9-1.1-12.6-3.3-14.1c-1.4-1-3.1-1.6-5.2-1.8l-8.4-0.4v67.9H361 c0-90.5,0.1-139,0.2-145.3h35.4c2.6,0,5.5,0.2,8.5,0.5c3,0.4,6.1,1.5,9,3.3c4.5,3,7.5,7.9,8.8,14.5c1.2,6.6,1.8,13.6,1.8,21.3 c0,5.4-0.6,10.8-1.8,16.2c-1.5,6-4.7,10.2-9.6,12.5c6.1,2.3,9.6,8.9,10.5,19.8c0.7,8.2,1.1,19.5,1.1,33.8c-0.1,6.1,0,11.4,0.2,16 C425.1,739,425.2,741.5,425.4,745.3z M391.7,658.9c2.1,0,3.9-0.3,5.5-1c1.9-1,3.1-3.5,3.7-7.6c0.4-3.8,0.6-7.5,0.6-11.2 c0-4.6-0.2-9-0.7-13.3c-0.5-3-1.4-4.9-2.8-5.8c-1.8-1.1-4.8-1.6-9-1.6l-4.3,0.1v40.4H391.7z" />
          <Path d="M485.8,719.7h-21.7l-3.1,25.6h-22.8l0.5-3.8c0.3-2.4,0.7-4.9,1-7.4c0.2-1.3,0.4-2.5,0.6-3.9c0.7-4.7,1.3-9.4,2-14.1 c0.8-5.3,1.6-10.6,2.3-15.9l13.3-93.4c0.1-0.7,0.3-1.3,0.4-2c0.1-0.7,0.2-1.4,0.3-2.1l0.4-2.9h30.3l0.4,2.8 c0.6,4.2,1.2,8.3,1.9,12.5c0.8,5.5,1.6,11,2.5,16.6c0.9,5.9,1.8,11.8,2.7,17.7l13.5,89.2c0.1,0.7,0.2,1.3,0.3,2 c0,0.2,0,0.4,0.1,0.6c0,0.1,0,0.2,0,0.3l0.5,3.7h-22l-0.4-2.9c0-0.1,0-0.2,0-0.3C487.8,734.7,486.8,727.2,485.8,719.7z  M467.4,692.9c-0.3,2.3-0.6,4.7-0.9,7.1c0,0.2,0,0.4-0.1,0.6h16.9c-0.5-3.8-1-7.6-1.5-11.4c-0.5-4-1.1-7.9-1.6-11.8l-0.7-4.9 c-0.5-4-1.1-7.9-1.6-11.8c-0.9-7.4-1.9-14.7-2.9-22.1l-0.6-4L467.4,692.9z" />
          <Path d="M594,745.3h-16.8l-5.8-16.6c-1.7-4.6-3.3-9.3-4.9-14c-10.7-30.3-17.1-48.3-19-54.1c0,3.1,0.1,6.2,0.3,9.2 c0.6,16.3,0.9,28.6,0.9,36.9c-0.1,14.3-0.2,27.2-0.2,38.6h-21.8V604.8c0.1-0.5,0.1-2.1,0.1-4.8h18.5 c15.2,43.4,24.8,70.6,28.8,81.7c-1-24-1.5-39-1.5-45.1c0.1-6,0.1-18.1,0.1-36.6h21.4L594,745.3z" />
          <Path d="M685.2,745.3h-24.8l-20.5-59.2v59.2h-23.6c0.1-85.3,0.1-133.7,0.1-145.3h23.4v57.9L659,600h24.3l-24.1,71.6 C675.3,717.3,684,741.8,685.2,745.3z" />
          <Path d="M721.4,745.3H698c0-92.1,0-140.5,0.1-145.3h23.4C721.5,689.7,721.5,738.1,721.4,745.3z" />
          <Path d="M810.5,745.3h-16.8l-5.8-16.6c-1.7-4.6-3.3-9.3-4.9-14c-10.7-30.3-17.1-48.3-19-54.1c0,3.1,0.1,6.2,0.3,9.2 c0.6,16.3,0.9,28.6,0.9,36.9c-0.1,14.3-0.2,27.2-0.2,38.6h-21.8V604.8c0.1-0.5,0.1-2.1,0.1-4.8H762c15.2,43.4,24.8,70.6,28.8,81.7 c-1-24-1.5-39-1.5-45.1c0.1-6,0.1-18.1,0.1-36.6h21.4L810.5,745.3z" />
          <Path d="M834.3,728.8c-1.8-4.6-2.9-9.2-3.4-13.9c-0.5-4.7-0.7-9.1-0.7-13.3v-34.3c0-2.8,0-5.7-0.1-8.5c0-0.3,0-0.7,0-1 c-0.1-6.9,0-13.9,0.1-21c0.4-14.4,4-24.7,10.9-31.1c3.8-3.6,8.8-5.9,15-6.9c6.1-1,12-0.8,17.7,0.5c5.4,1.3,9.6,3.8,12.8,7.4 c1.8,2.1,3.1,4.3,4.1,6.7c0.9,2.3,1.6,4.5,2.1,6.6c1.6,8.6,2.2,17.2,1.9,25.6c0,0.8,0,1.6,0,2.3v3.9h-22.1v-6 c0.1-3.6,0.1-7.3,0-11c0-2.4-0.1-4.8-0.4-7.3c-0.3-2.4-1-4.5-2.2-6.2c-1.3-1.5-3-2.3-5.1-2.5c-2.1-0.2-4,0.1-5.7,1 c-1.6,1-2.7,2.4-3.4,4.3c-0.7,1.9-1.1,3.8-1.3,5.9c-0.4,3.8-0.6,7.3-0.6,10.4v55c0,0.9,0,1.7,0,2.5c0,0.1,0,0.1,0,0.2 c-0.1,3.8,0,7.6,0.2,11.3c0,4.6,0.8,8.9,2.3,12.9c1,1.8,2.4,3,4.2,3.6c1.8,0.6,3.6,0.7,5.4,0.2c2-0.7,3.5-1.8,4.5-3.4 c1-1.6,1.7-3.3,2.1-5c1.1-5.7,1.6-11.2,1.4-16.5c0-3.8,0-7.7,0-11.6v-6.9h-11v-17.6h31.8v80.1h-11.2l-2.2-9.1 c-5.2,6.7-12.4,10.2-21.5,10.6c-6.4-0.1-11.9-1.8-16.3-5.2C839.5,738.5,836.4,734.2,834.3,728.8z" />
          <Path d="M943.5,747c-4.6,0-8.9-0.7-12.7-2.1c-9.4-3.3-15.2-12.3-17.4-26.8c-0.7-5.6-1-11.1-1-16.6l0.1-11h22.4v17 c0,7.9,1,13.3,3,16.4c1.3,1.8,3.2,2.7,5.8,2.7c1.1,0,2.3-0.2,3.7-0.5c1.4-0.4,2.5-1.4,3.4-3.2c1.1-3.4,1.7-7.1,1.7-10.9 c0-7.2-1.3-13.3-3.9-18.2c-2.1-4-5.3-8.2-9.6-12.7c-3.5-3.4-7-7.1-10.5-10.8c-9.7-10.1-15-20.7-15.8-31.8 c-0.1-1.7-0.2-3.4-0.2-5.1c0-4.8,0.5-9.3,1.5-13.5c2.4-10.6,8-17.2,16.7-19.8c4.1-1.2,8-1.8,11.6-1.8c3.6,0.1,6.3,0.2,8,0.5 c8.7,1.3,14.7,5.4,18,12.2c2.6,5.6,4.1,12.1,4.5,19.5c0.3,4.5,0.4,10.5,0.4,18h-21.8v-8.5l-0.1-7.4c-0.1-4-0.7-7.5-2-10.6 c-1.1-2.1-2.9-3.1-5.7-3.3c-1,0-1.8,0.1-2.3,0.2c-4.5,0.8-6.8,5-6.8,12.5c0,2.3,0.3,4.4,0.8,6.6c1,4.1,4.2,8.9,9.5,14.4 c0.1,0,0.1,0,0.1,0.1c0.9,0.9,4.9,5.2,11.9,12.8c5.6,6,9.8,12.2,12.8,18.4c3.5,7.2,5.3,15.1,5.3,23.9c0,6.6-0.6,12.5-1.9,17.8 c-1.5,6.2-3.9,11-7.1,14.2C960.9,744.5,953.4,747,943.5,747z" />
        </G>
      </G>
    </Svg>
  );
};
