import { NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

export enum APP_ROUTES {
  Dashboard = 'Dashboard',
  HOME = 'Home',
  UPLOAD_PODCAST = 'UploadPodcast',
  EXPLORE = 'Explore',
  SAVED = 'Saved',
  CHARTS = 'Charts',
  PROFILE = 'Profile',
  PROFILE_SETTINGS = 'ProfileSettings',
  FAVORITE_CHANNELS = 'FavoriteChannels',
  VIEW_PROFILE = 'ViewProfile',
  SEARCH = 'Search',
  UPDATE_PROFILE = 'UpdateProfile',
  NOTIFICATIONS = 'Notifications',
  GENERAL_SETTINGS = 'GeneralSettings',
  NOTIFICATION_SETTINGS = 'NotificationSettings',
  MANAGE_SESSIONS = 'ManageSessions',
  CHANGE_PASSWORD = 'ChangePassword',
  DELETE_ACCOUNT = 'DeleteAccount',
  OTHER_USER_PROFILE = 'OtherUserProfile',
  WEB_VIEW = 'WebView',
  BANNER = 'Banner',
  POLLS = 'Polls',
  CREATE_POLL = 'CreatePoll',
}

export enum AUTH_ROUTES {
  SPLASH = 'splash',
  SIGN_IN = 'login',
  SIGN_UP = 'signup',
}

export enum STACKS {
  APP = 'App',
  AUTH = 'Auth',
}

export type AppStackNavigatorParamList = {
  [APP_ROUTES.Dashboard]: undefined;
  [APP_ROUTES.HOME]: undefined;
  [APP_ROUTES.UPLOAD_PODCAST]: undefined;
  [APP_ROUTES.EXPLORE]: undefined;
  [APP_ROUTES.SAVED]: undefined;
  [APP_ROUTES.CHARTS]: undefined;
  [APP_ROUTES.PROFILE]: undefined;
  [APP_ROUTES.PROFILE_SETTINGS]: undefined;
  [APP_ROUTES.FAVORITE_CHANNELS]: undefined;
  [APP_ROUTES.VIEW_PROFILE]: { userId: string; otherUserProfile?: boolean };
  [APP_ROUTES.SEARCH]: undefined;
  [APP_ROUTES.UPDATE_PROFILE]: undefined;
  [APP_ROUTES.NOTIFICATIONS]: undefined;
  [APP_ROUTES.GENERAL_SETTINGS]: undefined;
  [APP_ROUTES.NOTIFICATION_SETTINGS]: undefined;
  [APP_ROUTES.MANAGE_SESSIONS]: undefined;
  [APP_ROUTES.CHANGE_PASSWORD]: undefined;
  [APP_ROUTES.DELETE_ACCOUNT]: undefined;
  [APP_ROUTES.OTHER_USER_PROFILE]: {
    userId: string;
    otherUserProfile: boolean;
  };
  [APP_ROUTES.WEB_VIEW]: { url: string };
  [APP_ROUTES.BANNER]: undefined;
  [APP_ROUTES.POLLS]: undefined;
  [APP_ROUTES.CREATE_POLL]: { isEditMode?: boolean; pollId?: string };
};

export type AuthStackNavigatorParamList = {
  [AUTH_ROUTES.SIGN_IN]: undefined;
  [AUTH_ROUTES.SIGN_UP]: undefined;
  [AUTH_ROUTES.SPLASH]: undefined;
};

export type RootStackParamList = {
  [STACKS.APP]: NavigatorScreenParams<AppStackNavigatorParamList>;
  [STACKS.AUTH]: NavigatorScreenParams<AuthStackNavigatorParamList>;
};

// If the stack needs to change, provide this type to useNavigation hook
export type NavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  keyof RootStackParamList
>;
