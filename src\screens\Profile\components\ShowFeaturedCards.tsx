import React, { FC, useMemo, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/AntDesign';
import YoutubePlayer, { PLAYER_STATES } from 'react-native-youtube-iframe';

import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import { openInAppBrowser } from '../../../utils/media';
import { YT_URL_PATTERN } from '../../../utils/posts';

export interface ShowFeaturedCardsProps {}

const extractYouTubeVideoId = (url: string): string | null => {
  const match = url.match(YT_URL_PATTERN);

  return match && match[2].length === 11 ? match[2] : null;
};

export const ShowFeaturedCards: FC<ShowFeaturedCardsProps> = ({}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { featuredCards } = useAppSelector(state => state.user);
  const [isYTVideo, setIsYTVideo] = useState({
    videoId: '',
    cardId: 0,
  });
  const [playing, setPlaying] = useState(false);

  const handleOpenUrl = (url: string, id?: number) => {
    if (!url) return;
    const isYoutube = YT_URL_PATTERN.test(url);
    if (isYoutube) {
      const match = extractYouTubeVideoId(url);
      setIsYTVideo({
        videoId: match || '',
        cardId: id || 0,
      });

      return;
    }
    openInAppBrowser(url);
  };

  const togglePlaying = (state: PLAYER_STATES) => {
    switch (state) {
      case PLAYER_STATES.PLAYING:
        setPlaying(true);
        break;
      case PLAYER_STATES.PAUSED:
        setPlaying(false);
        break;
      default:
        break;
    }
  };

  const handleClosePlayer = () => {
    setIsYTVideo({
      videoId: '',
      cardId: 0,
    });
  };

  return (
    <View style={styles.container}>
      {featuredCards
        .filter(item => item.enabled === 1)
        .map(({ title, image, link, id }, index) => {
          if (isYTVideo.cardId === id && isYTVideo.videoId) {
            return (
              <View key={`yt-video-${id}${index}`}>
                <View style={styles.bgCover}>
                  <YoutubePlayer
                    height={verticalScale(210)}
                    play={playing}
                    webViewStyle={styles.webView}
                    videoId={isYTVideo.videoId}
                    onChangeState={togglePlaying}
                  />
                </View>
                <View>
                  <Pressable
                    onPress={handleClosePlayer}
                    style={styles.closeButton}>
                    <Icon name="close" size={20} color={colors.foreground} />
                  </Pressable>
                </View>
              </View>
            );
          }

          return (
            <Pressable
              key={`featured-card-${id}`}
              onPress={() => handleOpenUrl(link, id)}
              style={styles.banner}>
              <View style={styles.iconWrapper}>
                <FastImage
                  source={{ uri: image }}
                  style={styles.networkImage}
                />
              </View>
              <CustomText style={styles.bannerTitle}>{title}</CustomText>
            </Pressable>
          );
        })}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      marginVertical: moderateScale(20),
      gap: moderateScale(15),
    },
    banner: {
      backgroundColor: colors.surface,
      marginHorizontal: moderateScale(14),
      borderRadius: moderateScale(8),
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(12),
      height: verticalScale(55),
      paddingHorizontal: moderateScale(12),
      shadowOffset: { width: 0, height: 1 },
      shadowColor: colors.foreground,
      shadowOpacity: 0.3,
      shadowRadius: 2,
    },
    networkImage: {
      width: moderateScale(48),
      height: moderateScale(48),
      borderRadius: moderateScale(50),
    },
    iconWrapper: { width: moderateScale(50) },
    bannerTitle: {
      color: colors.foreground,
      fontSize: moderateScale(14),
      fontWeight: '500',
    },
    webView: { borderRadius: moderateScale(10) },
    bgCover: {
      height: verticalScale(160),
      borderRadius: moderateScale(10),
      overflow: 'hidden',
      marginHorizontal: moderateScale(14),
    },
    closeButton: {
      backgroundColor: colors.surface,
      margin: 'auto',
      borderRadius: moderateScale(50),
      padding: moderateScale(10),
      marginTop: verticalScale(10),
    },
  });
