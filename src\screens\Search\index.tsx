import { FC, useEffect, useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { ParamListBase } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import CustomButton from '../../components/common/button';
import Input from '../../components/common/input';
import CustomText from '../../components/common/text';
import { OfflineView } from '../../components/OfflineView';
import { SearchSkeleton } from '../../components/skeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import { useDebounce } from '../../hooks/useDebounce';
import {
  clearRecentSearches,
  clearSearchResults,
  ESearchType,
  fetchRecentSearches,
  searchQuery,
} from '../../redux/slices/searchSlice';

import { ChannelItem, RecentSearchItem, UserItem } from './components';

interface SearchProps {
  navigation: StackNavigationProp<ParamListBase, string, undefined>;
}

const Search: FC<SearchProps> = ({ navigation }) => {
  const { isConnected } = useConnectionStatus();
  const dispatch = useAppDispatch();
  const { results, recentSearches, status } = useAppSelector(
    state => state.search,
  );
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [query, setQuery] = useState<string>('');
  const isLoading = status === 'loading';
  const debouncedQuery = useDebounce(query, 500);

  useEffect(() => {
    dispatch(fetchRecentSearches());

    return () => {
      dispatch(clearSearchResults());
      dispatch(clearRecentSearches());
    };
  }, [dispatch]);

  useEffect(() => {
    if (debouncedQuery) {
      dispatch(searchQuery(debouncedQuery));
    }
  }, [debouncedQuery, dispatch]);

  const handleSearch = (searchTerm: string) => {
    setQuery(searchTerm);
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  if (!isConnected) return <OfflineView />;

  if (status === 'failed')
    return <CustomText>Error loading results</CustomText>;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.inputContainer}>
          <Input
            name="query"
            value={query}
            placeholder="Search"
            onChangeText={_value => handleSearch(_value)}
            style={styles.input}
            autoCapitalize="none"
          />
        </View>
        <CustomButton
          onPress={handleCancel}
          variant="transparent"
          title="Cancel"
        />
      </View>
      {isLoading ? <SearchSkeleton /> : null}
      {results.length <= 0 && !isLoading ? (
        <FlatList
          data={recentSearches}
          // @ts-ignore
          keyExtractor={item => item?.key.toString()}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => (
            <RecentSearchItem
              id={item.id}
              text={item.text}
              time={item.time}
              onPress={text => {
                setQuery(text);
              }}
            />
          )}
        />
      ) : null}
      {!isLoading ? (
        <FlatList
          data={results}
          keyExtractor={item => item.key.toString()}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => {
            if (item.type === ESearchType.PUBLISHER) {
              return <UserItem {...item} />;
            } else if (item.type === ESearchType.CHANNELS) {
              return <ChannelItem {...item} />;
            }

            return null;
          }}
        />
      ) : null}
    </View>
  );
};

export default Search;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      width: '100%',
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: moderateScale(16),
      paddingVertical: verticalScale(10),
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: moderateScale(10),
    },
    input: {
      padding: moderateScale(0),
      height: moderateScale(35),
      width: '90%',
      alignSelf: 'center',
    },
    inputContainer: {
      flex: 1,
    },
    avatar: {
      width: moderateScale(45),
      height: moderateScale(45),
      borderRadius: moderateScale(5),
    },
  });
