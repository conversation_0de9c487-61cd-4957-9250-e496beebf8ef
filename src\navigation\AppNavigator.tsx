/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect, useMemo } from 'react';
import { OneSignal } from 'react-native-onesignal';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigationState } from '@react-navigation/native';
import {
  createStackNavigator,
  TransitionPresets,
} from '@react-navigation/stack';

import { ColorScheme } from '../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import useTypeSafeNavigation from '../hooks/useTypeSafeNavigation';
import { fetchUserDetails } from '../redux/slices/authSlice';
import { getNotifications } from '../redux/slices/notificationsSlice';
import { getUsersCount } from '../redux/slices/userSlice';
import BannerScreen from '../screens/Banner/BannerScreen';
import FavoriteChannels from '../screens/FavoriteChannels';
import CreatePost from '../screens/Home/UploadPodcast';
import Notifications from '../screens/Notifications';
import UpdateProfile from '../screens/Profile/UpdateProfile';
import ViewProfile from '../screens/Profile/ViewProfile';
import Search from '../screens/Search';
import PollsScreen from '../screens/Polls/PollsScreen';
import CreatePollScreen from '../screens/Polls/CreatePollScreen';
import WebViewScreen from '../screens/WebViewScreen';
import { fetchAdThreshold } from '../services/api/adService';
import { APP_ROUTES, AppStackNavigatorParamList } from '../types/routes';
import { setItemToAS, STORAGE_KEYS } from '../utils/storage';

import { Header } from './components/Header';
import { BottomTabNavigator } from './BottomTabNavigator';

const Stack = createStackNavigator<AppStackNavigatorParamList>();

const AppNavigator = () => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const typeSafeNavigation = useTypeSafeNavigation();

  const styles = useMemo(() => createStyles(colors), [colors]);
  const { user } = useAppSelector(state => state.auth);
  const { showMiniPlayer, isMiniPlayer } = useAppSelector(
    state => state.miniPlayer,
  );
  const currentRouteName = useNavigationState(state => {
    if (!state) return null;
    const route = state.routes[state.index];
    if (route.state) {
      const nestedRoute = route.state.routes[route.state?.index!];

      return nestedRoute.name;
    }

    return route.name;
  });

  useEffect(() => {
    dispatch(
      fetchUserDetails({
        data: { user_id: user?.userId as string, fetch: 'user_data' },
      }),
    );
    dispatch(getUsersCount());
  }, []);

  useEffect(() => {
    // Ensure OneSignal is initialized
    OneSignal.Notifications.addEventListener('click', event => {
      console.log('Notification clicked:', event);

      // Check if the app is in foreground/background
      if (event.notification) {
        console.log('Notification Condition Passed');
        typeSafeNavigation.navigate(APP_ROUTES.NOTIFICATIONS);
      }
    });
  }, []);

  useEffect(() => {
    dispatch(getNotifications());

    const interval = setInterval(() => {
      dispatch(getNotifications());
    }, 120000);

    return () => clearInterval(interval);
  }, []);

  const isFullScreen =
    currentRouteName === APP_ROUTES.FAVORITE_CHANNELS ||
    currentRouteName === APP_ROUTES.NOTIFICATIONS ||
    currentRouteName === APP_ROUTES.SEARCH ||
    currentRouteName === APP_ROUTES.CREATE_POLL ||
    currentRouteName === APP_ROUTES.BANNER;

  useEffect(() => {
    const fetchAdThreshHold = async () => {
      const res = await fetchAdThreshold();
      if (!res) return;
      await setItemToAS(STORAGE_KEYS.AD_THRESHOLD, res.toString());
    };

    fetchAdThreshHold();
  }, []);

  return (
    <SafeAreaView
      edges={
        (showMiniPlayer && !isMiniPlayer) || isFullScreen ? ['top'] : undefined
      }
      style={styles.safeArea}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}>
        <Stack.Screen
          name={APP_ROUTES.Dashboard}
          component={BottomTabNavigator}
        />
        <Stack.Screen
          name={APP_ROUTES.UPLOAD_PODCAST}
          component={CreatePost}
          options={({ navigation, route }) => ({
            headerShown: true,
            header: () => (
              <Header
                iconName="close"
                routeName={route.name}
                colors={colors}
                navigation={navigation}
              />
            ),
            ...TransitionPresets.ModalSlideFromBottomIOS,
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.FAVORITE_CHANNELS}
          component={FavoriteChannels}
          options={({ navigation }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName={'View All Favorites'}
                colors={colors}
                navigation={navigation}
              />
            ),
            ...TransitionPresets.SlideFromRightIOS,
          })}
        />

        <Stack.Screen
          name={APP_ROUTES.VIEW_PROFILE}
          component={ViewProfile}
          options={({ navigation }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName={'View My Profile'}
                colors={colors}
                navigation={navigation}
              />
            ),
          })}
        />

        <Stack.Screen
          name={APP_ROUTES.OTHER_USER_PROFILE}
          component={ViewProfile}
          options={({ navigation }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName={'Profile'}
                colors={colors}
                navigation={navigation}
              />
            ),
          })}
        />

        <Stack.Screen
          name={APP_ROUTES.SEARCH}
          component={Search}
          options={() => ({
            headerShown: false,
            ...TransitionPresets.ModalSlideFromBottomIOS,
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.UPDATE_PROFILE}
          component={UpdateProfile}
          options={({ navigation }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName={'Update My Profile'}
                colors={colors}
                navigation={navigation}
              />
            ),
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.NOTIFICATIONS}
          component={Notifications}
          options={({ navigation, route }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName={route.name}
                colors={colors}
                navigation={navigation}
              />
            ),
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.WEB_VIEW}
          component={WebViewScreen}
          options={({ navigation, route }) => ({
            headerShown: true,
            ...TransitionPresets.ModalSlideFromBottomIOS,
            header: () => (
              <Header
                iconName="close"
                routeName={route.params.url}
                colors={colors}
                hideNotification
                hideSearch
                navigation={navigation}
                titleType="url"
              />
            ),
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.BANNER}
          component={BannerScreen}
          options={({ navigation }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName="Banners"
                colors={colors}
                navigation={navigation}
              />
            ),
            ...TransitionPresets.SlideFromRightIOS,
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.POLLS}
          component={PollsScreen}
          options={({ navigation }) => ({
            headerShown: true,
            header: () => (
              <Header
                routeName={'Community Polls'}
                colors={colors}
                navigation={navigation}
              />
            ),
            ...TransitionPresets.SlideFromRightIOS,
          })}
        />
        <Stack.Screen
          name={APP_ROUTES.CREATE_POLL}
          component={CreatePollScreen}
          options={() => ({
            headerShown: false,
            ...TransitionPresets.ModalSlideFromBottomIOS,
          })}
        />
      </Stack.Navigator>
    </SafeAreaView>
  );
};

export default AppNavigator;

const createStyles = (colors: ColorScheme) => ({
  safeArea: {
    flex: 1,
    backgroundColor: colors.background,
  },
});
