import React, { useEffect, useMemo, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { ScrollView, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { yupResolver } from '@hookform/resolvers/yup';

import CustomButton from '../../components/common/button';
import DropdownPicker from '../../components/common/Dropdown';
import Input from '../../components/common/input';
import { GENERAL_SETTINGS_FIELDS } from '../../constants/profile';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { fetchUserDetails } from '../../redux/slices/authSlice';
import { updateUserData } from '../../services/api/userService';
import { generalSettingsValidationSchema } from '../../utils/validationSchema';

interface IGeneralSettings {
  username?: string;
  email: string;
  gender?: string;
  country_id?: string;
  verification?: string;
}

const GeneralSettings = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { userDetails, user } = useAppSelector(state => state.auth);
  const navigation = useTypeSafeNavigation();
  const dispatch = useAppDispatch();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<IGeneralSettings>({
    mode: 'onSubmit',
    resolver: yupResolver(generalSettingsValidationSchema),
    defaultValues: {
      username: userDetails?.username,
      email: userDetails?.email,
      gender: userDetails?.gender,
      country_id: userDetails?.country_id,
      verification:
        Number(userDetails?.verified) === 0 ? 'Not Verified' : 'Verified',
    },
  });

  useEffect(() => {
    dispatch(
      fetchUserDetails({
        data: { user_id: user?.userId as string, fetch: 'user_data' },
      }),
    );
  }, []);

  const onSubmit: SubmitHandler<IGeneralSettings> = async data => {
    setIsLoading(true);
    delete data.username;
    delete data.verification;
    const response = await updateUserData(data);
    if (response) {
      dispatch(
        fetchUserDetails({
          data: {
            user_id: user?.userId as string,
            fetch: 'user_data',
          },
        }),
      );
      navigation.goBack();
    }
    setIsLoading(false);
  };

  return (
    <ScrollView style={styles.container}>
      <View>
        {GENERAL_SETTINGS_FIELDS.map(field => (
          <Controller
            key={field.name}
            control={control}
            name={field.name as keyof IGeneralSettings}
            render={({ field: { onChange, value } }) =>
              field.isDropdown ? (
                <DropdownPicker
                  label={field.label}
                  data={field.data}
                  placeholder={field.placeholder}
                  value={value}
                  onChange={onChange}
                />
              ) : (
                <Input
                  name={field.name}
                  label={field.label}
                  value={value as keyof IGeneralSettings}
                  placeholder={field.placeholder}
                  onChangeText={onChange}
                  errors={errors}
                  autoCapitalize="none"
                  disabled={field.isDisabled}
                />
              )
            }
          />
        ))}
        <View style={styles.btnWrapper}>
          <CustomButton
            title="Save"
            onPress={handleSubmit(onSubmit)}
            loading={isLoading}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default GeneralSettings;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: moderateScale(20),
    },
    btnWrapper: {
      marginVertical: verticalScale(20),
    },
  });
