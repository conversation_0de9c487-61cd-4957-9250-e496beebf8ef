/* eslint-disable no-nested-ternary */
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import {
  Dimensions,
  Platform,
  Pressable,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {
  FlatList,
  Gesture,
  GestureDetector,
} from 'react-native-gesture-handler';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import AntDesignIcon from 'react-native-vector-icons/AntDesign';
import EntypoIcon from 'react-native-vector-icons/Entypo';
import WebView, { WebViewMessageEvent } from 'react-native-webview';
import { PLAYER_STATES } from 'react-native-youtube-iframe';

import { PauseIcon, PlayIcon } from '../../assets/svgs';
import {
  bottomNavBarH,
  isGooglePixel,
  navigationBarHeight,
  SOFT_MENU_BAR_HEIGHT,
} from '../../constants/common';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { setResumeVideo } from '../../redux/slices/adSlice';
import {
  checkAdDisplay,
  playVideo,
  setIsFetchNewPlaylist,
  setIsMiniPlayer,
  stopVideo,
  togglePlay,
} from '../../redux/slices/miniPlayerSlice';
import { fetchPlaylist, resetState } from '../../redux/slices/playlistSlice';
import { fetchPosts, loadMorePosts } from '../../redux/slices/postsSlice';
import HighlightedEpisodesItem from '../../screens/Home/components/HighlightedEpisodesItem';
import { PostDetailItem } from '../../screens/Home/components/PostDetailItem';
import { EPostType } from '../../screens/Home/HomeFeed';
import { PlaylistVideos } from '../../types/playlist';
import { HighlightedEpisode } from '../../types/podcasts';
import { Post } from '../../types/posts';
import {
  getCarouselListType,
  getYoutubeURL,
  injectedCSS,
  injectedJavaScript,
} from '../../utils/posts';
import { AnimatedButton } from '../common/AnimatedButton';
import CustomText from '../common/text';
import { PlaylistItem } from '../playlist';
import { MiniPlayerSkeleton } from '../skeleton';
import ToggleButton from '../ToggleButton';

import { PostInsights } from './card/components';

export type SocialMediaValues = {
  url: string;
  isVisible: boolean;
};
export interface ISocialIcons {
  instagram: SocialMediaValues;
  facebook: SocialMediaValues;
  twitter: SocialMediaValues;
  tiktok: SocialMediaValues;
  youtube: SocialMediaValues;
}

const { height: screenHeight } = Dimensions.get('screen');
const { height, width } = Dimensions.get('window');
const MINIMIZED_HEIGHT = verticalScale(50);
const DRAG_THRESHOLD = verticalScale(335);
const VIDEO_PLAYER_HEIGHT =
  Platform.OS === 'ios' ? verticalScale(190) : (width / 16) * 10;

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const hapticFeedbackOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const PostDetailModal: FC = () => {
  const dispatch = useAppDispatch();
  const { colors, theme } = useAppSelector(state => state.theme);
  const { posts } = useAppSelector(state => state.posts);
  const { isPlaying, videoData, isFetchNewPlaylist } = useAppSelector(
    state => state.miniPlayer,
  );
  const { highlightedEpisodes } = useAppSelector(state => state.posts);
  const { playlist, status: playlistStatus } = useAppSelector(
    state => state.playlist,
  );
  const { isAdVisible, resumeVideo } = useAppSelector(state => state.ads);
  const webviewRef = useRef(null);

  const [isVideoPlaying, setIsVideoPlaying] = useState(isPlaying);
  const [selectedCarouselList, setSelectedCarouselList] = useState<
    HighlightedEpisode[]
  >([]);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [isMiniPlayer, setIsLocalMiniPlayer] = useState(false);

  const [playlistData, setPlaylistData] = useState<PlaylistVideos[]>([]);

  const styles = useMemo(
    () => createStyles(colors, isMiniPlayer),
    [colors, isMiniPlayer],
  );

  const safeHeight = screenHeight - bottomNavBarH;
  const statusBarHeight = StatusBar.currentHeight || 24;

  const safeHeight2 = screenHeight - (statusBarHeight + navigationBarHeight);

  const getTranslateY = (value: number) => {
    if (isGooglePixel) {
      return value / 1.25;
    }

    return safeHeight2 * 0.89;
  };

  const MINI_PLAYER_TRANSLATEY = getTranslateY(screenHeight);
  const translateY = useSharedValue(0);
  const modalHeight = useSharedValue(
    isGooglePixel ? screenHeight - SOFT_MENU_BAR_HEIGHT : safeHeight,
  );
  const videoPlayerWidth = useSharedValue(width);
  const videoPlayerHeight = useSharedValue(VIDEO_PLAYER_HEIGHT);
  const initialTranslateY = useSharedValue(0);

  const youtubeURL = getYoutubeURL(
    videoData?.videoId!,
    (isPlaying && !isAdVisible) || resumeVideo,
  );

  const filteredPosts = useMemo(
    () => [
      posts.find(item => item.id === videoData?.id),
      ...posts.filter(item => item.id !== videoData?.id),
    ],
    [posts],
  );

  useEffect(() => {
    if (videoData?.playlistId && videoData?.type === EPostType.PODCAST_CAROUSEL)
      dispatch(
        fetchPlaylist({
          id: videoData?.playlistId,
          channel_id: videoData?.channelId,
        }),
      );

    return () => {
      setPlaylistData([]);
      resetState();
    };
  }, [videoData?.playlistId, videoData?.channelId]);

  useEffect(() => {
    if (
      videoData?.type === EPostType.PODCAST_CAROUSEL &&
      playlistStatus !== 'loading'
    ) {
      dispatch(
        playVideo({
          ...videoData,
          id: String(playlist?.videos?.[0]?.id),
          title: playlist?.videos?.[0]?.video_title,
          subtitle: playlist?.channel?.name || videoData?.subtitle,
          videoId: playlist?.videos?.[0]?.video_id,
          videoViews: String(playlist?.videos?.[0]?.views),
          time: playlist?.videos?.[0]?.created_at,
          type: EPostType.PODCAST_CAROUSEL,
        }),
      );
      setPlaylistData(playlist?.videos?.slice(0, 6) as PlaylistVideos[]);
      setIsVideoPlaying(true);
    }

    return () => {
      setPlaylistData([]);
      resetState();
    };
  }, [playlist, playlistStatus]);

  useEffect(() => {
    if (!posts.length && videoData?.type === EPostType.POST)
      dispatch(fetchPosts({ type: 'get_news_feed', limit: 10 }));
  }, [dispatch]);

  useEffect(() => {
    if (
      videoData?.list &&
      videoData?.id &&
      videoData?.type === EPostType.HIGHLIGHTED_EPISODES &&
      !isAutoPlay &&
      isFetchNewPlaylist
    ) {
      const episodesList =
        highlightedEpisodes[getCarouselListType(videoData?.list)].episodes;
      const selectedVideoData = episodesList?.find(
        item => item.id === Number(videoData?.id),
      );
      setSelectedCarouselList([
        selectedVideoData!,
        ...episodesList.filter(item => item.id !== selectedVideoData?.id),
      ]);
      dispatch(setIsFetchNewPlaylist(false));
    }
  }, [videoData?.id, videoData?.list, isFetchNewPlaylist]);

  const handleSetIsMiniPlayer = (value: boolean) => {
    setIsLocalMiniPlayer(value);
    dispatch(setIsMiniPlayer(value));
  };

  const gesture = Gesture.Pan()
    .onUpdate(event => {
      translateY.value = withSpring(
        Math.max(event.translationY * 1.3 + initialTranslateY.value, 0),
        {
          damping: 30,
          stiffness: 300,
        },
      );
      const progress = Math.min(1, event.translationY / 600);
      if (event.translationY > 0) {
        videoPlayerHeight.value = Math.max(
          90,
          VIDEO_PLAYER_HEIGHT - progress * (VIDEO_PLAYER_HEIGHT - 90),
        );
      } else {
        videoPlayerHeight.value = withTiming(VIDEO_PLAYER_HEIGHT, {
          duration: 300,
        });
      }

      if (event.translationY > DRAG_THRESHOLD) {
        runOnJS(handleSetIsMiniPlayer)(true);
        videoPlayerWidth.value = withTiming(Math.max(width, 100), {
          duration: 300,
        });
      } else {
        videoPlayerWidth.value = withTiming(width, { duration: 300 });
        runOnJS(handleSetIsMiniPlayer)(false);
      }
    })
    .onEnd(event => {
      if (event.translationY > DRAG_THRESHOLD || event.velocityY > 100) {
        translateY.value = withSpring(MINI_PLAYER_TRANSLATEY, {
          damping: 50,
          stiffness: 300,
        });
        initialTranslateY.value = withSpring(height * 0.7, {
          damping: 50,
          stiffness: 300,
        });

        videoPlayerWidth.value = withTiming(Math.max(MINIMIZED_HEIGHT, 100), {
          duration: 300,
        });
        videoPlayerHeight.value = withSpring(MINIMIZED_HEIGHT, {
          damping: 50,
          stiffness: 300,
        });
        runOnJS(handleSetIsMiniPlayer)(true);
      } else {
        runOnJS(handleSetIsMiniPlayer)(false);
        translateY.value = withSpring(0, {
          damping: 50,
          stiffness: 300,
        });
        videoPlayerWidth.value = withTiming(Math.max(130, width), {
          duration: 300,
        });
        videoPlayerHeight.value = withSpring(VIDEO_PLAYER_HEIGHT, {
          damping: 50,
          stiffness: 300,
        });
        initialTranslateY.value = 0;
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
      height: modalHeight.value,
    };
  });
  const videoAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: videoPlayerHeight.value,
    };
  });

  const miniPlayerContainer = useAnimatedStyle(() => {
    return {
      width: videoPlayerWidth.value,
      height: videoPlayerHeight.value,
    };
  });

  const playNextVideo = () => {
    if (!isAutoPlay) return;

    const currentIndex = selectedCarouselList.findIndex(
      item => item.id === Number(videoData?.id),
    );
    const nextIndex = (currentIndex + 1) % selectedCarouselList.length;
    const nextEpisode = selectedCarouselList[nextIndex];

    if (nextEpisode) {
      dispatch(
        playVideo({
          id: String(nextEpisode?.id),
          title: nextEpisode?.title,
          subtitle: 'Highlighted Episodes',
          videoId: nextEpisode?.vidId,
          videoViews: String(nextEpisode?.views),
          time: nextEpisode?.time,
          list: nextEpisode?.list,
          type: EPostType.HIGHLIGHTED_EPISODES,
        }),
      );
      setIsVideoPlaying(true);
    }
  };

  const togglePlaying = async (state: PLAYER_STATES) => {
    dispatch(setResumeVideo(false));

    const shouldShowAd = await dispatch(checkAdDisplay());

    if (!shouldShowAd) {
      setIsVideoPlaying(false);
    } else {
      setIsVideoPlaying(true);
    }

    switch (state) {
      case PLAYER_STATES.PLAYING:
        setIsVideoPlaying(true);
        break;
      case PLAYER_STATES.PAUSED:
        setIsVideoPlaying(false);
        break;
      case PLAYER_STATES.ENDED:
        playNextVideo();
        break;
      default:
        break;
    }
  };

  const handleCloseModal = () => {
    dispatch(stopVideo());
    dispatch(setIsMiniPlayer(false));
    dispatch(resetState());
  };

  const handleFullScreen = () => {
    if (!isMiniPlayer) return;
    translateY.value = withSpring(0, {
      damping: 50,
      stiffness: 300,
    });
    videoPlayerWidth.value = withTiming(width, {
      duration: 300,
    });
    videoPlayerHeight.value = withSpring(VIDEO_PLAYER_HEIGHT, {
      damping: 50,
      stiffness: 500,
    });
    handleSetIsMiniPlayer(false);
  };

  const handlePlayPause = () => {
    if (isAdVisible) return;
    if (webviewRef.current) {
      ReactNativeHapticFeedback.trigger('impactLight', hapticFeedbackOptions);
      const jsCode = `document.querySelector('video').${isPlaying ? 'pause' : 'play'}();`;
      // @ts-ignore
      webviewRef.current.injectJavaScript(jsCode);
      dispatch(togglePlay(!isPlaying));
    }
  };

  const handleOnMessage = (event: WebViewMessageEvent) => {
    const message = event.nativeEvent.data;
    if (message === 'playing') {
      togglePlaying(PLAYER_STATES.PLAYING);
    } else if (message === 'paused') {
      togglePlaying(PLAYER_STATES.PAUSED);
    } else if (message === 'ended') {
      togglePlaying(PLAYER_STATES.ENDED);
    }
  };

  return (
    <GestureDetector gesture={gesture}>
      <Animated.View
        pointerEvents="box-none"
        style={[styles.container, animatedStyle]}>
        <Animated.View style={[styles.miniPlayerContainer, videoAnimatedStyle]}>
          {playlistStatus !== 'loading' ? (
            <>
              <Animated.View
                style={[styles.youtubeIframeContainer, miniPlayerContainer]}>
                <View style={styles.webViewWrapper}>
                  <WebView
                    ref={webviewRef}
                    style={styles.webView}
                    containerStyle={styles.webViewContainer}
                    source={{
                      uri: youtubeURL,
                    }}
                    allowsFullscreenVideo={true}
                    javaScriptEnabled={true}
                    injectedJavaScript={injectedJavaScript}
                    injectedCSS={injectedCSS}
                    onMessage={handleOnMessage}
                    mediaPlaybackRequiresUserAction={false}
                    allowsInlineMediaPlayback={true}
                  />
                </View>
              </Animated.View>
              <AnimatedPressable
                onPress={handleFullScreen}
                style={styles.contentContainer}>
                <CustomText numberOfLines={1} style={styles.videoTitle}>
                  {videoData?.title}
                </CustomText>
                {videoData?.subtitle ? (
                  <CustomText numberOfLines={2} style={styles.username}>
                    {videoData?.subtitle}
                  </CustomText>
                ) : null}
              </AnimatedPressable>
              <View style={styles.actionContainer}>
                <Pressable onPress={handlePlayPause}>
                  {isVideoPlaying ? (
                    <EntypoIcon
                      name="controller-paus"
                      size={18}
                      color={colors.foreground}
                    />
                  ) : (
                    <EntypoIcon
                      name="controller-play"
                      size={20}
                      color={colors.foreground}
                    />
                  )}
                </Pressable>
                <Pressable onPress={handleCloseModal}>
                  <AntDesignIcon
                    name="close"
                    size={24}
                    color={colors.foreground}
                  />
                </Pressable>
              </View>
            </>
          ) : (
            <MiniPlayerSkeleton isMiniPlayerVisible={isMiniPlayer} />
          )}
        </Animated.View>

        {!isMiniPlayer ? (
          <View style={[styles.fullScreenContainer]}>
            <View style={styles.contentContainer}>
              <Text numberOfLines={2} style={styles.title}>
                {videoData?.title}
              </Text>
              <View style={styles.postActionContainer}>
                <View style={styles.postActionLeftContent}>
                  <FastImage
                    source={
                      theme === 'light'
                        ? require('../../assets/png/cpnent-logo.png')
                        : require('../../assets/png/cpnent-logo-dark.png')
                    }
                    style={styles.logo}
                  />
                  {videoData?.videoViews && videoData?.time ? (
                    <PostInsights
                      views={videoData?.videoViews!}
                      time={videoData?.time}
                    />
                  ) : null}
                </View>
                <AnimatedButton onPress={handlePlayPause}>
                  <View style={styles.playContainer}>
                    {isVideoPlaying ? (
                      <PauseIcon
                        width={moderateScale(60)}
                        height={moderateScale(60)}
                        fill={colors.foreground}
                      />
                    ) : (
                      <PlayIcon
                        width={moderateScale(60)}
                        height={moderateScale(60)}
                        fill={colors.foreground}
                      />
                    )}
                  </View>
                </AnimatedButton>
              </View>
            </View>
            <View style={styles.flatlistContainer}>
              {videoData?.type === EPostType.POST ? (
                <FlatList
                  data={filteredPosts}
                  style={styles.list}
                  contentContainerStyle={{
                    paddingBottom: verticalScale(isGooglePixel ? 20 : 10),
                  }}
                  showsVerticalScrollIndicator={false}
                  renderItem={({ item }) => {
                    if (item) {
                      return (
                        <PostDetailItem
                          data={item as Post}
                          selectedVideoId={videoData?.id!}
                        />
                      );
                    }

                    return null;
                  }}
                  onEndReached={() => {
                    if (translateY.value !== 0) return;
                    dispatch(
                      loadMorePosts({
                        type: 'get_news_feed',
                        limit: 5,
                        after_post_id: Number(posts[posts.length - 1].id),
                      }),
                    );
                  }}
                  onEndReachedThreshold={0.3}
                />
              ) : videoData?.type === EPostType.HIGHLIGHTED_EPISODES ? (
                <>
                  <View style={styles.autoplayContainer}>
                    <CustomText style={styles.upnextText}>Up Next</CustomText>
                    <View style={styles.autoplaySubContainer}>
                      <CustomText style={styles.autoplayText}>
                        AUTOPLAY
                      </CustomText>
                      <ToggleButton
                        isEnabled={isAutoPlay}
                        style={styles.switch}
                        toggleSwitch={() => setIsAutoPlay(!isAutoPlay)}
                      />
                    </View>
                  </View>
                  <FlatList
                    data={selectedCarouselList}
                    style={styles.highlightedEpisodesList}
                    contentContainerStyle={{
                      paddingBottom: verticalScale(isGooglePixel ? 20 : 10),
                    }}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item }) => (
                      <HighlightedEpisodesItem
                        data={item}
                        selectedEpisodeId={Number(videoData?.id)}
                      />
                    )}
                  />
                </>
              ) : videoData?.type === EPostType.PODCAST_CAROUSEL ? (
                <FlatList
                  data={playlistData}
                  style={styles.list}
                  showsVerticalScrollIndicator={false}
                  renderItem={({ item }) => (
                    <PlaylistItem
                      {...item}
                      selectedEpisodeId={videoData?.id!}
                      channelName={
                        playlist?.channel?.name! || videoData?.subtitle!
                      }
                    />
                  )}
                  onEndReached={() => {
                    setPlaylistData([
                      ...playlistData,
                      ...playlist?.videos?.slice(
                        playlistData.length,
                        playlistData.length + 6,
                      )!,
                    ]);
                  }}
                  onEndReachedThreshold={0.3}
                />
              ) : null}
            </View>
          </View>
        ) : null}
      </Animated.View>
    </GestureDetector>
  );
};

export default PostDetailModal;

const createStyles = (colors: ColorScheme, isMiniPlayer: boolean) =>
  StyleSheet.create({
    container: {
      backgroundColor: 'transparent',
      position: 'absolute',
      alignItems: 'baseline',
    },
    miniPlayerContainer: {
      backgroundColor: colors.background,
      flexDirection: 'row',
      justifyContent: 'space-between',
      borderTopLeftRadius: isMiniPlayer ? 18 : 0,
      borderTopRightRadius: isMiniPlayer ? 18 : 0,
      shadowColor: colors.foreground,
      shadowOffset: {
        width: 0,
        height: -10,
      },
      shadowOpacity: 0.03,
      width: width,
      paddingHorizontal: isMiniPlayer ? moderateScale(6) : 0,
    },
    webView: {
      flex: 1,
      borderRadius: moderateScale(isMiniPlayer ? 8 : 12),
      overflow: 'hidden',
      position: 'relative',
      height: '100%',
    },
    webViewContainer: {
      marginVertical: !isMiniPlayer ? verticalScale(10) : 0,
      borderRadius: moderateScale(isMiniPlayer ? 8 : 12),
      overflow: 'hidden',
    },
    webViewWrapper: {
      height: isMiniPlayer ? '80%' : '100%',
      borderRadius: moderateScale(10),
      overflow: 'hidden',
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontFamily: 'Antonio-Bold',
    },
    views: {
      color: colors.foreground,
      fontSize: moderateScale(14),
      fontWeight: '500',
    },
    playContainer: {
      paddingHorizontal: moderateScale(14),
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: moderateScale(5),
    },
    playText: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontFamily: 'Antonio-SemiBold',
      textTransform: 'uppercase',
    },
    contentContainer: {
      paddingHorizontal: moderateScale(10),
      flex: isMiniPlayer ? 1 : 0,
      justifyContent: 'center',
      gap: moderateScale(4),
    },
    list: {
      width: '100%',
      flex: 1,
      backgroundColor: colors.background,
    },
    highlightedEpisodesList: {
      width: '100%',
      flex: 1,
      backgroundColor: colors.background,
      paddingBottom: verticalScale(20),
      paddingTop: verticalScale(5),
    },
    videoTitle: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontWeight: '600',
    },
    username: {
      color: colors.foreground,
      fontSize: moderateScale(12),
    },
    actionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(10),
      marginRight: moderateScale(6),
    },
    postActionContainer: {
      flexDirection: 'row',
      marginTop: verticalScale(4),
      marginBottom: verticalScale(8),
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },

    fullScreenContainer: {
      width: width,
      flex: 1,
      zIndex: 100,
      backgroundColor: colors.background,
    },
    youtubeIframeContainer: {
      justifyContent: 'center',
      paddingHorizontal: moderateScale(3),
      borderRadius: moderateScale(10),
      overflow: 'hidden',
    },
    flatlistContainer: {
      flex: 1,
    },
    autoplayContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: colors.base,
      paddingVertical: verticalScale(10),
      paddingHorizontal: moderateScale(16),
    },
    autoplaySubContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    autoplayText: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      lineHeight: 20,
      fontWeight: '500',
    },
    upnextText: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      lineHeight: 20,
      fontWeight: '600',
    },
    switch: { transform: [{ scale: 0.6 }] },
    logo: {
      width: moderateScale(85),
      height: moderateScale(28),
    },
    postActionLeftContent: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      gap: moderateScale(5),
    },
  });
