import { FC } from 'react';

import {
  CategoriesIcon,
  CommunityIcon,
  FavoritesIcon,
  PodRankingIcon,
  ProfileIcon,
} from '../../assets/svgs';
import { useAppSelector } from '../../hooks/redux';
import { APP_ROUTES } from '../../types/routes';

interface RenderTabBarIconsProps {
  route: { name: string };
  focused: boolean;
}

export const RenderTabBarIcons: FC<RenderTabBarIconsProps> = ({
  route,
  focused,
}) => {
  const { colors } = useAppSelector(state => state.theme);

  switch (route.name) {
    case APP_ROUTES.EXPLORE:
      return <CategoriesIcon fill={colors.foreground} isFocused={focused} />;
    case APP_ROUTES.SAVED:
      return <FavoritesIcon fill={colors.foreground} isFocused={focused} />;
    case APP_ROUTES.HOME:
      return (
        <CommunityIcon
          fill={colors.foreground}
          playIconFill={colors.background}
          isFocused={focused}
        />
      );
    case APP_ROUTES.CHARTS:
      return <PodRankingIcon fill={colors.foreground} isFocused={focused} />;

    case APP_ROUTES.PROFILE:
      return <ProfileIcon fill={colors.foreground} isFocused={focused} />;

    default:
      return null;
  }
};
