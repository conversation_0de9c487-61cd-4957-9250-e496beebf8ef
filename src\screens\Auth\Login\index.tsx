import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import {
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import { OneSignal } from 'react-native-onesignal';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { yupResolver } from '@hookform/resolvers/yup';
import { useNavigation } from '@react-navigation/native';

import DarkLogo from '../../../assets/svgs/DarkLogo';
import Logo from '../../../assets/svgs/Logo';
import AnimatedLogin from '../../../components/AnimatedLogin';
import Checkbox from '../../../components/common/checkbox';
import Input from '../../../components/common/input';
import CustomText from '../../../components/common/text';
import { <PERSON><PERSON><PERSON><PERSON>, LOGIN_FIELDS } from '../../../constants/auth';
import { ColorScheme, THEME_MODE } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { setUser } from '../../../redux/slices/authSlice';
import { loginUser } from '../../../services/authServervice';
import { APP_ROUTES, NavigationProp, STACKS } from '../../../types/routes';
import {
  setItemToAS,
  setTokenToAS,
  STORAGE_KEYS,
} from '../../../utils/storage';
import { loginValidationSchema } from '../../../utils/validationSchema';
import AuthTab from '../components/AuthTab';
import ForgetPasswordModalStepper from '../components/ForgotPassword';

export interface ILoginFields {
  username: string;
  password: string;
  saveLoginInfo?: boolean;
}

const INITIAL_VALUES: ILoginFields = {
  username: '',
  password: '',
  saveLoginInfo: false,
};
const screenHeight = Dimensions.get('screen').height;

const Login = () => {
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInputsFilled, setIsInputsFilled] = useState(false);

  const navigation = useNavigation<NavigationProp>();
  const { colors, theme } = useAppSelector(state => state.theme);
  const dispatch = useAppDispatch();

  const logoPositionY = useRef(
    new Animated.Value(screenHeight / verticalScale(2)),
  ).current;

  const logoOpacity = useRef(new Animated.Value(0.4)).current;
  const logoScale = useRef(new Animated.Value(2)).current;

  useEffect(() => {
    setTimeout(() => {
      OneSignal.Notifications.requestPermission(true);
    }, 3000);
  }, []);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(logoPositionY, {
        toValue: screenHeight / verticalScale(20),
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(logoOpacity, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(logoScale, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ILoginFields>({
    mode: 'onSubmit',
    resolver: yupResolver(loginValidationSchema),
    defaultValues: INITIAL_VALUES,
  });

  useEffect(() => {
    const subscription = watch(data => {
      const isFilled =
        (data?.username?.trim().length ?? 0) > 0 &&
        (data?.password?.trim().length ?? 0) > 0;
      setIsInputsFilled(isFilled);
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  const onSubmit: SubmitHandler<ILoginFields> = async data => {
    setIsLoading(true);
    const isSaveLoginInfo = data?.saveLoginInfo;
    const oneSignalId = await OneSignal.User.getOnesignalId();
    const subscriptionId = await OneSignal.User.pushSubscription.getIdAsync();
    delete data?.saveLoginInfo;

    const loginPayload = {
      ...data,
      ios_n_device_id: oneSignalId,
    };

    const response = await loginUser(loginPayload);

    if (response) {
      const { access_token, user_id: userId } = response;

      dispatch(setUser({ access_token, userId, oneSignalId, subscriptionId }));
      if (isSaveLoginInfo) {
        await setTokenToAS(access_token);
        await setItemToAS(STORAGE_KEYS.USER_ID, userId);
      }
      navigation.navigate(STACKS.APP, { screen: APP_ROUTES.HOME });
    }
    setIsLoading(false);
  };

  const handleForgotPassword = () => setIsVisible((prev: boolean) => !prev);

  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <KeyboardAvoidingView style={styles.container} enabled behavior="padding">
      <SafeAreaView style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="always">
          {/* Logo Animation */}
          <View style={styles.logoWrapper}>
            <Animated.View
              style={[
                styles.logo,
                {
                  transform: [
                    { translateY: logoPositionY },
                    { scale: logoScale },
                  ],
                  opacity: logoOpacity,
                },
              ]}>
              {theme === THEME_MODE.dark ? <DarkLogo /> : <Logo />}
            </Animated.View>
          </View>

          {/* Main Content */}
          <View style={styles.lottieWrapper}>
            <AnimatedLogin />
          </View>
          <View style={styles.wrapper}>
            <View style={styles.textWrapper}>
              <CustomText style={styles.mainTitle}>{LOGIN.title}</CustomText>
              <CustomText style={styles.subTitle}>{LOGIN.subTitle}</CustomText>
            </View>
            <AuthTab
              isInputsFilled={isInputsFilled}
              onPressLogin={handleSubmit(onSubmit) as () => void}
              isLoading={isLoading}
            />
            <View style={{ marginTop: verticalScale(13) }}>
              {LOGIN_FIELDS.map(field => (
                <Controller
                  key={field.name}
                  control={control}
                  name={field.name as keyof ILoginFields}
                  render={({ field: { onChange, value, name } }) =>
                    field.name === LOGIN_FIELDS[2].name ? (
                      <Checkbox
                        title={field.placeholder}
                        checked={value as boolean}
                        setChecked={onChange}
                      />
                    ) : (
                      <Input
                        name={name}
                        value={value as keyof ILoginFields}
                        placeholder={field.placeholder}
                        secureTextEntry={field.secureTextEntry}
                        onChangeText={_value => onChange(_value)}
                        errors={errors}
                        autoCapitalize="none"
                      />
                    )
                  }
                />
              ))}
            </View>
            <View style={styles.forgotPassword}>
              <Pressable onPress={handleForgotPassword}>
                <CustomText style={styles.forgotPasswordText}>
                  {LOGIN.forgotPassword}
                </CustomText>
              </Pressable>
            </View>
            <ForgetPasswordModalStepper
              isVisible={isVisible}
              setIsVisible={setIsVisible}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default Login;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    logoWrapper: {
      alignItems: 'center',
      height: screenHeight / verticalScale(7),
    },
    logo: {
      alignSelf: 'center',
      zIndex: 4,
      elevation: 5,
    },
    lottieWrapper: {
      zIndex: -1,
    },
    wrapper: {
      marginHorizontal: moderateScale(20),
    },
    textWrapper: {
      marginVertical: verticalScale(20),
    },
    mainTitle: {
      fontWeight: '500',
      fontSize: moderateScale(21),
      textAlign: 'center',
      color: colors.foreground,
    },
    subTitle: {
      fontWeight: '500',
      fontSize: moderateScale(13),
      textAlign: 'center',
      color: colors.foreground,
      width: '70%',
      marginHorizontal: 'auto',
      marginTop: verticalScale(5),
    },
    forgotPassword: {
      marginTop: verticalScale(-12),
      alignSelf: 'flex-end',
    },
    forgotPasswordText: {
      color: colors.secondary,
      fontStyle: 'italic',
    },
    btnWrapper: {
      marginVertical: verticalScale(20),
    },
    bottomText: {
      color: colors.secondary,
      textAlign: 'center',
      marginTop: verticalScale(20),
    },
  });
