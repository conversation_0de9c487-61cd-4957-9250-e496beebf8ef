import React, { FC, useEffect, useMemo } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  StyleSheet,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { ParamListBase } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { VerifyIconWithStroke } from '../../assets/svgs';
import CustomText from '../../components/common/text';
import VerifiedUsersSkeleton from '../../components/skeleton/VerifiedUsersSkeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import {
  getVerifiedUsers,
  loadMoreVerifiedUsers,
  resetVerifiedUsers,
} from '../../redux/slices/userSlice';
import { APP_ROUTES } from '../../types/routes';
import { Userdata } from '../../types/user';

const PLACEHOLDER_AVATAR = require('../../assets/png/placeholder-avatar.png');

const LIMIT = 12;

interface VerifiedUsersScreenProps {
  navigation: StackNavigationProp<ParamListBase, string, undefined>;
}

const VerifiedUsersScreen: FC<VerifiedUsersScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);

  const styles = useMemo(() => createStyles(colors), [colors]);
  const {
    verifiedUsers,
    verifiedUsersStatus,
    verifiedUsersPage,
    verifiedUsersHasMore,
    loadingMoreVerifiedUsers,
  } = useAppSelector(state => state.user);

  useEffect(() => {
    // Reset state and load first page
    dispatch(resetVerifiedUsers());
    dispatch(getVerifiedUsers({ limit: LIMIT, page: 1 }));
  }, []);

  const handleLoadMore = () => {
    if (
      verifiedUsersStatus !== 'loading' &&
      verifiedUsersHasMore &&
      !loadingMoreVerifiedUsers
    ) {
      dispatch(
        loadMoreVerifiedUsers({ limit: LIMIT, page: verifiedUsersPage + 1 }),
      );
    }
  };

  const renderFooter = () => {
    // Only show spinner if we are loading more (pagination), not initial load
    if (verifiedUsers.length > 0 && loadingMoreVerifiedUsers) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" />
        </View>
      );
    }

    return null;
  };

  const renderUser = ({ item }: { item: Userdata }) => (
    <Pressable
      style={styles.userItem}
      onPress={() => {
        navigation.navigate(APP_ROUTES.OTHER_USER_PROFILE, {
          userId: item.user_id,
          otherUserProfile: true,
        });
      }}>
      <View style={styles.avatarContainer}>
        <FastImage
          source={
            item.avatar
              ? { uri: item.avatar, priority: FastImage.priority.high }
              : PLACEHOLDER_AVATAR
          }
          style={styles.avatar}
        />
        <VerifyIconWithStroke
          width={26}
          height={26}
          style={styles.verifiedIcon}
        />
      </View>
      <View style={styles.nameRow}>
        <CustomText numberOfLines={1} style={styles.name}>
          {item?.username || `${item?.first_name} ${item?.last_name}`}
        </CustomText>
      </View>
    </Pressable>
  );

  if (verifiedUsersStatus === 'loading') {
    return <VerifiedUsersSkeleton />;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={verifiedUsers}
        renderItem={renderUser}
        keyExtractor={item => `${item.user_id}-${item.username}`}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        numColumns={3}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={
          verifiedUsersStatus === 'succeeded' && verifiedUsers.length === 0 ? (
            <CustomText style={styles.emptyText}>
              No verified users found.
            </CustomText>
          ) : null
        }
        ListFooterComponent={renderFooter}
      />
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContainer: {
      paddingHorizontal: moderateScale(16),
      gap: verticalScale(10),
    },
    userItem: {
      flex: 1,
      alignItems: 'center',
      margin: moderateScale(6),
      borderRadius: moderateScale(16),
      paddingVertical: verticalScale(8),
      paddingHorizontal: moderateScale(4),
      minWidth: moderateScale(90),
      maxWidth: '31%',
    },
    avatar: {
      width: moderateScale(80),
      height: moderateScale(80),
      borderRadius: moderateScale(54),
    },
    avatarContainer: {
      position: 'relative',
    },
    nameRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: verticalScale(8),
    },
    name: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontWeight: '600',
      marginRight: moderateScale(4),
      textAlign: 'center',
    },
    verifiedIcon: {
      position: 'absolute',
      left: 0,
      bottom: 0,
    },
    emptyText: {
      textAlign: 'center',
      marginTop: verticalScale(32),
      color: colors.foreground,
      fontSize: moderateScale(16),
    },
    footerContainer: {
      padding: 16,
    },
  });

export default VerifiedUsersScreen;
