import Toast from 'react-native-toast-message';

import { CPN, REST_SUB_URL } from '../../constants/common/api';
import { MANAGE_SESSIONS_TEXT } from '../../constants/manageSessions';
import { TOAST_TITLE, TOAST_TYPE } from '../../constants/toast';
import {
  DeleteSessionRequest,
  DeleteSessionResponse,
  GetSessionsResponse,
  SessionData,
} from '../../types/manageSessions';

import { API } from './ApiInstance';

export const getAllSessions = async (): Promise<SessionData[] | null> => {
  const response = await API.Post<{ type: string }, GetSessionsResponse>(
    `${CPN}${REST_SUB_URL.SESSIONS}`,
    { type: 'get' },
  );
  if (response.data?.api_status === 200) {
    return response.data.data;
  }
  Toast.show({
    type: TOAST_TYPE.ERROR,
    text1: TOAST_TITLE.SESSION,
    text2: MANAGE_SESSIONS_TEXT.errorFetch,
  });

  return null;
};

export const deleteSession = async (
  payload: DeleteSessionRequest,
): Promise<boolean> => {
  const response = await API.Post<DeleteSessionRequest, DeleteSessionResponse>(
    `${CPN}${REST_SUB_URL.SESSIONS}`,
    payload,
  );
  if (response.data?.api_status === 200) {
    Toast.show({
      type: TOAST_TYPE.SUCCESS,
      text1: TOAST_TITLE.SESSION,
      text2: response.data.message,
    });

    return true;
  }
  Toast.show({
    type: TOAST_TYPE.SUCCESS,
    text1: TOAST_TITLE.SESSION,
    text2: MANAGE_SESSIONS_TEXT.errorDelete,
  });

  return false;
};
