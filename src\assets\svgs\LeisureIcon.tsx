import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const LeisureIcon = (props: SvgProps) => (
  <Svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="white"
    stroke="white"
    {...props}>
    <Path
      d="M9.48185 13.4631L6.73185 12.0971L6.73126 12.0968C6.66155 12.0627 6.58456 12.0495 6.50924 12.0572V12.0245L6.36347 12.0996L3.61756 13.5131C3.61747 13.5132 3.61738 13.5132 3.61728 13.5133C3.61309 13.5154 3.6094 13.5175 3.60712 13.5187L3.60675 13.5189L3.60232 13.5214L3.60224 13.5212L3.59709 13.5242C3.05754 13.8408 2.82541 14.3362 2.84166 14.8249C2.85474 15.2185 3.02874 15.6043 3.32524 15.8855C2.70953 15.945 2.17225 16.358 1.9534 16.9596C1.74651 17.5004 1.78904 18.1235 2.05971 18.6138C2.33252 19.1079 2.83668 19.4652 3.54003 19.4652H4.86362L3.78549 20.9609L3.7853 20.9611C3.65269 21.1464 3.69076 21.4072 3.87394 21.5444L3.87428 21.5447C4.05831 21.6813 4.31714 21.641 4.4519 21.4544L4.45195 21.4543L5.88534 19.4652H11.3159L12.7475 21.4543L12.7476 21.4544C12.8822 21.6408 13.1408 21.6824 13.3259 21.5458L13.3265 21.5453C13.4613 21.4443 13.5183 21.2755 13.4852 21.1193H13.5301L13.4158 20.9609L12.3377 19.4652H14.1458C14.2562 19.4652 14.3619 19.4205 14.439 19.3408C14.4391 19.3408 14.4392 19.3407 14.4393 19.3406L20.5809 13.0857L20.5809 13.0857C21.2644 12.3889 21.2066 11.5166 20.79 10.8854C20.3954 10.2874 19.6664 9.8883 18.9252 10.0624C18.7598 9.40966 18.28 8.88281 17.6465 8.66682L17.6464 8.66676C16.9783 8.44024 16.2433 8.59466 15.7178 9.07024L15.7177 9.07036C15.1933 9.54672 14.9586 10.2717 15.102 10.9712L15.102 10.9714C15.1743 11.3215 15.3358 11.6391 15.5632 11.8968C15.4335 11.7947 15.2889 11.7134 15.1356 11.6578C14.6569 11.4841 14.0947 11.5621 13.6365 12.0288L11.9783 13.7179H10.4026L9.00651 8.41049L15.5518 6.62438L9.48185 13.4631ZM9.48185 13.4631L8.20887 8.62767L1.66529 10.4142C1.44292 10.4751 1.21762 10.3394 1.15908 10.1176L1.15906 10.1175C0.637459 8.13482 0.910022 6.02256 1.91793 4.24511C2.92586 2.46671 4.58583 1.16906 6.53363 0.637419C8.48151 0.105724 10.5566 0.384586 12.3024 1.41259C14.0508 2.43694 15.3249 4.12867 15.8439 6.11232C15.8721 6.21911 15.8568 6.33351 15.8029 6.42898L15.7158 6.37982L15.8029 6.42898C15.7485 6.52532 15.6585 6.59538 15.5519 6.62435L9.48185 13.4631ZM9.41757 15.8794L6.72496 14.5914L6.72479 14.5913C6.61494 14.539 6.48788 14.5393 6.37804 14.5898L6.37812 14.5902L4.6925 15.3797L4.69243 15.3795L4.68727 15.3823C4.4799 15.4947 4.23114 15.4924 4.025 15.3767C3.82027 15.2613 3.6848 15.0477 3.6667 14.8084C3.66222 14.5764 3.78878 14.3629 3.99035 14.2587C3.99037 14.2587 3.99039 14.2586 3.99042 14.2586L6.55261 12.9403L9.71665 14.5134L9.7173 14.5137C9.774 14.5414 9.83655 14.5558 9.89933 14.5558H12.1504C12.2605 14.5558 12.3673 14.5114 12.4447 14.4304C12.4449 14.4302 12.4451 14.43 12.4453 14.4298L14.2225 12.6195L14.2225 12.6195C14.3786 12.4604 14.5481 12.4059 14.7042 12.4158C14.8629 12.4258 15.0164 12.5029 15.1338 12.6226C15.2512 12.7422 15.3276 12.8996 15.3376 13.0631C15.3474 13.2242 15.2934 13.398 15.1376 13.5569L9.41757 15.8794ZM9.41757 15.8794H12.8575L15.1375 13.5569L9.41757 15.8794ZM5.65077 1.85014C4.57011 3.92316 4.41391 6.37263 5.22718 8.57495L1.86228 9.49228C1.55112 7.93879 1.77509 6.3215 2.49674 4.91478L5.65077 1.85014ZM5.65077 1.85014C4.29997 2.48869 3.19009 3.56493 2.49676 4.91473L5.65077 1.85014ZM15.7951 12.1203C15.9371 12.2363 16.0957 12.333 16.2675 12.406L16.0768 12.6002C16.0112 12.423 15.9146 12.2608 15.7951 12.1203ZM13.2835 16.6291L13.3181 16.6026V16.5988L13.3227 16.5941L18.6735 11.1428C18.8876 10.9247 19.1259 10.8507 19.3496 10.8689C19.5763 10.8873 19.7962 11.0012 19.9651 11.1733C20.1339 11.3453 20.2463 11.5701 20.2644 11.8026C20.2824 12.0325 20.209 12.2762 19.995 12.4941L13.9746 18.6265H3.54016V18.6264L3.53537 18.6266C3.17969 18.6437 2.85188 18.426 2.7241 18.0838L2.72375 18.0829C2.61269 17.794 2.62539 17.4484 2.76233 17.1782C2.89674 16.9131 3.15126 16.7182 3.54016 16.7182H13.0287C13.1194 16.7182 13.2071 16.6878 13.279 16.6326L13.2835 16.6291ZM16.2195 9.73559L16.2196 9.73541C16.5412 9.40868 17.023 9.30899 17.4447 9.48002C17.852 9.64647 18.1295 10.0376 18.1583 10.4838C18.1347 10.5059 18.1115 10.5286 18.0885 10.552L16.943 11.7189C16.5071 11.6876 16.1244 11.4047 15.9626 10.9863C15.7954 10.5538 15.8967 10.0618 16.2195 9.73559ZM11.8891 2.135L11.8893 2.13513C13.3246 2.97683 14.4066 4.32661 14.9288 5.92687L11.5637 6.8451C11.1862 4.52474 9.84771 2.48299 7.89344 1.23778C9.28154 1.10901 10.6782 1.42057 11.8891 2.135ZM7.48923 15.8794H5.58795L6.54865 15.4293L7.48923 15.8794ZM6.78002 1.56737C8.91229 2.62117 10.4018 4.67608 10.7621 7.06417L6.02749 8.35608C5.16892 6.10474 5.45009 3.56712 6.78002 1.56737Z"
      fill={props.stroke}
      stroke={props.stroke}
      strokeWidth={0.2}
    />
  </Svg>
);
