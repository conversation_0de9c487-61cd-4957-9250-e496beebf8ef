import { useEffect, useMemo } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';

import { ListItem } from '../../components/charts';
import { OfflineView } from '../../components/OfflineView';
import { ChartSkeleton } from '../../components/skeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import { fetchChannels } from '../../redux/slices/channelSlice';

export const Charts = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useConnectionStatus();

  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { channels, status } = useAppSelector(state => state.channels);

  useEffect(() => {
    dispatch(fetchChannels({ type: 'top' }));
  }, []);

  if (!isConnected) return <OfflineView />;

  if (status === 'loading') return <ChartSkeleton />;

  return (
    <View style={styles.container}>
      <FlatList
        data={channels?.top}
        showsVerticalScrollIndicator={false}
        keyExtractor={item => item.id.toString()}
        renderItem={({ item, index }) => <ListItem index={index} {...item} />}
        // eslint-disable-next-line react/no-unstable-nested-components
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    separator: {
      borderBottomColor: colors.base,
      borderBottomWidth: 1,
      marginHorizontal: moderateScale(20),
    },
  });
