import React, { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import CustomButton from '../../../components/common/button';
import Input from '../../../components/common/input';
import CustomText from '../../../components/common/text';
import { PLACEHOLDER_AVATAR } from '../../../constants/profile';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import { openGallery } from '../../../utils/media';
import { IUserData } from '../UpdateProfile';

export interface UpdateProfileHeaderProps {
  userData?: IUserData;
  setUserData: React.Dispatch<React.SetStateAction<IUserData>>;
}

export const UpdateProfileHeader: FC<UpdateProfileHeaderProps> = ({
  userData,
  setUserData,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(
    () => createStyles(colors, userData?.bio || ''),
    [colors, userData?.bio],
  );

  const handleOnchange = (name: string, value: string) => {
    setUserData(prevState => ({
      ...prevState,
      [name]: value,
    }));
  };

  const pickImage = async () => {
    const response = await openGallery();
    if (!response) return;
    handleOnchange('avatar', response);
  };

  return (
    <View style={styles.topSection}>
      <View style={styles.topSectionInner}>
        <FastImage
          source={
            userData?.avatar ? { uri: userData?.avatar } : PLACEHOLDER_AVATAR
          }
          onError={() => handleOnchange('avatar', '')}
          style={styles.profileImage}
        />
        <View style={{ width: moderateScale(188) }}>
          <CustomButton
            title="Pick An Image"
            style={styles.button}
            onPress={pickImage}
          />
          <CustomButton
            title="Delete"
            style={styles.button}
            onPress={() => handleOnchange('avatar', '')}
            variant="secondary"
          />
        </View>
      </View>
      <View>
        <Input
          label="Username"
          name="username"
          value={userData?.username}
          disabled
          placeholder="Username"
          style={styles.title}
        />
        <View>
          <Input
            label="Bio"
            name="bio"
            value={userData?.bio}
            placeholder="Bio"
            onChangeText={value => handleOnchange('bio', value)}
            style={styles.textArea}
            numberOfLines={4}
            multiline
          />
          <View style={styles.bioLengthContainer}>
            <CustomText style={styles.bioLengthMaxLimitText}>
              <CustomText style={styles.bioLengthText}>
                {userData?.bio.length}
              </CustomText>
              /100
            </CustomText>
          </View>
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: ColorScheme, bio: string) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      borderRadius: moderateScale(8),
      paddingVertical: verticalScale(20),
      paddingHorizontal: moderateScale(16),
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3,
      marginHorizontal: moderateScale(20),
    },
    title: { borderRadius: moderateScale(6), height: moderateScale(36) },
    topSection: {
      backgroundColor: colors.background,
      borderRadius: moderateScale(8),
      padding: moderateScale(20),
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3,
      marginBottom: verticalScale(14),
      marginHorizontal: moderateScale(20),
    },
    topSectionInner: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: moderateScale(16),
    },
    profileImage: {
      width: moderateScale(90),
      height: moderateScale(90),
      borderRadius: moderateScale(50),
    },
    button: {
      paddingVertical: moderateScale(14),
    },
    textArea: {
      borderRadius: moderateScale(6),
      height: moderateScale(80),
      paddingVertical: moderateScale(14),
    },
    bioLengthContainer: {
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
    },
    bioLengthText: {
      color: bio.length > 100 ? colors.accent : colors.foreground,
    },
    bioLengthMaxLimitText: {
      color: colors.foreground,
    },
  });
