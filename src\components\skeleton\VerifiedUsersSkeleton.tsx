import React, { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

interface VerifiedUsersSkeletonProps {
  numItems?: number;
}

const VerifiedUsersSkeleton: React.FC<VerifiedUsersSkeletonProps> = ({
  numItems = 12,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          flexWrap="wrap"
          justifyContent="space-between"
          paddingHorizontal={moderateScale(16)}>
          {Array.from({ length: numItems }).map((_, idx) => (
            <SkeletonPlaceholder.Item
              key={idx}
              width={moderateScale(90)}
              alignItems="center"
              margin={moderateScale(6)}
              minWidth={moderateScale(90)}
              maxWidth="31%"
              borderRadius={moderateScale(16)}
              paddingVertical={verticalScale(8)}
              paddingHorizontal={moderateScale(4)}
              marginBottom={verticalScale(10)}>
              <SkeletonPlaceholder.Item
                width={moderateScale(80)}
                height={moderateScale(80)}
                borderRadius={moderateScale(54)}
                marginBottom={verticalScale(8)}
              />
              <SkeletonPlaceholder.Item
                flexDirection="row"
                alignItems="center"
                justifyContent="center"
                marginTop={verticalScale(2)}>
                <SkeletonPlaceholder.Item
                  width={moderateScale(50)}
                  height={moderateScale(16)}
                  borderRadius={4}
                  marginRight={moderateScale(4)}
                />
                <SkeletonPlaceholder.Item
                  width={16}
                  height={16}
                  borderRadius={8}
                  marginLeft={moderateScale(1)}
                />
              </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder.Item>
          ))}
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      paddingHorizontal: moderateScale(16),
      backgroundColor: colors.background,
    },
  });

export default VerifiedUsersSkeleton;
