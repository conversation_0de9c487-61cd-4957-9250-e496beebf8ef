import React, { useMemo } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Modal, { ModalProps } from 'react-native-modal';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

interface Props extends Partial<ModalProps> {
  isVisible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title: string;
  message: string;
  confirmText: string;
  cancelText: string;
}

const AlertModal: React.FC<Props> = ({
  isVisible,
  onConfirm,
  onCancel,
  title,
  message,
  confirmText,
  cancelText,
  ...props
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <Modal
      onBackdropPress={onCancel}
      isVisible={isVisible}
      backdropOpacity={0.5}
      {...props}>
      <View style={styles.modalContainer}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            onPress={onCancel}
            style={[styles.button, styles.cancelButton]}>
            <Text style={styles.cancelText}>{cancelText}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onConfirm}
            style={[styles.button, styles.confirmButton]}>
            <Text style={styles.confirmText}>{confirmText}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    modalContainer: {
      backgroundColor: colors.background,
      padding: moderateScale(18),
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
    },
    title: {
      color: colors.foreground,
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: verticalScale(10),
    },
    message: {
      color: colors.foreground,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: verticalScale(18),
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: moderateScale(10),
    },
    button: {
      flex: 1,
      paddingVertical: verticalScale(8),
      borderRadius: 5,
      alignItems: 'center',
    },
    cancelButton: {
      backgroundColor: colors.base,
    },
    confirmButton: {
      backgroundColor: colors.accent,
    },
    cancelText: {
      color: colors.foreground,
      fontWeight: 'bold',
    },
    confirmText: {
      color: 'white',
      fontWeight: 'bold',
    },
  });

export default AlertModal;
