import * as React from 'react';
import Svg, { Path, Rect, SvgProps } from 'react-native-svg';
const SessionIcon = (props: SvgProps) => (
  <Svg width={45} height={45} viewBox="0 0 45 45" fill="none" {...props}>
    <Rect width={45} height={45} rx={22.5} fill="#C41208" />
    <Path
      d="M25.5 30.5H19.5M14.5 24.3002V18.7002C14.5 17.5801 14.5 17.0196 14.718 16.5918C14.9097 16.2155 15.2155 15.9097 15.5918 15.718C16.0196 15.5 16.5801 15.5 17.7002 15.5H27.3002C28.4203 15.5 28.9796 15.5 29.4074 15.718C29.7837 15.9097 30.0905 16.2155 30.2822 16.5918C30.5 17.0192 30.5 17.579 30.5 18.6969V24.3031C30.5 25.421 30.5 25.98 30.2822 26.4074C30.0905 26.7837 29.7837 27.0905 29.4074 27.2822C28.98 27.5 28.421 27.5 27.3031 27.5H17.6969C16.579 27.5 16.0192 27.5 15.5918 27.2822C15.2155 27.0905 14.9097 26.7837 14.718 26.4074C14.5 25.9796 14.5 25.4203 14.5 24.3002Z"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default SessionIcon;
