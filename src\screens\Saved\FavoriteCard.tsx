import React, { FC, useMemo } from 'react';
import { Animated, Dimensions, Pressable, StyleSheet } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import {
  playVideo,
  setShowMiniPlayer,
} from '../../redux/slices/miniPlayerSlice';
import { Channel } from '../../services/types';
import { EPostType } from '../Home/HomeFeed';

interface IFavoriteCard extends Channel {
  ITEMS_PER_ROW: number;
  scaleValue?: Animated.AnimatedInterpolation<string | number>;
  marginValue?: Animated.AnimatedInterpolation<string | number>;
  hideValue?: Animated.AnimatedInterpolation<string | number>;
}

const { width } = Dimensions.get('window');

const FavoriteCard: FC<IFavoriteCard> = ({
  id,
  name,
  image,
  url,
  ITEMS_PER_ROW,
  scaleValue,
  marginValue,
  hideValue,
}) => {
  const ITEM_SIZE = width / ITEMS_PER_ROW - 16;
  const dispatch = useAppDispatch();
  const navigation = useTypeSafeNavigation();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(
    () => createStyles(colors, ITEM_SIZE),
    [colors, ITEM_SIZE],
  );

  const handleNavigation = () => {
    const playlistId = url?.split('/')?.pop();
    if (!playlistId) return;
    if (!hideValue) navigation.goBack();
    dispatch(
      playVideo({
        playlistId: playlistId,
        channelId: String(id),
        type: EPostType.PODCAST_CAROUSEL,
      }),
    );
    dispatch(setShowMiniPlayer(true));
  };

  return (
    <Animated.View
      style={[
        styles.container,
        { transform: [{ scale: scaleValue || 1 }], margin: marginValue || 6 },
      ]}>
      <Pressable onPress={handleNavigation} style={styles.pressable}>
        <FastImage source={{ uri: image }} style={styles.image} />
        <Animated.Text
          numberOfLines={1}
          style={[styles.title, { opacity: hideValue }]}>
          {name}
        </Animated.Text>
      </Pressable>
    </Animated.View>
  );
};

export default FavoriteCard;

const createStyles = (colors: ColorScheme, ITEM_SIZE: number) =>
  StyleSheet.create({
    container: {
      borderRadius: 5,
      width: ITEM_SIZE,
      height: ITEM_SIZE + 20,
    },
    pressable: {
      justifyContent: 'space-between',
      alignItems: 'center',
      height: '100%',
    },
    image: {
      width: ITEM_SIZE - 5,
      height: ITEM_SIZE - 5,
      borderRadius: moderateScale(10),
    },
    title: {
      color: colors.foreground,
      fontFamily: 'Roboto-Regular',
    },
  });
