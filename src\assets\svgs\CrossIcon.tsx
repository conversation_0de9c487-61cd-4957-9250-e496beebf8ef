import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

interface CrossIconProps extends SvgProps {
  color?: string;
}

const CrossIcon: React.FC<CrossIconProps> = ({ color = 'white', ...props }) => (
  <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
    <Path
      d="M15 15L10 10M10 10L5 5M10 10L15 5M10 10L5 15"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

export default CrossIcon;
