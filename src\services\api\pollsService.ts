import { CPN, REST_SUB_URL, SERVER_KEY } from '../../constants/common/api';
import { Poll, CreatePollRequest, VoteRequest, PollsResponse, PollOption } from '../../types/polls';
import { API } from './ApiInstance';
import Toast from 'react-native-toast-message';
import { TOAST_TITLE, TOAST_TYPE } from '../../constants/toast';
import { Post } from '../../types/posts';

// Fetch polls - using the dedicated polls endpoint
export const fetchPollsService = async (payload: {
  action?: string;
  page?: number;
  user_id?: string;
}): Promise<Poll[]> => {
  try {
    console.log('fetchPollsService - payload:', payload);
    const formData = new FormData();
    formData.append('server_key', SERVER_KEY);
    
    // Add action parameter (defaults to 'all' if not specified)
    if (payload.action) {
      formData.append('action', payload.action);
    }
    
    // Add page parameter (defaults to 1 if not specified)
    if (payload.page) {
      formData.append('page', payload.page.toString());
    }
    
    // Add user_id if specified (required for user_polls and user_voted actions)
    if (payload.user_id) {
      formData.append('user_id', payload.user_id);
    }
    
    const response = await API.Post(
      `${CPN}${REST_SUB_URL.GET_POLLS}`,
      formData,
      true // isFormDataType
    );
    
    const responseData = response?.data as { api_status: number; polls: any[] };
    if (response.status && responseData?.api_status === 200 && responseData?.polls) {
      // Transform polls to Poll format
      return responseData.polls.map(transformPostToPoll);
    }
    
    let rawErrorMessage = (response?.data as any)?.errors?.error_text || response?.message || 'Unknown error occurred';
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_FETCH_POLLS,
      text2: errorMessage,
    });
    return [];
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: 'Failed to Fetch Polls',
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return [];
  }
};

// Create poll - using the same create post endpoint
export const createPollService = async (payload: CreatePollRequest): Promise<Poll | undefined> => {
  try {
    // Transform CreatePollRequest to match API format
    const formData = new FormData();
    
    try {
      formData.append('postText', payload.question);
      
      // Add each answer option as separate answer[] fields
      payload.options.forEach(option => {
        formData.append('answer[]', option.trim()); // Trim whitespace to match API format
      });
      
      // Calculate end date based on duration (duration is in days)
      const now = new Date();
      
      const durationInDays = Number(payload.duration) || 1; // Default to 1 day if invalid
      
      // Ensure duration is reasonable (1 to 30 days)
      const safeDuration = Math.max(1, Math.min(30, durationInDays));
      
      // Use a simpler date calculation
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + safeDuration);
      
      // Validate the date before using it
      if (isNaN(endDate.getTime())) {
        throw new Error('Invalid end date calculated');
      }
      
      const endDateISO = endDate.toISOString();
      formData.append('end', endDateISO);
      
    } catch (formError) {
      throw formError;
    }
    
    const response = await API.Post(
      `${CPN}${REST_SUB_URL.CREATE_POST}`,
      formData,
      true, // isFormDataType
    );
    
    const responseData = response?.data as APIResponse;
    if (responseData?.api_status === 200 && responseData?.post_data) {
      // Transform the returned post data to Poll format
      return transformPostToPoll(responseData.post_data);
    }
    
    // Use API response message instead of hardcoded error
    let rawErrorMessage = (response?.data as any)?.errors?.error_text || response?.message || 'Unknown error occurred';
    // Replace newlines with spaces for better toast display
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_CREATE_POLL,
      text2: errorMessage,
    });
    
    return undefined;
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_CREATE_POLL,
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return undefined;
  }
};

// Vote on poll
export const voteOnPollService = async (payload: VoteRequest): Promise<Poll | undefined> => {
  try {
    // Create form data for the vote request
    const formData = new FormData();
    formData.append('id', payload.optionId); // The option ID sent as 'id' parameter
    
    const response = await API.Post(
      `${CPN}${REST_SUB_URL.VOTE_UP}`,
      formData,
      true, // isFormDataType
    );
    
    if (response.status && response.data && (response.data as any)?.api_status === 200) {
      // Vote successful - return a minimal poll object to indicate success
      // The UI will refresh polls to get updated vote counts
      return { 
        id: payload.pollId,
        // This signals successful vote - the component will refresh all polls
      } as Poll;
    }
    
    // Use API response message instead of manually extracting message
    let rawErrorMessage = (response?.data as any)?.errors?.error_text || response?.message || 'Unknown error occurred';
    // Replace newlines with spaces for better toast display
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    const isClosedPoll = errorMessage.toLowerCase().includes('closed') || errorMessage.toLowerCase().includes('ended');
    
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: isClosedPoll ? 'Poll Closed' : TOAST_TITLE.FAILED_TO_VOTE,
      text2: errorMessage,
    });
    
    return undefined;
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_VOTE,
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return undefined;
  }
};

// Delete poll - using the same delete post endpoint
export const deletePollService = async (pollId: string): Promise<boolean> => {
  try {
    const formData = new FormData();
    formData.append('server_key', SERVER_KEY);
    formData.append('post_id', pollId);
    formData.append('action', 'delete');

    const response = await API.Post(
      `${CPN}/post-actions`,
      formData,
      true // isFormDataType
    );
    
    if (response.status) {
      return true;
    }
    
    // Use API response message instead of hardcoded error
    let rawErrorMessage = (response?.data as any)?.errors?.error_text || response?.message || 'Unknown error occurred';
    // Replace newlines with spaces for better toast display
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_DELETE_POLL,
      text2: errorMessage,
    });
    return false;
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_DELETE_POLL,
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return false;
  }
};

// Update poll - this might not be supported by the API
export const updatePollService = async (payload: {
  id: string;
  question: string;
  options: string[];
  duration: number;
}): Promise<Poll | undefined> => {
  try {
    // If the API supports updating polls, implement it here
    // Otherwise, this might need to delete and recreate
    const response = await API.Post(`${CPN}${REST_SUB_URL.POST_ACTION}`, {
      post_id: payload.id,
      action: 'edit',
      postText: payload.question,
      // Add other parameters as needed
    });
    
    if (response.status && response.data) {
      return response.data as unknown as Poll;
    }
    
    let rawErrorMessage = (response?.data as any)?.errors?.error_text || response?.message || 'Unknown error occurred';
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_UPDATE_POLL,
      text2: errorMessage,
    });
    return undefined;
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_UPDATE_POLL,
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return undefined;
  }
};

// Helper function to transform Post data to Poll format
// You'll need to adjust this based on your actual API response structure
const transformPostToPoll = (post: Post): Poll => {
  // Handle the case where options might be objects or strings
  const processOptions = (options: any[]): PollOption[] => {
    if (!options || !Array.isArray(options)) return [];
    
    return options.map((option, index) => {
      // If option is an object (from API response)
      if (typeof option === 'object' && option !== null) {
        return {
          id: option.id || String(index),
          text: option.text || option.option_text || String(option),
          votes: Number(option.option_votes) || Number(option.votes) || 0,
          percentage: Number(option.percentage_num) || Number(option.percentage) || 0,
          isVoted: post.voted_id !== undefined && 
                   post.voted_id !== null && 
                   post.voted_id !== 0 && 
                   String(post.voted_id) === String(option.id),
        };
      }
      // If option is a string (fallback)
      return {
        id: String(index),
        text: String(option),
        votes: 0,
        percentage: 0,
        isVoted: false, // Fallback case - assume not voted
      };
    });
  };

  // Calculate total votes from options
  const pollOptions = processOptions(post.options || []);
  const totalVotes = pollOptions.reduce((sum, option) => sum + option.votes, 0);

  // Calculate expiration date safely
  let createdDate: Date;
  let expirationDate: Date;
  
  try {
    // Handle Unix timestamp (convert seconds to milliseconds)
    if (post.time) {
      const timestamp = Number(post.time);
      // If it's a Unix timestamp in seconds (like 1752836293), convert to milliseconds
      const timestampMs = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
      createdDate = new Date(timestampMs);
    } else {
      createdDate = new Date();
    }
    
    // Validate created date
    if (isNaN(createdDate.getTime())) {
      createdDate = new Date();
    }
    
    expirationDate = new Date(createdDate);
    expirationDate.setDate(expirationDate.getDate() + 7); // Default 7 days
    
    // Validate expiration date
    if (isNaN(expirationDate.getTime())) {
      expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 7);
    }
  } catch (error) {
    createdDate = new Date();
    expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 7);
  }



  return {
    id: post.post_id,
    question: post.Orginaltext || post.postText || '',
    creator: {
      user_id: post.publisher.user_id,
      username: post.publisher.username || '',
      first_name: post.publisher.first_name || '',
      last_name: post.publisher.last_name || '',
      avatar: post.publisher.avatar || '',
      verified: post.publisher.verified === '1',
    },
    options: pollOptions,
    totalVotes,
    duration: 7, // Default to 7 days, adjust based on your API response
    createdAt: post.time || new Date().toISOString(),
    expiresAt: expirationDate.toISOString(),
    isCompleted: false, // Could be determined by checking if poll is expired
    isVoted: post.voted_id !== undefined && post.voted_id !== null && post.voted_id !== 0,
    userVotedOptionId: (post.voted_id !== undefined && post.voted_id !== null && post.voted_id !== 0) ? String(post.voted_id) : undefined,
    pollActive: Boolean(post.poll_active), // true = closed/show results, false = active/can vote
  };
};

interface APIResponse {
  api_status: number;
  errors?: {
    error_id: string;
    error_text: string;
  };
  post_data?: Post;
}

const fetchPollByIdService = async (postId: string): Promise<Poll | undefined> => {
  try {
    const formData = new FormData();
    formData.append('server_key', SERVER_KEY);
    formData.append('fetch', 'post_data');
    formData.append('post_id', postId);

    const response = await API.Post<FormData, APIResponse>(
      `${CPN}/get-post-data`,
      formData,
      true // isFormDataType
    );

    const responseData = response?.data as APIResponse;
    if (responseData?.api_status === 200 && responseData?.post_data) {
      return transformPostToPoll(responseData.post_data);
    }

    let rawErrorMessage = (responseData?.errors?.error_text || 'Unknown error occurred');
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_FETCH_POLL,
      text2: errorMessage,
    });
    return undefined;
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: 'Failed to Fetch Poll',
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return undefined;
  }
};

const reportPollService = async (pollId: string): Promise<boolean> => {
  try {
    const formData = new FormData();
    formData.append('post_id', pollId);
    formData.append('action', 'report');

    const response = await API.Post(
      `${CPN}/post-actions`,
      formData,
      true // isFormDataType
    );
    
    if (response.status) {
      return true;
    }
    
    // Show error toast with API response message
    let rawErrorMessage = (response?.data as any)?.errors?.error_text || response?.message || 'Unknown error occurred';
    // Replace newlines with spaces for better toast display
    const errorMessage = rawErrorMessage.replace(/\n/g, ' ');
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_REPORT_POLL,
      text2: errorMessage,
    });
    return false;
  } catch (error) {
    Toast.show({
      type: TOAST_TYPE.ERROR,
      text1: TOAST_TITLE.FAILED_TO_REPORT_POLL,
      text2: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return false;
  }
};

export { transformPostToPoll, fetchPollByIdService, reportPollService }; 