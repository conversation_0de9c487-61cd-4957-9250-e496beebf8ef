import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

interface BarBottomIconProps extends SvgProps {
  color?: string;
}

const BarBottomIcon: React.FC<BarBottomIconProps> = ({
  color = '#444444',
  ...props
}) => (
  <Svg width={14} height={14} viewBox="0 0 14 14" fill="none" {...props}>
    <Path
      d="M11.6667 8.75004H2.33334M11.6667 8.75004V4.19824C11.6667 3.54612 11.6667 3.21958 11.5396 2.97026C11.4278 2.75073 11.2488 2.57239 11.0293 2.46053C10.7798 2.33337 10.4535 2.33337 9.80012 2.33337H4.20012C3.54672 2.33337 3.21978 2.33337 2.97022 2.46053C2.75069 2.57239 2.57235 2.75073 2.46049 2.97026C2.33334 3.21982 2.33334 3.54676 2.33334 4.20015V8.75004M11.6667 8.75004V9.80184C11.6667 10.454 11.6667 10.78 11.5396 11.0294C11.4278 11.2489 11.2488 11.4278 11.0293 11.5397C10.78 11.6667 10.4539 11.6667 9.8018 11.6667H4.1982C3.54608 11.6667 3.21954 11.6667 2.97022 11.5397C2.75069 11.4278 2.57235 11.2489 2.46049 11.0294C2.33334 10.7798 2.33334 10.4535 2.33334 9.80015V8.75004"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

export default BarBottomIcon;
