import { Dimensions, StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../../../constants/theme/colors';

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    modalStyles: {
      justifyContent: 'flex-end',
      margin: 0,
    },
    container: {
      maxHeight: Dimensions.get('window').height * 0.8,
      backgroundColor: colors.background,
      padding: moderateScale(20),
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
    },
    title: {
      fontSize: moderateScale(18),
      fontWeight: '500',
      color: colors.foreground,
    },
    description: {
      color: colors.foreground,
      marginVertical: verticalScale(10),
    },
    btnsWrapper: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: verticalScale(10),
      gap: moderateScale(10),
    },
    btnStyle: { flex: 1 },
  });

export default createStyles;
