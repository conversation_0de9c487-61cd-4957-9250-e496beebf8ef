import * as React from 'react';
import Svg, { G, <PERSON>, SvgProps } from 'react-native-svg';

interface CommunityIconProps extends SvgProps {
  isFocused?: boolean;
  playIconFill?: string;
}

export const CommunityIcon: React.FC<CommunityIconProps> = props => {
  return (
    <Svg id="Layer_1" x="0px" y="0px" viewBox="0 0 1059 800" {...props}>
      <G>
        <G>
          {props?.isFocused ? (
            <>
              <Path d="M718.4,383c-1,4.3-1.5,8.7-2.2,13c0-0.1,0-0.1,0-0.2c0.8-3.8,1.6-8.5,2.5-13.9C718.6,382.3,718.5,382.6,718.4,383z" />
              <Path d="M766.9,237.6c-0.2-20.8-6.2-40.1-22.8-53.9c-8.3-6.8-18.4-11.5-28.6-17.7c-2.3-27.2-16.7-47-44.2-56.9 c-0.3-1.8-0.6-3.4-0.8-5c-4-29.5-27.9-51.1-57.6-51.2c-51.8-0.2-103.6-0.2-155.3,0c-25.3,0.1-47.4,16.8-54.9,41 c-1.6,5-2.3,10.3-3.4,15.4c-27.3,10.1-41.6,29.8-44,58.3c-0.2,0.2-0.2,0.4-0.4,0.5c-1.1,0.5-2.1,0.9-3.2,1.3 c-29.2,10.1-43.8,31.3-47.2,61.3c-2.2,19.4,0.1,38.6,2.6,57.7c5.3,39.7,10.7,79.4,16.7,119c2.7,17.9,5.9,35.8,11,53.1 c8.5,29.4,28.9,47.1,59.2,51.5c18.7,2.7,37.8,3.8,56.6,3.9c56.4,0.5,112.9,0.4,169.3,0c17.6-0.1,35.3-0.9,52.8-3.1 c33.6-4.2,56.1-23,64.4-56c3.7-14.8,6.3-29.9,8.8-45.1c0,0,0-0.1,0-0.2c0-0.2,0.1-0.5,0.2-0.9c0-0.2,0.1-0.4,0.1-0.5 c0,0,0,0,0-0.1c2.5-15,20.8-124.8,19.8-148.6C766.6,253.5,767,245.5,766.9,237.6z M453.5,85.7c4.6-0.8,9.3-0.9,13.9-0.9 c45.4-0.1,90.9-0.1,136.3,0c4.5,0,9,0.2,13.4,0.9c10.5,1.7,18.6,9.8,20.9,20.8H433.1C434.6,96.3,443,87.5,453.5,85.7z  M411.5,139.6c3.1-0.5,6.3-0.8,9.4-0.8c76.4,0,152.7,0,229.1,0c16.2,0,27.8,7.8,31.8,21.4c0.2,0.6,0.2,1.3,0.2,1.6 c-97.7-1.9-195.3-1.9-292.9,0C390.5,151.7,400.6,141.4,411.5,139.6z M718.7,381.9c-0.8,5.4-1.7,10.1-2.5,13.9c0,0.1,0,0.1,0,0.2 c-0.2,1.5-0.4,2.9-0.7,4.4c-0.1,0.4-0.1,0.8-0.2,1.2c-3.3,16.8-6,33.7-10.4,50.2c-3.9,14.7-14,24.5-29.4,27.3 c-9.9,1.8-20.1,3.7-30.1,3.8c-73.2,0.3-146.5,0.3-219.8,0c-10.5,0-21.2-2-31.6-4.1c-12.1-2.5-20.7-9.9-25.9-21.4 c-6.1-13.7-8.3-28.4-10.5-43c-6.1-41.5-12.3-82.9-17.5-124.5c-2.3-18.6-3.5-37.4-3.3-56.1c0.2-19.1,11.3-31.5,30-35.3 c12.3-2.5,25-4.7,37.5-4.8c87.4-0.4,174.8-0.4,262.2,0c12.5,0.1,25.2,2.2,37.5,4.8c16.5,3.5,27.2,13.5,29.6,30.9 c0.6,4.6,1,9.2,1.1,13.9C734.7,243.4,725.9,336.6,718.7,381.9z" />
              <Path d="M733.7,229.5c-2.3-17.3-13.1-27.4-29.6-30.9c-12.3-2.6-25-4.8-37.5-4.8c-87.4-0.4-174.8-0.4-262.2,0 c-12.5,0-25.2,2.3-37.5,4.8c-18.7,3.8-29.7,16.2-30,35.3c-0.2,18.7,0.9,37.6,3.3,56.1c5.2,41.6,11.4,83,17.5,124.5 c2.1,14.6,4.3,29.3,10.5,43c5.1,11.5,13.8,18.9,25.9,21.4c10.4,2.1,21,4,31.6,4.1c73.2,0.4,146.5,0.3,219.8,0 c10,0,20.2-1.9,30.1-3.8c15.4-2.8,25.5-12.6,29.4-27.3c4.4-16.5,7.1-33.4,10.4-50.2c0.1-0.4,0.2-0.8,0.2-1.2 c0.2-1.4,0.4-2.8,0.7-4.4c0.6-4.4,1.2-8.8,2.2-13c0.1-0.4,0.2-0.7,0.3-1.1c7.2-45.3,16-138.6,16-138.6 C734.6,238.7,734.3,234.1,733.7,229.5z M602.4,364.9c-12.6,9.2-26.3,16.8-39.6,25.1c-11.2,6.9-22.6,13.5-33.5,20.8 c-26,17.2-53.2-3.8-53.1-28.5c0.1-15.3,0-30.6,0-45.9h0.1c0-15.8-0.4-31.6,0.1-47.4c0.4-14.2,7.7-24.5,20.5-30.6 c9.1-4.4,18.7-4.5,27.3,0.8c25.9,15.6,51.9,31.2,77.2,47.8C621.2,319.8,621.6,351,602.4,364.9z" />
              <Path
                fill={props?.playIconFill}
                d="M601.5,306.9c-25.3-16.6-51.3-32.2-77.2-47.8c-8.6-5.2-18.2-5.1-27.3-0.8c-12.8,6.1-20.1,16.4-20.5,30.6 c-0.5,15.8-0.1,31.6-0.1,47.4h-0.1c0,15.3,0.1,30.6,0,45.9c-0.2,24.6,27.1,45.7,53.1,28.5c10.9-7.3,22.3-13.8,33.5-20.8 c13.3-8.3,27-15.9,39.6-25.1C621.6,351,621.2,319.8,601.5,306.9z M580.9,340.7c-23.2,14.6-46.6,28.9-69.9,43.3 c-0.2,0.1-0.6,0.1-1,0.2c-2.1-3.4-2.3-91.2,0-96.2c24.4,15.2,48.8,30.3,74.7,46.4C583.3,336.8,582.7,339.6,580.9,340.7z"
              />
              <Path d="M590.7,334.4c-1.4,2.4-8.1,5.2-9.8,6.3c-23.2,14.6-46.6,28.9-69.9,43.3c-0.2,0.1-0.6,0.1-1,0.2c-2.1-3.4-2.3-91.2,0-96.2 C534.5,303.1,564.8,318.3,590.7,334.4z" />
            </>
          ) : (
            <>
              <Path d="M355.3,167.5c2.4-28.5,16.7-48.3,44-58.3c1.1-5,1.8-10.3,3.4-15.4c7.5-24.2,29.6-40.9,54.9-41c51.8-0.2,103.6-0.2,155.3,0 c29.7,0.1,53.7,21.8,57.6,51.2c0.2,1.6,0.5,3.3,0.8,5c27.4,10,41.9,29.7,44.1,56.9c10.2,6.2,20.3,10.9,28.6,17.7 c16.6,13.7,22.6,33.1,22.8,53.9c0.1,16.3-1.5,32.5-2.9,48.8c-0.8,9.2-8.8,15-18,14c-8.4-0.9-14.4-8-13.9-17.1 c0.7-11.3,2-22.5,2.5-33.8c0.3-6.6-0.1-13.3-1-19.9c-2.3-17.3-13.1-27.4-29.6-30.9c-12.3-2.6-25-4.8-37.5-4.8 c-87.4-0.4-174.8-0.4-262.2,0c-12.5,0.1-25.2,2.3-37.5,4.8c-18.7,3.8-29.7,16.2-30,35.3c-0.2,18.7,0.9,37.6,3.3,56.1 c5.2,41.6,11.4,83,17.5,124.5c2.1,14.6,4.3,29.3,10.5,43c5.1,11.5,13.8,18.9,25.9,21.4c10.4,2.1,21,4,31.6,4.1 c73.2,0.3,146.5,0.3,219.7,0c10,0,20.2-1.9,30.1-3.8c15.4-2.8,25.5-12.6,29.4-27.3c4.4-16.5,7.1-33.4,10.4-50.2 c1.2-6.2,1.6-12.6,3.1-18.7c1.9-7.7,10-12.6,17.7-11.2c8.5,1.6,15,8.8,13.7,16.9c-3.8,22.8-6.9,45.7-12.5,68.1 c-8.3,33-30.8,51.8-64.4,56c-17.4,2.2-35.2,3-52.8,3.1c-56.4,0.4-112.9,0.5-169.3,0c-18.9-0.2-38-1.2-56.7-3.9 c-30.3-4.4-50.7-22.1-59.2-51.5c-5-17.3-8.2-35.3-11-53.1c-6-39.6-11.5-79.3-16.7-119c-2.5-19.1-4.8-38.3-2.6-57.7 c3.3-30,18-51.2,47.2-61.3c1.1-0.4,2.2-0.9,3.2-1.3C355,167.9,355.1,167.7,355.3,167.5z M682,161.7c0-0.3,0-1-0.2-1.6 c-4.1-13.6-15.6-21.4-31.8-21.4c-76.4,0-152.7,0-229.1,0c-3.1,0-6.3,0.3-9.4,0.8c-10.9,1.9-21,12.1-22.3,22.1 C486.7,159.9,584.3,159.9,682,161.7z M638,106.4c-2.3-11-10.5-19.1-20.9-20.7c-4.4-0.7-8.9-0.8-13.4-0.9 c-45.4-0.1-90.9-0.1-136.3,0c-4.6,0-9.4,0.1-13.9,0.9c-10.4,1.8-18.9,10.5-20.4,20.7C501.3,106.4,569.5,106.4,638,106.4z" />
              <Path d="M476.4,336.4c0-15.8-0.4-31.6,0.1-47.4c0.4-14.2,7.7-24.5,20.5-30.6c9.1-4.4,18.7-4.5,27.3,0.8 c25.9,15.6,51.9,31.2,77.2,47.8c19.7,12.9,20.1,44.1,0.9,58c-12.6,9.2-26.3,16.8-39.6,25.1c-11.1,7-22.6,13.5-33.5,20.8 c-26,17.2-53.2-3.8-53.1-28.5C476.4,367,476.3,351.7,476.4,336.4C476.3,336.4,476.4,336.4,476.4,336.4z M584.7,334.4 c-25.9-16.1-50.3-31.2-74.7-46.4c-2.3,4.9-2.1,92.7,0,96.2c0.3-0.1,0.7,0,1-0.2c23.3-14.4,46.7-28.7,69.9-43.3 C582.7,339.6,583.3,336.8,584.7,334.4z" />
            </>
          )}
        </G>
        <G>
          <Path d="M222.7,687.3c0,1.6,0,3.2,0,4.8c0,0.1,0,0.3,0,0.4c0.1,4.9,0.1,9.8,0,14.7c-0.1,9.1-1.4,16.5-3.7,22.3 c-3.3,8.5-8.8,13.8-16.4,16.2c-3.6,1.1-7.3,1.6-11,1.6c-4.7,0-9.1-0.9-13.4-2.6c-6.8-3.1-11.8-8.6-14.9-16.7 c-1.7-4.8-2.7-9.7-3-14.6c-0.3-5-0.5-9.7-0.5-14.1v-53.8c0-5.3,0.2-10.4,0.7-15.5c0.5-5.1,1.7-10,3.6-14.7 c2.7-6.3,6.5-10.8,11.5-13.7c6.7-3.5,14.5-4.5,23.3-3c6.1,1.1,11,3.8,14.7,8c3.6,4,6.2,9.6,7.6,16.8c0.5,2.8,0.8,5.6,1,8.3 c0.1,2.7,0.2,5.3,0.2,7.8v18.5H200v-17.1c0.1-2.7,0-5.3-0.1-7.9v-0.1c-0.1-2-0.3-4-0.6-6.1c-0.3-2.1-0.9-3.8-1.9-5.3 c-1-1.5-2.4-2.3-4.1-2.6c-1.7-0.3-3.2-0.1-4.6,0.4c-2.5,1-4.2,3.9-5,8.7c-0.4,2.3-0.6,4.5-0.7,6.8c-0.1,2.3-0.1,4.7-0.1,7.1v44.8 c0,2.1,0,4.2,0,6.3c0,4.2,0,8.4,0,12.6c0,4.3,0.2,8,0.7,11c0.2,1.8,0.6,3.5,1.2,5.2c0.6,1.7,1.5,3,2.8,3.9c1.4,0.9,3,1.2,4.7,1 c1.7-0.2,3.1-0.9,4.1-2c2-2.7,3-6.2,3.2-10.5c0.2-2.4,0.3-5.3,0.3-8.6V684h22.7V687.3z" />
          <Path d="M272.2,747.1c-19.9,0-30.6-11.9-32-35.8c-0.2-3.9-0.3-7.7-0.3-11.5l0.1-5.6c0-30.9,0-48.2-0.1-51.9 c0-3.7,0.1-7.5,0.4-11.3c1.8-22.2,12.6-33.2,32.2-33.2c10.8,0.1,18.7,3.3,23.6,9.7c4.6,5.9,7.1,14.3,7.7,25.4 c0.2,3.7,0.3,7.3,0.3,10.9l-0.1,6.1c0,30.3,0,47.2,0.1,50.6c0,4.7-0.2,9.4-0.6,14.1c-1,10.4-3.8,18.2-8.3,23.5 C290,744.1,282.3,747.1,272.2,747.1z M271.9,726.5c0.6,0,1.3-0.1,2.1-0.2c3.5-0.4,5.6-4,6.3-10.8c0.3-3.2,0.5-6.3,0.5-9.4v-70.2 c0-2.1-0.1-4.4-0.3-6.8c-0.4-5-1.7-8.2-4-9.5c-1.4-0.8-2.8-1.2-4.3-1.2c-1.8,0-3.4,0.5-4.8,1.6c-1.9,1.7-3.1,4.2-3.5,7.3 c-0.5,3.1-0.7,6-0.7,8.7c0,44.2,0,67.4-0.1,69.7c0,4.8,0.5,9.5,1.4,14C265.4,724.2,267.9,726.5,271.9,726.5z" />
          <Path d="M419.6,745.5H399c0-40.4,0.1-65.5,0.3-75.4c0.1-2.5,0.2-7,0.2-13.4l0.1-5.6l-17.4,93.7h-18.4l-18.3-94.2 c0.5,29.2,0.8,47.2,0.8,53.8l-0.1,41h-20.6V604.3c0.1-0.5,0.1-2.1,0.1-4.8h28.1l19.3,101.8l18.2-101.8h28.4L419.6,745.5z" />
          <Path d="M538.1,745.5h-20.6c0-40.4,0.1-65.5,0.3-75.4c0.1-2.5,0.2-7,0.2-13.4l0.1-5.6l-17.4,93.7h-18.4l-18.3-94.2 c0.5,29.2,0.8,47.2,0.8,53.8l-0.1,41h-20.6V604.3c0.1-0.5,0.1-2.1,0.1-4.8h28.1l19.3,101.8l18.2-101.8h28.4L538.1,745.5z" />
          <Path d="M591.1,747.3c-18.9,0-29.3-10.4-31-31.4c-0.3-3.8-0.5-7.6-0.5-11.4c0.1-3.2,0.1-38.2,0.1-105.1h23.1c0,64.9,0,98-0.1,99.4 l0.1,10.3c0,5.6,0.4,9.6,1.2,12.1c1.2,3.7,3.7,5.6,7.3,5.6c4.2,0,6.9-1.9,7.9-5.7c0.8-2.9,1.2-7,1.2-12.1V599.5h23.1v109 c0,16-3.8,26.9-11.3,32.7C607,745.3,600,747.3,591.1,747.3z" />
          <Path d="M711.9,745.5h-16.5l-5.7-16.7c-1.6-4.7-3.2-9.4-4.8-14.1c-10.5-30.4-16.8-48.6-18.6-54.4c0,3.1,0.1,6.2,0.3,9.3 c0.6,16.4,0.9,28.7,0.9,37c-0.1,14.4-0.2,27.3-0.2,38.8h-21.4V604.3c0.1-0.5,0.1-2.1,0.1-4.8h18.2c14.9,43.6,24.3,71,28.3,82.1 c-1-24.1-1.5-39.2-1.5-45.3c0.1-6,0.1-18.2,0.1-36.7h21L711.9,745.5z" />
          <Path d="M756.9,745.5h-22.9c0-92.5,0-141.2,0.1-146.1h23C757,689.6,757,738.3,756.9,745.5z" />
          <Path d="M810.3,745.5h-22.9c0-73.8,0.1-114,0.2-120.6l-0.1-4.7h-16.6v-20.8h55.5v20.8h-15.9C810.4,701,810.4,742.7,810.3,745.5z" />
          <Path d="M876.5,745.5h-23.1c0-33,0.1-51.4,0.2-55.1c0-2.4-0.8-6.3-2.4-11.8c-12.3-44.4-18.8-68.3-19.5-71.8l-2-7.4h22.1l13.5,52.1 l12-52.1h22.1c-12,46.2-18.1,69.8-18.5,70.8c-2.9,10.7-4.4,16.9-4.4,18.7l0.1,1.7c0,30.2,0,47.3-0.1,51.5L876.5,745.5z" />
        </G>
      </G>
    </Svg>
  );
};
