export interface ColorScheme {
  base: string;
  background: string;
  foreground: string;
  secondary: string;
  accent: string;
  white: string;
  black: string;
  surface: string;
  highlightSurface: string;
  descriptionSecondary: string;
  adSkipButtonBg: string;
  link: string;
  separator: string;
}

export interface ThemeColors {
  light: ColorScheme;
  dark: ColorScheme;
}

export const COLORS: ThemeColors = {
  light: {
    base: '#EDEDED',
    background: '#FFFFFF',
    foreground: '#000000',
    secondary: '#444444',
    accent: '#C41208',
    white: '#FFF',
    black: '#000000',
    surface: '#F5F5F5',
    highlightSurface: '#FFFFFF',
    descriptionSecondary: '#6C6C6C',
    adSkipButtonBg: '#D3D3D3',
    link: '#0086D4',
    separator: '#e4e4e4',
  },
  dark: {
    base: '#161616',
    background: '#000000',
    foreground: '#FFFFFF',
    secondary: '#6C6C6C',
    accent: '#C41208',
    white: '#FFFFFF',
    black: '#000000',
    surface: '#0D0D0D',
    highlightSurface: '#161616',
    descriptionSecondary: '#6C6C6C',
    adSkipButtonBg: '#686666',
    link: '#0086D4',
    separator: '#262626',
  },
};

export const THEME_MODE = {
  light: 'light',
  dark: 'dark',
};
