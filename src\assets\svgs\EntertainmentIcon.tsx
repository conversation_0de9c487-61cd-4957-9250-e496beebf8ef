import * as React from 'react';
import Svg, { ClipP<PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const EntertainmentIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      clipPath="url(#a)">
      <Path d="M17.938 6.75v9.563a1.125 1.125 0 0 1-1.125 1.125H2.188a1.125 1.125 0 0 1-1.126-1.125V6.75M17.938 4.5H1.063V1.687A1.125 1.125 0 0 1 2.188.563h14.624a1.125 1.125 0 0 1 1.125 1.125V4.5ZM6.125 4.5 7.25.562M11.75 4.5 12.875.562" />
      <Path d="M7.616 13.995a.642.642 0 0 1-.928-.574V9.079a.642.642 0 0 1 .928-.574l4.342 2.171a.64.64 0 0 1 0 1.148l-4.342 2.171Z" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path d="M.5 0h18v18H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
