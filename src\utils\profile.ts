import { ISocialIcons } from '../screens/Profile/components/SocialIconsModal';

type ConvertedData = {
  [key: string]: string;
};
export const convertSocialMediaData = (data: ISocialIcons): ConvertedData => {
  const result: ConvertedData = {};

  for (const platform in data) {
    if (data.hasOwnProperty(platform)) {
      const { url, isVisible } = data[platform as keyof ISocialIcons];
      result[platform] = url;
      result[`is_${platform}`] = isVisible ? '1' : '0';
    }
  }

  return result;
};
