import React, { useMemo, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import {
  KeyboardAvoidingView,
  KeyboardTypeOptions,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { yupResolver } from '@hookform/resolvers/yup';
import { useNavigation } from '@react-navigation/native';
import dayjs from 'dayjs';

import DarkLogo from '../../../assets/svgs/DarkLogo';
import Logo from '../../../assets/svgs/Logo';
import CustomButton from '../../../components/common/button';
import Input from '../../../components/common/input';
import CustomText from '../../../components/common/text';
import { SIGNUP, SIGNUP_FIELDS } from '../../../constants/auth';
import { ColorScheme, THEME_MODE } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { setUser } from '../../../redux/slices/authSlice';
import { signupUser } from '../../../services/authServervice';
import { EFieldType } from '../../../types';
import { APP_ROUTES, NavigationProp, STACKS } from '../../../types/routes';
import { openInAppBrowser } from '../../../utils/media';
import { signupValidationSchema } from '../../../utils/validationSchema';
import AuthTab from '../components/AuthTab';

interface ISignupFields {
  username: string;
  email: string;
  birthday: Date;
  password: string;
  confirm_password: string;
}

const minAge = 17;
const maximumDate = dayjs().subtract(minAge, 'year').toDate();

const INITIAL_VALUES: ISignupFields = {
  username: '',
  email: '',
  birthday: maximumDate,
  password: '',
  confirm_password: '',
};

const Signup = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ISignupFields>({
    mode: 'onSubmit',
    resolver: yupResolver(signupValidationSchema),
    defaultValues: INITIAL_VALUES,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const { colors, theme } = useAppSelector(state => state.theme);
  const dispatch = useAppDispatch();

  const navigation = useNavigation<NavigationProp>();

  const styles = useMemo(() => createStyles(colors), [colors]);

  const onSubmit: SubmitHandler<ISignupFields> = async data => {
    setIsLoading(true);

    const response = await signupUser({
      ...data,
      birthday: data.birthday.toString(),
    });
    if (response) {
      const { access_token, user_id: userId } = response;
      dispatch(setUser({ access_token, userId }));
      navigation.navigate(STACKS.APP, { screen: APP_ROUTES.HOME });
    }
    setIsLoading(false);
  };

  return (
    <KeyboardAvoidingView style={styles.container} enabled behavior="padding">
      <SafeAreaView>
        <ScrollView
          keyboardShouldPersistTaps="always"
          showsVerticalScrollIndicator={false}>
          <View style={styles.logoWrapper}>
            {theme === THEME_MODE.dark ? <DarkLogo /> : <Logo />}
          </View>
          <AuthTab />
          <View style={styles.fieldsWrapper}>
            {SIGNUP_FIELDS.slice(0, 3).map(field => (
              <Controller
                key={field.name}
                control={control}
                name={field.name as keyof ISignupFields}
                render={({ field: { onChange, value, name } }) => {
                  if (field.type === EFieldType.DATE) {
                    return (
                      <>
                        <DatePicker
                          modal
                          open={showDatePicker}
                          onConfirm={(date: Date) => {
                            onChange(date);
                            setShowDatePicker(false);
                          }}
                          onCancel={() => setShowDatePicker(false)}
                          date={value as Date}
                          mode="date"
                          maximumDate={maximumDate}
                        />
                        <Pressable onPress={() => setShowDatePicker(true)}>
                          <Input
                            name={name}
                            value={dayjs(value as Date).format('MM/DD/YYYY')}
                            placeholder={field.placeholder}
                            style={{
                              backgroundColor: colors.base,
                              color: colors.foreground,
                            }}
                            disabled
                            errors={errors}
                          />
                        </Pressable>
                      </>
                    );
                  }

                  return (
                    <Input
                      name={name}
                      value={value as string}
                      placeholder={field.placeholder}
                      keyboardType={field.keyboardType as KeyboardTypeOptions}
                      onChangeText={_value => onChange(_value)}
                      errors={errors}
                      autoCapitalize="none"
                    />
                  );
                }}
              />
            ))}
            <CustomText style={styles.signupInstruction}>
              {SIGNUP.instruction}
            </CustomText>
            <View style={{ marginTop: verticalScale(11) }}>
              {SIGNUP_FIELDS.slice(3).map(field => (
                <Controller
                  key={field.name}
                  control={control}
                  name={field.name as keyof ISignupFields}
                  render={({ field: { onChange, value, name } }) => (
                    <Input
                      name={name}
                      value={value as string}
                      placeholder={field.placeholder}
                      secureTextEntry={field.secureTextEntry}
                      onChangeText={_value => onChange(_value)}
                      errors={errors}
                    />
                  )}
                />
              ))}
            </View>
          </View>
          <View>
            <CustomText style={styles.agreeText}>
              {SIGNUP.agree}
              <CustomText
                style={styles.toc}
                onPress={() => {
                  openInAppBrowser(SIGNUP.terms.url);
                }}>
                {SIGNUP.terms.content}
              </CustomText>{' '}
              &{' '}
              <CustomText
                style={styles.toc}
                onPress={() => {
                  openInAppBrowser(SIGNUP.policy.url);
                }}>
                {SIGNUP.policy.content}
              </CustomText>
            </CustomText>
          </View>
          <View style={styles.btnWrapper}>
            <CustomButton
              title={SIGNUP.submitBtn}
              onPress={handleSubmit(onSubmit)}
              loading={isLoading}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default Signup;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(20),
    },
    logoWrapper: {
      alignItems: 'center',
      marginTop: Platform.OS === 'android' ? verticalScale(24) : 0,
      marginVertical: verticalScale(10),
    },
    fieldsWrapper: {
      marginTop: verticalScale(18),
    },
    signupInstruction: {
      color: colors.foreground,
      fontSize: moderateScale(12),
    },
    agreeText: {
      color: colors.foreground,
      fontSize: moderateScale(12),
      textAlign: 'center',
      marginTop: verticalScale(16),
    },
    toc: {
      color: colors.accent,
    },
    btnWrapper: {
      marginVertical: verticalScale(20),
    },
  });
