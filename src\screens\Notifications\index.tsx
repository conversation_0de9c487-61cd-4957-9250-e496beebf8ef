import { FC, useEffect, useMemo } from 'react';
import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native';
import { verticalScale } from 'react-native-size-matters';
import { SwipeListView } from 'react-native-swipe-list-view';
import { ParamListBase } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { DeleteIcon } from '../../assets/svgs';
import { OfflineView } from '../../components/OfflineView';
import { NotificationsSkeleton } from '../../components/skeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import {
  deleteNotification,
  getNotifications,
  seenNotification,
} from '../../redux/slices/notificationsSlice';

import { NotificationListItem } from './components';

const { width } = Dimensions.get('window');

interface NotificationsProps {
  navigation: StackNavigationProp<ParamListBase, string, undefined>;
}

const Notifications: FC<NotificationsProps> = ({}) => {
  const { isConnected } = useConnectionStatus();

  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { notifications, status } = useAppSelector(
    state => state.notifications,
  );

  const handleDelete = (id: string) => {
    dispatch(deleteNotification(id));
  };

  useEffect(() => {
    const unseenNotificationsIds = notifications
      .filter(notification => Number(notification.seen) <= 0)
      .map(notification => notification.id)
      .toString();
    if (unseenNotificationsIds) {
      dispatch(seenNotification(unseenNotificationsIds));
    } else {
      dispatch(getNotifications());
    }
  }, []);

  if (status === 'loading') {
    return <NotificationsSkeleton />;
  }
  if (!isConnected) return <OfflineView />;

  return (
    <View style={styles.container}>
      <SwipeListView
        data={notifications?.filter(
          notification => notification.type === 'reaction',
        )}
        disableRightSwipe
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        renderItem={data => {
          return <NotificationListItem {...data.item} />;
        }}
        renderHiddenItem={({ item }) => (
          <Pressable
            style={styles.deleteButton}
            onPress={() => handleDelete(item.id)}>
            <DeleteIcon stroke={colors.white} />
          </Pressable>
        )}
        rightOpenValue={-55}
        // eslint-disable-next-line react/no-unstable-nested-components
        ListEmptyComponent={() => (
          <Text style={styles.noNotification}>No notifications yet.</Text>
        )}
      />
    </View>
  );
};

export default Notifications;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      width: '100%',
      backgroundColor: colors.background,
    },
    deleteButton: {
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      backgroundColor: colors.accent,
      width: width * 0.14,
      marginVertical: verticalScale(6),
    },
    deleteButtonText: {
      color: '#fff',
      fontWeight: 'bold',
    },
    noNotification: {
      color: colors.foreground,
      fontSize: 16,
      textAlign: 'center',
      marginTop: 20,
    },
  });
