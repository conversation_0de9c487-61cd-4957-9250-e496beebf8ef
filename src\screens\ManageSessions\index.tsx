import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { FlatList, Text, View } from 'react-native';

import CustomButton from '../../components/common/button';
import AlertModal from '../../components/common/Modal';
import { ManagedSessionsSkeleton } from '../../components/skeleton';
import { MANAGE_SESSIONS_TEXT } from '../../constants/manageSessions';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { clearUser } from '../../redux/slices/authSlice';
import {
  deleteSession,
  getAllSessions,
} from '../../services/api/sessionsService';
import { SessionData } from '../../types/manageSessions';
import { clearAS } from '../../utils/storage';

import SessionCard from './components/SessionCard';
import { createStyles } from './styles';

const ManageSessions = () => {
  const dispatch = useAppDispatch();

  const [sessions, setSessions] = useState<SessionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { user } = useAppSelector(state => state.auth);

  const fetchSessions = useCallback(async () => {
    setLoading(true);
    const data = await getAllSessions();
    if (data) {
      setSessions(data);
    }
    setLoading(false);
  }, []);

  const handleDeleteSession = useCallback(
    async (item: SessionData) => {
      const success = await deleteSession({ type: 'delete', id: item?.id });
      if (success) {
        setSessions(prevSessions =>
          prevSessions.filter(session => session.id !== item?.id),
        );
      }

      if (item.session_id === user?.access_token) {
        clearAS();
        dispatch(clearUser());
      }
    },
    [sessions],
  );

  const deleteAllSessions = useCallback(async () => {
    setDeleteLoading(true);
    await Promise.all(
      sessions.map(session =>
        deleteSession({ type: 'delete', id: session.id }),
      ),
    );
    setSessions([]);
    setDeleteLoading(false);
    clearAS();
    dispatch(clearUser());
  }, [sessions]);

  const handleDeleteAll = () => setIsVisible(true);

  useEffect(() => {
    fetchSessions();
  }, [fetchSessions]);

  if (loading) return <ManagedSessionsSkeleton />;

  return (
    <View style={styles.container}>
      <FlatList
        data={sessions}
        keyExtractor={({ session_id }) => session_id}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => (
          <SessionCard item={item} onDeleteSession={handleDeleteSession} />
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {MANAGE_SESSIONS_TEXT.noSessions}
            </Text>
          </View>
        }
        ListFooterComponent={
          sessions.length > 0 ? (
            <View style={styles.footer}>
              <CustomButton
                title="Logout from all sessions"
                onPress={handleDeleteAll}
                loading={deleteLoading}
              />
            </View>
          ) : null
        }
      />
      <AlertModal
        title="Delete Sessions"
        message="Are you sure you want to delete all sessions?"
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        onConfirm={deleteAllSessions}
        confirmText="OK"
        cancelText="Cancel"
      />
    </View>
  );
};

export default ManageSessions;
