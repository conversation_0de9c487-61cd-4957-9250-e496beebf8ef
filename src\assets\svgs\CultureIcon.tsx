import * as React from 'react';
import Svg, { Mask, Path, SvgProps } from 'react-native-svg';

export const CultureIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Mask
      id="a"
      width={26}
      height={20}
      x={-0.5}
      y={-0.322}
      fill="#000"
      maskUnits="userSpaceOnUse">
      <Path fill="#fff" d="M-.5-.322h26v20h-26z" />
      <Path
        fillRule="evenodd"
        d="M10.656 7.752a.276.276 0 1 1 0-.552.276.276 0 0 1 0 .552Zm0-1.28c-.553 0-1.003.45-1.003 1.003 0 .553.45 1.003 1.003 1.003.553 0 1.003-.45 1.003-1.003a1.006 1.006 0 0 0-1.003-1.003Zm3.69 1.28a.276.276 0 1 1 0-.552.276.276 0 0 1 0 .552Zm0-1.28c-.553 0-1.003.45-1.003 1.003a1.004 1.004 0 1 0 1.003-1.003Zm3.151 2.822a.277.277 0 1 1 .002-.554.277.277 0 0 1-.002.554Zm0-1.279c-.553 0-1.003.45-1.003 1.003 0 .553.45 1.003 1.003 1.003a1.002 1.002 0 1 0 0-2.006Zm-9.993 1.28a.277.277 0 1 1 .002-.554.277.277 0 0 1-.002.553Zm0-1.28c-.553 0-1.003.45-1.003 1.003a1.005 1.005 0 0 0 2.007 0c0-.554-.451-1.003-1.004-1.003Zm13.21 1.196a9.81 9.81 0 0 0-.573-.768l2.34-.325-1.767 1.093Zm-6.118.846a5.067 5.067 0 0 1 2.047 1.673l-3.44 2.128 1.393-3.801ZM12.5 18.595a3.849 3.849 0 0 1-3.088-1.536l3.088-1.91 3.09 1.91a3.85 3.85 0 0 1-3.09 1.536Zm-4.142-6.866a5.092 5.092 0 0 1 2.047-1.673l1.392 3.8L8.36 11.73ZM2.521 8.12l2.34.324a9.81 9.81 0 0 0-.573.768L2.521 8.118ZM3.914 4.54c.164-.139.331-.273.501-.402l.71 3.607-3.643-.507a13.408 13.408 0 0 1 2.432-2.698Zm10.72-2.964L12.5 4.567l-2.133-2.991a13.425 13.425 0 0 1 4.265 0ZM7.963 5.93a9.897 9.897 0 0 0-2.16 1.513l-.628-3.202 2.789 1.69ZM9.92 2.2l-.688 3.188c.83-.29 1.7-.47 2.582-.531L9.92 2.199ZM8.484 5.397l.777-3.594a13.227 13.227 0 0 0-3.92 1.688l3.143 1.906Zm7.287-.01-.688-3.188-1.893 2.656c.882.062 1.75.242 2.581.532Zm.745.01-.775-3.593c1.371.343 2.696.906 3.92 1.688l-3.145 1.905Zm.869.722a9.97 9.97 0 0 1 1.811 1.325l.63-3.202-2.789 1.69c.117.06.233.122.348.187ZM12.5 13.663l1.412-3.855a5.125 5.125 0 0 0-2.823 0l1.41 3.855Zm5.507-2.778 2.087-1.29a9.367 9.367 0 0 0-.936-1.17l-.018-.021a9.1 9.1 0 0 0-2.952-2.078.309.309 0 0 1-.116-.05 9.168 9.168 0 0 0-3.571-.718c-1.252 0-2.463.25-3.572.719a.311.311 0 0 1-.115.05 9.064 9.064 0 0 0-2.998 2.125c-.33.353-.633.735-.909 1.143l2.087 1.291.745.46a5.802 5.802 0 0 1 4.762-2.46c1.913 0 3.667.913 4.762 2.46l.744-.461Zm3.694-5.788c.289.281.464.45.768.793.377.425.729.878 1.05 1.35l-3.643.506.71-3.607c.333.256.658.53.973.82l.142.138Zm2.743 2.232a14.138 14.138 0 0 0-1.432-1.922 13.01 13.01 0 0 0-.805-.832l-.144-.14-.007-.006a13.98 13.98 0 0 0-6.663-3.45.346.346 0 0 0-.115-.024 14.103 14.103 0 0 0-5.564 0 .438.438 0 0 0-.052.007.441.441 0 0 0-.054.016 13.962 13.962 0 0 0-6.166 3.008A14.095 14.095 0 0 0 .555 7.33a.366.366 0 0 0 .118.502l3.534 2.186 3.448 2.133 4.154 2.57-3.104 1.921a.364.364 0 0 0-.118.5 4.576 4.576 0 0 0 3.913 2.18 4.58 4.58 0 0 0 3.914-********** 0 0 0 .045-.274.358.358 0 0 0-.163-.226l-3.104-1.92 4.153-2.571 3.448-2.133 3.535-2.187a.368.368 0 0 0 .117-.502Z"
        clipRule="evenodd"
      />
    </Mask>
    <Path
      fill="#000"
      fillRule="evenodd"
      d="M10.656 7.752a.276.276 0 1 1 0-.552.276.276 0 0 1 0 .552Zm0-1.28c-.553 0-1.003.45-1.003 1.003 0 .553.45 1.003 1.003 1.003.553 0 1.003-.45 1.003-1.003a1.006 1.006 0 0 0-1.003-1.003Zm3.69 1.28a.276.276 0 1 1 0-.552.276.276 0 0 1 0 .552Zm0-1.28c-.553 0-1.003.45-1.003 1.003a1.004 1.004 0 1 0 1.003-1.003Zm3.151 2.822a.277.277 0 1 1 .002-.554.277.277 0 0 1-.002.554Zm0-1.279c-.553 0-1.003.45-1.003 1.003 0 .553.45 1.003 1.003 1.003a1.002 1.002 0 1 0 0-2.006Zm-9.993 1.28a.277.277 0 1 1 .002-.554.277.277 0 0 1-.002.553Zm0-1.28c-.553 0-1.003.45-1.003 1.003a1.005 1.005 0 0 0 2.007 0c0-.554-.451-1.003-1.004-1.003Zm13.21 1.196a9.81 9.81 0 0 0-.573-.768l2.34-.325-1.767 1.093Zm-6.118.846a5.067 5.067 0 0 1 2.047 1.673l-3.44 2.128 1.393-3.801ZM12.5 18.595a3.849 3.849 0 0 1-3.088-1.536l3.088-1.91 3.09 1.91a3.85 3.85 0 0 1-3.09 1.536Zm-4.142-6.866a5.092 5.092 0 0 1 2.047-1.673l1.392 3.8L8.36 11.73ZM2.521 8.12l2.34.324a9.81 9.81 0 0 0-.573.768L2.521 8.118ZM3.914 4.54c.164-.139.331-.273.501-.402l.71 3.607-3.643-.507a13.408 13.408 0 0 1 2.432-2.698Zm10.72-2.964L12.5 4.567l-2.133-2.991a13.425 13.425 0 0 1 4.265 0ZM7.963 5.93a9.897 9.897 0 0 0-2.16 1.513l-.628-3.202 2.789 1.69ZM9.92 2.2l-.688 3.188c.83-.29 1.7-.47 2.582-.531L9.92 2.199ZM8.484 5.397l.777-3.594a13.227 13.227 0 0 0-3.92 1.688l3.143 1.906Zm7.287-.01-.688-3.188-1.893 2.656c.882.062 1.75.242 2.581.532Zm.745.01-.775-3.593c1.371.343 2.696.906 3.92 1.688l-3.145 1.905Zm.869.722a9.97 9.97 0 0 1 1.811 1.325l.63-3.202-2.789 1.69c.117.06.233.122.348.187ZM12.5 13.663l1.412-3.855a5.125 5.125 0 0 0-2.823 0l1.41 3.855Zm5.507-2.778 2.087-1.29a9.367 9.367 0 0 0-.936-1.17l-.018-.021a9.1 9.1 0 0 0-2.952-2.078.309.309 0 0 1-.116-.05 9.168 9.168 0 0 0-3.571-.718c-1.252 0-2.463.25-3.572.719a.311.311 0 0 1-.115.05 9.064 9.064 0 0 0-2.998 2.125c-.33.353-.633.735-.909 1.143l2.087 1.291.745.46a5.802 5.802 0 0 1 4.762-2.46c1.913 0 3.667.913 4.762 2.46l.744-.461Zm3.694-5.788c.289.281.464.45.768.793.377.425.729.878 1.05 1.35l-3.643.506.71-3.607c.333.256.658.53.973.82l.142.138Zm2.743 2.232a14.138 14.138 0 0 0-1.432-1.922 13.01 13.01 0 0 0-.805-.832l-.144-.14-.007-.006a13.98 13.98 0 0 0-6.663-3.45.346.346 0 0 0-.115-.024 14.103 14.103 0 0 0-5.564 0 .438.438 0 0 0-.052.007.441.441 0 0 0-.054.016 13.962 13.962 0 0 0-6.166 3.008A14.095 14.095 0 0 0 .555 7.33a.366.366 0 0 0 .118.502l3.534 2.186 3.448 2.133 4.154 2.57-3.104 1.921a.364.364 0 0 0-.118.5 4.576 4.576 0 0 0 3.913 2.18 4.58 4.58 0 0 0 3.914-********** 0 0 0 .045-.274.358.358 0 0 0-.163-.226l-3.104-1.92 4.153-2.571 3.448-2.133 3.535-2.187a.368.368 0 0 0 .117-.502Z"
      clipRule="evenodd"
    />
    <Path
      stroke="#000"
      strokeWidth={0.3}
      d="M10.656 7.752a.276.276 0 1 1 0-.552.276.276 0 0 1 0 .552Zm0-1.28c-.553 0-1.003.45-1.003 1.003 0 .553.45 1.003 1.003 1.003.553 0 1.003-.45 1.003-1.003a1.006 1.006 0 0 0-1.003-1.003Zm3.69 1.28a.276.276 0 1 1 0-.552.276.276 0 0 1 0 .552Zm0-1.28c-.553 0-1.003.45-1.003 1.003a1.004 1.004 0 1 0 1.003-1.003Zm3.151 2.822a.277.277 0 1 1 .002-.554.277.277 0 0 1-.002.554Zm0-1.279c-.553 0-1.003.45-1.003 1.003 0 .553.45 1.003 1.003 1.003a1.002 1.002 0 1 0 0-2.006Zm-9.993 1.28a.277.277 0 1 1 .002-.554.277.277 0 0 1-.002.553Zm0-1.28c-.553 0-1.003.45-1.003 1.003a1.005 1.005 0 0 0 2.007 0c0-.554-.451-1.003-1.004-1.003Zm13.21 1.196a9.81 9.81 0 0 0-.573-.768l2.34-.325-1.767 1.093Zm-6.118.846a5.067 5.067 0 0 1 2.047 1.673l-3.44 2.128 1.393-3.801ZM12.5 18.595a3.849 3.849 0 0 1-3.088-1.536l3.088-1.91 3.09 1.91a3.85 3.85 0 0 1-3.09 1.536Zm-4.142-6.866a5.092 5.092 0 0 1 2.047-1.673l1.392 3.8L8.36 11.73ZM2.521 8.12l2.34.324a9.81 9.81 0 0 0-.573.768L2.521 8.118ZM3.914 4.54c.164-.139.331-.273.501-.402l.71 3.607-3.643-.507a13.408 13.408 0 0 1 2.432-2.698Zm10.72-2.964L12.5 4.567l-2.133-2.991a13.425 13.425 0 0 1 4.265 0ZM7.963 5.93a9.897 9.897 0 0 0-2.16 1.513l-.628-3.202 2.789 1.69ZM9.92 2.2l-.688 3.188c.83-.29 1.7-.47 2.582-.531L9.92 2.199ZM8.484 5.397l.777-3.594a13.227 13.227 0 0 0-3.92 1.688l3.143 1.906Zm7.287-.01-.688-3.188-1.893 2.656c.882.062 1.75.242 2.581.532Zm.745.01-.775-3.593c1.371.343 2.696.906 3.92 1.688l-3.145 1.905Zm.869.722a9.97 9.97 0 0 1 1.811 1.325l.63-3.202-2.789 1.69c.117.06.233.122.348.187ZM12.5 13.663l1.412-3.855a5.125 5.125 0 0 0-2.823 0l1.41 3.855Zm5.507-2.778 2.087-1.29a9.367 9.367 0 0 0-.936-1.17l-.018-.021a9.1 9.1 0 0 0-2.952-2.078.309.309 0 0 1-.116-.05 9.168 9.168 0 0 0-3.571-.718c-1.252 0-2.463.25-3.572.719a.311.311 0 0 1-.115.05 9.064 9.064 0 0 0-2.998 2.125c-.33.353-.633.735-.909 1.143l2.087 1.291.745.46a5.802 5.802 0 0 1 4.762-2.46c1.913 0 3.667.913 4.762 2.46l.744-.461Zm3.694-5.788c.289.281.464.45.768.793.377.425.729.878 1.05 1.35l-3.643.506.71-3.607c.333.256.658.53.973.82l.142.138Zm2.743 2.232a14.138 14.138 0 0 0-1.432-1.922 13.01 13.01 0 0 0-.805-.832l-.144-.14-.007-.006a13.98 13.98 0 0 0-6.663-3.45.346.346 0 0 0-.115-.024 14.103 14.103 0 0 0-5.564 0 .438.438 0 0 0-.052.007.441.441 0 0 0-.054.016 13.962 13.962 0 0 0-6.166 3.008A14.095 14.095 0 0 0 .555 7.33a.366.366 0 0 0 .118.502l3.534 2.186 3.448 2.133 4.154 2.57-3.104 1.921a.364.364 0 0 0-.118.5 4.576 4.576 0 0 0 3.913 2.18 4.58 4.58 0 0 0 3.914-********** 0 0 0 .045-.274.358.358 0 0 0-.163-.226l-3.104-1.92 4.153-2.571 3.448-2.133 3.535-2.187a.368.368 0 0 0 .117-.502Z"
      clipRule="evenodd"
      mask="url(#a)"
    />
  </Svg>
);
