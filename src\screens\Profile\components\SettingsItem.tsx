import React, { useMemo, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import EntypoIcon from 'react-native-vector-icons/Entypo';

import AlertModal from '../../../components/common/Modal';
import CustomText from '../../../components/common/text';
import ToggleButton from '../../../components/ToggleButton';
import { VERIFICATION_LINK } from '../../../constants/profile';
import { ColorScheme, THEME_MODE } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { useThemeManager } from '../../../hooks/useThemeManager';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import { clearUser } from '../../../redux/slices/authSlice';
import { APP_ROUTES } from '../../../types/routes';
import { openInAppBrowser } from '../../../utils/media';
import { clearAS } from '../../../utils/storage';

interface Props {
  title: string;
  icon: React.ReactNode;
  isToggle?: boolean;
  route?: string;
  isLogout?: boolean;
  isVerification?: boolean;
}

const SettingsItem: React.FC<Props> = ({
  title,
  icon,
  isToggle,
  route,
  isLogout,
  isVerification,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const { colors, theme } = useAppSelector(state => state.theme);
  const { setTheme } = useThemeManager();
  const styles = useMemo(() => createStyles(colors), [colors]);
  const dispatch = useAppDispatch();
  const navigation = useTypeSafeNavigation();

  const toggleTheme = () => {
    setTheme(theme === THEME_MODE.light ? 'dark' : 'light');
  };

  const handleNavigation = () => {
    if (route) navigation.navigate(route as APP_ROUTES.Dashboard);
  };

  const handleLogout = () => {
    clearAS();
    dispatch(clearUser());
  };

  const handleOnClick = () => {
    if (isVerification) {
      openInAppBrowser(VERIFICATION_LINK);
    } else if (isLogout) {
      setIsVisible(true);
    } else {
      handleNavigation();
    }
  };

  return (
    <>
      <Pressable onPress={handleOnClick} style={styles.container}>
        <View style={styles.wrapper}>
          <View style={{ width: moderateScale(20) }}>{icon}</View>
          <CustomText style={styles.title}>{title}</CustomText>
        </View>
        {isToggle ? (
          <ToggleButton
            isEnabled={theme === THEME_MODE.dark}
            toggleSwitch={toggleTheme}
          />
        ) : (
          <EntypoIcon
            name="chevron-small-right"
            size={moderateScale(24)}
            color={colors.secondary}
          />
        )}
      </Pressable>
      <AlertModal
        isVisible={isVisible}
        onConfirm={handleLogout}
        onCancel={() => setIsVisible(false)}
        title="Logout"
        message="Are you sure you want to log out?"
        confirmText="Logout"
        cancelText="Cancel"
      />
    </>
  );
};

export default SettingsItem;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: moderateScale(16),
      borderBottomWidth: 0.2,
      borderColor: colors.secondary,
      alignItems: 'center',
    },
    wrapper: {
      flexDirection: 'row',
      gap: moderateScale(4),
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(14),
      fontWeight: '400',
    },
  });
