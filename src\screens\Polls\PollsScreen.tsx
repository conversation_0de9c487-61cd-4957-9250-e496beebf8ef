import React, { useCallback, useMemo, useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, SafeAreaView, ListRenderItem, Pressable, Alert, RefreshControl } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import CustomText from '../../components/common/text';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { Poll } from '../../types/polls';
import PollCard from './components/PollCard';
import PollsSkeleton from './components/PollsSkeleton';
import FeatherIcon from 'react-native-vector-icons/Feather';
import { QuestionNavIcon } from '../../assets/svgs';
import { fetchPolls, voteOnPoll, deletePoll, clearCreatePollStatus } from '../../redux/slices/pollsSlice';
import { StackNavigationProp } from '@react-navigation/stack';
import { APP_ROUTES, AppStackNavigatorParamList } from '../../types/routes';
import { reportPollService } from '../../services/api/pollsService';
import Toast from 'react-native-toast-message';
import { TOAST_TYPE, TOAST_TITLE } from '../../constants/toast';


interface PollsScreenProps {
  navigation: StackNavigationProp<AppStackNavigatorParamList, APP_ROUTES.POLLS>;
}

const PollsScreen: React.FC<PollsScreenProps> = ({ navigation }) => {
  const { colors, theme } = useAppSelector(state => state.theme);
  const { polls, status, error, hasMore, voteStatus } = useAppSelector(state => state.polls);
  const { user } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const styles = useMemo(() => createStyles(colors, theme), [colors, theme ]);
  const [refreshing, setRefreshing] = useState(false);
  const [votingPollId, setVotingPollId] = useState<string | null>(null);
  const [votingOptionId, setVotingOptionId] = useState<string | null>(null);

  // Fetch polls on component mount - only if no data exists
  useEffect(() => {
    // Capture current polls length at mount time to avoid reactive dependencies
    const currentPollsCount = polls.length;
    // Only fetch if we don't have any polls data yet
    if (currentPollsCount === 0) {
      dispatch(fetchPolls({ action: 'all', refresh: false }));
    }
  }, [dispatch]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle pull to refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(fetchPolls({ action: 'all', refresh: true }))
      .finally(() => setRefreshing(false));
  }, []);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (status !== 'loading' && hasMore) {
      dispatch(fetchPolls({ action: 'all', refresh: false }));
    }
  }, [dispatch, status, hasMore]);

  const handleOptionPress = async (option: any, pollId: string) => {
    // Prevent multiple rapid clicks - check if already voting
    if (voteStatus === 'loading' || votingPollId === pollId) {
      return;
    }
    
    // Find the poll to check if it's active
    const poll = polls.find(p => p.id === pollId);
    
    // Prevent voting if poll is closed (pollActive = true)
    if (poll?.pollActive) {
      Toast.show({
        type: TOAST_TYPE.ERROR,
        text1: 'Poll Closed',
        text2: 'This poll is no longer accepting votes',
      });
      return;
    }
    
    // Prevent voting if user has already voted
    if (poll?.isVoted) {
      Toast.show({
        type: TOAST_TYPE.ERROR,
        text1: 'Already Voted',
        text2: 'You have already voted on this poll',
      });
      return;
    }
    
    // Set voting state to prevent multiple clicks
    setVotingPollId(pollId);
    setVotingOptionId(option.id);
    
    const result = await dispatch(voteOnPoll({ pollId, optionId: option.id }));
    
    // Clear voting state
    setVotingPollId(null);
    setVotingOptionId(null);
    
    // If vote was successful, refresh polls to get updated vote counts
    if (voteOnPoll.fulfilled.match(result)) {
      dispatch(fetchPolls({ action: 'all', refresh: true }));
    }
  };

  const handleDeletePress = (pollId: string) => {
    Alert.alert(
      'Delete Poll',
      'Are you sure you want to delete this poll?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            dispatch(deletePoll(pollId));
          },
        },
      ]
    );
  };

  const handleReportPress = async (pollId: string) => {
    const success = await reportPollService(pollId);
    if (success) {
      Toast.show({ type: 'success', text1: 'Poll reported successfully.' });
      // Refresh polls to get updated data
      dispatch(fetchPolls({ action: 'all', refresh: true }));
    } else {
      Toast.show({ type: 'error', text1: 'Failed to report poll.' });
    }
  };

  const renderPollCard: ListRenderItem<Poll> = useCallback(({ item }) => (
    <PollCard 
      poll={item} 
      onOptionPress={(option) => handleOptionPress(option, item.id)}
      onDeletePress={handleDeletePress}
      onReportPress={handleReportPress}
      votingOptionId={voteStatus === 'loading' && votingPollId === item.id ? votingOptionId : null}
    />
  ), [polls, voteStatus, votingPollId, votingOptionId]);

  const handleOpenCreatePoll = () => {
    navigation.navigate(APP_ROUTES.CREATE_POLL, {
      isEditMode: false,
    });
  };

  if (status === 'loading' && polls.length === 0) {
    return <PollsSkeleton />;
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <FlatList
        data={polls}
        renderItem={renderPollCard}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.foreground}
            colors={[colors.foreground]}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={
          polls.length === 0 && status === 'succeeded' ? (
            <CustomText style={{ textAlign: 'center', marginTop: 32, color: colors.foreground }}>
              No polls found.
            </CustomText>
          ) : null
        }
      />
      {/* Floating Question Mark Icon */}
      <Pressable style={styles.fab} onPress={handleOpenCreatePoll} accessibilityLabel="Create Poll">
        <QuestionNavIcon width={50} height={50} stroke={colors.foreground} strokeWidth={1.5} />
      </Pressable>
    </SafeAreaView>
  );
};

const createStyles = (colors: any, theme: string) =>
  StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: moderateScale(12),
    },
    fab: {
      position: 'absolute',
      right: moderateScale(18),
      bottom: moderateScale(24),
      backgroundColor: theme === 'dark' ? colors.descriptionSecondary : colors.base,
      borderRadius: moderateScale(28),
      width: moderateScale(52),
      height: moderateScale(52),
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 8,
      shadowColor: colors.black,
      shadowOffset: { width: 4, height: 4 },
      shadowOpacity: 0.7,
      shadowRadius: 50,
      zIndex: 100,
    },

  });

export default PollsScreen; 