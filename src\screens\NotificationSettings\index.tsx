import React, { useMemo, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Text, View } from 'react-native';

import CustomButton from '../../components/common/button';
import Checkbox from '../../components/common/checkbox';
import {
  NOTIF_SETTINGS_FIELDS,
  NOTIFICATION_TEXT,
} from '../../constants/notificationSettings';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { fetchUserDetails } from '../../redux/slices/authSlice';
import { updateUserData } from '../../services/api/userService';

import { createStyles } from './styles';

interface INotificationSettingsFields {
  e_liked: boolean;
  e_followed: boolean;
}

const NotificationSettings = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { colors } = useAppSelector(state => state.theme);
  const { userDetails, user } = useAppSelector(state => state.auth);
  const navigation = useTypeSafeNavigation();
  const dispatch = useAppDispatch();

  const styles = useMemo(() => createStyles(colors), [colors]);

  const { control, handleSubmit } = useForm<INotificationSettingsFields>({
    mode: 'onSubmit',
    defaultValues: {
      e_liked: userDetails?.e_liked === '1',
      e_followed: userDetails?.e_followed === '1',
    },
  });

  const onSave: SubmitHandler<INotificationSettingsFields> = async data => {
    const formattedData = {
      e_liked: data.e_liked ? 1 : 0,
      e_followed: data.e_followed ? 1 : 0,
    };
    setIsLoading(true);
    const response = await updateUserData(formattedData);
    if (response) {
      dispatch(
        fetchUserDetails({
          data: { user_id: user?.userId as string, fetch: 'user_data' },
        }),
      );
      navigation.goBack();
    }
    setIsLoading(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{NOTIFICATION_TEXT.title}</Text>
      <View>
        {NOTIF_SETTINGS_FIELDS.map(field => (
          <Controller
            key={field.name}
            control={control}
            name={field.name as keyof INotificationSettingsFields}
            render={({ field: { onChange, value } }) => (
              <View style={styles.checkbox}>
                <Checkbox
                  title={field.label}
                  checked={value as boolean}
                  setChecked={onChange}
                />
              </View>
            )}
          />
        ))}
      </View>
      <View style={styles.buttonWrapper}>
        <CustomButton
          title="Save"
          onPress={handleSubmit(onSave)}
          loading={isLoading}
        />
      </View>
    </View>
  );
};

export default NotificationSettings;
