import React, { useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

import CustomText from './text';

interface Props {
  title: string;
  checked: boolean;
  setChecked: (value: boolean) => void;
}

const Checkbox: React.FC<Props> = ({ title, checked, setChecked }) => {
  const { colors } = useAppSelector(state => state.theme);

  const styles = useMemo(() => createStyles(colors), [colors]);

  const handlePress = () => {
    setChecked(!checked);
  };

  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <View style={styles.checkbox}>
        {checked && (
          <Icon
            name="check"
            size={moderateScale(16)}
            color={colors.secondary}
          />
        )}
      </View>
      <CustomText style={styles.title}>{title}</CustomText>
    </Pressable>
  );
};

export default Checkbox;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-start',
    },
    checkbox: {
      marginRight: moderateScale(8),
      backgroundColor: colors.base,
      width: moderateScale(18),
      height: moderateScale(18),
      borderRadius: moderateScale(4),
      alignItems: 'center',
      justifyContent: 'center',
    },
    title: {
      fontSize: moderateScale(16),
      color: colors.secondary,
    },
  });
