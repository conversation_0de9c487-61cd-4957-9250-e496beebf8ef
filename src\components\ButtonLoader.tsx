import { Animated, StyleSheet, View } from 'react-native';

const BlinkingDot = () => {
  const blinkAnimation = new Animated.Value(0);

  Animated.loop(
    Animated.sequence([
      Animated.timing(blinkAnimation, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 0.2,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]),
  ).start();

  const interpolatedOpacity = blinkAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.2, 1],
  });

  return (
    <Animated.View
      style={[styles.blinkingDot, { opacity: interpolatedOpacity }]}
    />
  );
};

const ButtonLoader = () => {
  return (
    <View style={styles.loading}>
      <View style={styles.spacer} />
      <BlinkingDot />
      <BlinkingDot />
      <BlinkingDot />
    </View>
  );
};

export default ButtonLoader;

const styles = StyleSheet.create({
  loading: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spacer: {
    marginRight: 2,
  },
  blinkingDot: {
    width: 5,
    height: 5,
    borderRadius: 50,
    backgroundColor: 'white',
    marginHorizontal: 1,
  },
});
