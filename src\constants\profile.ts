import countries from '../constants/countries.json';

const COUNTRY_LIST = Object.entries(countries).map(([key, value]) => ({
  label: value,
  value: key,
}));

const GENDER_LIST = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
  { label: 'Transgender', value: 'transgender' },
  {
    label: 'Non-Binary / Non-Conforming',
    value: 'non-binary-and-non-conforming',
  },
  { label: 'Prefer not to respond', value: 'prefer-not-to-respond' },
];

export const GENERAL_SETTINGS_FIELDS = [
  {
    name: 'username',
    label: 'Username',
    placeholder: 'Username',
    isDisabled: true,
  },
  {
    name: 'email',
    label: 'Email',
    placeholder: 'Email',
    keyboardType: 'email-address',
  },
  {
    name: 'gender',
    label: 'Gender',
    placeholder: 'Select Gender here',
    isDropdown: true,
    data: GENDER_LIST,
  },
  {
    name: 'country_id',
    label: 'Country',
    placeholder: 'Select Country here',
    isDropdown: true,
    data: COUNTRY_LIST,
  },
  {
    name: 'verification',
    label: 'Verification',
    placeholder: 'Verification',
    isDisabled: true,
  },
];

export const CHANGE_PASSWORD_FIELDS = [
  {
    name: 'current_password',
    label: 'Current Password',
    placeholder: 'Please enter your current password',
    secureTextEntry: true,
  },
  {
    name: 'new_password',
    label: 'New Password',
    placeholder: 'Please enter your new password',
    secureTextEntry: true,
  },
  {
    name: 'confirm_password',
    label: 'Repeat Password',
    placeholder: 'Please re-enter your new password',
    secureTextEntry: true,
  },
];

export const PLACEHOLDER_IMAGE = 'https://via.placeholder.com/150';
export const PLACEHOLDER_AVATAR = require('../assets/png/placeholder-avatar.png');

export const VERIFICATION_LINK =
  'https://coolpeoplenetwork.com/membership-account/membership-checkout';

export const VERIFICATION_STEPS = [
  {
    id: 1,
    title: 'Tell us about your podcast.',
    description:
      'Share some basic info, like what its about, Your planned scheduled for drops and what category.',
    image: require('../assets/png/info-symbol.png'),
  },
  {
    id: 2,
    title: 'Make it stand out',
    description:
      'Make sure you add a profile photo so user can easily identify your channel.',
    image: require('../assets/png/CPN-ICONIC-ICON.png'),
  },
  {
    id: 3,
    title: 'Finish up and get verified.',
    description:
      'Once your account is verified you have access to promote your episodes to the podcast community.',
    image: require('../assets/png/verified-icon.png'),
  },
];
