import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ToastState {
  isVisible: boolean;
}

const initialState: ToastState = {
  isVisible: false,
};

export const toastSlice = createSlice({
  name: 'toast',
  initialState,
  reducers: {
    setIsToastVisible: (state, action: PayloadAction<boolean>) => {
      state.isVisible = action.payload;
    },
  },
});

export const { setIsToastVisible } = toastSlice.actions;
