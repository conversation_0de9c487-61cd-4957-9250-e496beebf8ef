import { Alert } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import InAppBrowser from 'react-native-inappbrowser-reborn';

export const openGallery = (): Promise<string | null> => {
  return new Promise(resolve => {
    launchImageLibrary(
      {
        mediaType: 'photo',
      },
      response => {
        if (response?.didCancel) {
          resolve(null);
        } else if (response?.errorMessage) {
          resolve(null);
        } else if (response?.assets && response?.assets?.length > 0) {
          resolve(response?.assets?.[0]?.uri as string);
        } else {
          resolve(null);
        }
      },
    );
  });
};

export const appendImageToFormData = (
  formData: FormData,
  name: string,
  image: string,
) => {
  if (!image) {
    formData.append(name, '');
  } else if (image.startsWith('file:///')) {
    const localImageUri = image;
    const imageFile = {
      uri: localImageUri,
      name: 'image.jpg',
      type: 'image/jpeg',
    };
    formData.append(name, imageFile);
  }
};

export const openInAppBrowser = async (url: string) => {
  try {
    if (await InAppBrowser.isAvailable()) {
      await InAppBrowser.open(url, {
        dismissButtonStyle: 'done',
        readerMode: false,
        animated: true,
        modalPresentationStyle: 'fullScreen',
        modalTransitionStyle: 'coverVertical',
        enableBarCollapsing: true,
      });
    } else {
      Alert.alert('Error', 'InAppBrowser is not available');
    }
  } catch (error) {
    console.error('Error opening browser:', error);
  }
};
