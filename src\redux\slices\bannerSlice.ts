import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { fetchAdsBannersService } from '../../services/api/adService';
import { Banner } from '../../types/banner';

interface BannerState {
  banners: Banner[];
  loading: boolean;
  error: string | null;
}

const initialState: BannerState = {
  banners: [],
  loading: false,
  error: null,
};

export const fetchAdsBanners = createAsyncThunk(
  'banner/fetchBanners',
  async (payload: { slider_id: number }, { rejectWithValue }) => {
    try {
      const banners = await fetchAdsBannersService(payload);

      return banners;
    } catch (error) {
      return rejectWithValue('Failed to fetch banners');
    }
  },
);

export const bannerSlice = createSlice({
  name: 'banner',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchAdsBanners.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAdsBanners.fulfilled, (state, action) => {
        state.loading = false;
        state.banners = action.payload;
      })
      .addCase(fetchAdsBanners.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {} = bannerSlice.actions;
