import React, { FC, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import {
  playVideo,
  setShowMiniPlayer,
} from '../../../redux/slices/miniPlayerSlice';
import { fetchYTVideoDetails } from '../../../services/api/postsService';
import { Notifications } from '../../../types/notifications';
import { APP_ROUTES } from '../../../types/routes';
import { EPostType } from '../../Home/HomeFeed';
dayjs.extend(relativeTime);

interface NotificationListItemProps extends Notifications {}

export const NotificationListItem: FC<NotificationListItemProps> = ({
  notifier,
  post_data,
  time2,
}) => {
  const navigation = useTypeSafeNavigation();
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const thumbnail = `https://img.youtube.com/vi/${post_data.postYoutube}/hqdefault.jpg`;
  const handleNavigateToProfile = () => {
    navigation.navigate(APP_ROUTES.OTHER_USER_PROFILE, {
      userId: notifier.id,
      otherUserProfile: true,
    });
  };

  const handleNavigateToPost = async () => {
    const content = await fetchYTVideoDetails({ src: post_data.youtube });

    dispatch(
      playVideo({
        id: post_data.id,
        videoId: post_data.postYoutube,
        title: content.title,
        subtitle: post_data.publisher.first_name
          ? `${post_data.publisher.first_name} ${post_data.publisher.last_name}`
          : post_data.publisher.username,
        time: post_data.time,
        videoViews: post_data.videoViews,
        reaction: post_data.reaction,
        type: EPostType.POST,
      }),
    );
    navigation.goBack();
    dispatch(setShowMiniPlayer(true));
  };

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Pressable
          style={styles.avatarContainer}
          onPress={handleNavigateToProfile}>
          <FastImage source={{ uri: notifier.avatar }} style={styles.avatar} />
          <FastImage
            source={require('../../../assets/png/LikeReactIcon.png')}
            style={styles.emoji}
          />
        </Pressable>
        <View style={styles.textWrapper}>
          <CustomText style={styles.message} numberOfLines={2}>
            <CustomText style={styles.username}>{notifier.username}</CustomText>{' '}
            reacted to your post
          </CustomText>
          <CustomText style={styles.timestamp}>{time2}</CustomText>
        </View>
      </View>
      <Pressable onPress={handleNavigateToPost}>
        <FastImage source={{ uri: thumbnail }} style={styles.thumbnail} />
      </Pressable>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(6),
      backgroundColor: colors.background,
      gap: moderateScale(14),
    },
    contentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(8),
      flex: 1,
    },
    thumbnail: {
      width: moderateScale(85),
      height: moderateScale(50),
      borderRadius: moderateScale(5),
    },
    username: {
      fontSize: moderateScale(14),
      fontWeight: '500',
      color: colors.foreground,
    },
    message: {
      fontSize: moderateScale(14),
      color: colors.foreground,
    },
    timestamp: {
      fontSize: moderateScale(12),
      color: colors.secondary,
    },
    avatar: {
      width: moderateScale(40),
      height: moderateScale(40),
      borderRadius: moderateScale(50),
    },
    textWrapper: {
      gap: moderateScale(4),
      flex: 1,
    },
    emoji: {
      width: moderateScale(20),
      height: moderateScale(20),
      position: 'absolute',
      bottom: 0,
      right: 0,
    },
    avatarContainer: {
      position: 'relative',
    },
  });
