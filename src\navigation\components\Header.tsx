import { FC, useMemo } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/AntDesign';
import FeatherIcon from 'react-native-vector-icons/Feather';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { ParamListBase } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import {
  BannerIcon,
  NotificationIcon,
  QuestionNavIcon,
  SearchIcon,
  VerifiedIcon,
} from '../../assets/svgs';
import DarkLogo from '../../assets/svgs/DarkLogo';
import Logo from '../../assets/svgs/Logo';
import { NotificationBadge } from '../../components/NotificationBadge';
import { ColorScheme, THEME_MODE } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';
import { APP_ROUTES, STACKS } from '../../types/routes';

import { MembersCounts } from './MembersCounts';

export type CombinedHeaderNavigationProp =
  | StackNavigationProp<ParamListBase, string, undefined>
  | BottomTabNavigationProp<ParamListBase, string, undefined>;

interface HeaderProps {
  colors: ColorScheme;
  routeName?: string;
  iconName?: string;
  navigation: CombinedHeaderNavigationProp;
  showLogo?: boolean;
  hideNotification?: boolean;
  hideBanner?: boolean;
  hideVerifiedUsers?: boolean;
  hideSearch?: boolean;
  titleType?: 'default' | 'url';
}

export const Header: FC<HeaderProps> = ({
  colors,
  navigation,
  routeName,
  iconName = 'arrowleft',
  showLogo = false,
  hideNotification = false,
  hideBanner = false,
  hideVerifiedUsers = false,
  hideSearch = false,
  titleType = 'default',
}) => {
  const styles = useMemo(
    () => createStyles(colors, titleType),
    [colors, titleType],
  );
  const { theme } = useAppSelector(state => state.theme);

  const handleSearchPress = () => {
    navigation.navigate(STACKS.APP, { screen: APP_ROUTES.SEARCH });
  };
  const handleNotificationPress = () => {
    navigation.navigate(STACKS.APP, { screen: APP_ROUTES.NOTIFICATIONS });
  };

  const handleBannerPress = () => {
    navigation.navigate(STACKS.APP, { screen: APP_ROUTES.BANNER });
  };

  const handleVerifiedPress = () => {
    navigation.navigate(STACKS.APP, { screen: APP_ROUTES.POLLS });
  };

  let leftElement;
  if (showLogo) {
    leftElement = theme === THEME_MODE.dark ? <DarkLogo /> : <Logo />;
  } else {
    leftElement = (
      <Pressable onPress={navigation.goBack}>
        <Icon name={iconName} size={24} color={colors.foreground} />
      </Pressable>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.subContainer}>
        {leftElement}
        {routeName && !showLogo ? (
          <Text numberOfLines={1} style={styles.title}>
            {routeName.replace(/([a-z])([A-Z])/g, '$1 $2')}
          </Text>
        ) : (
          <MembersCounts />
        )}
      </View>

      <View style={styles.actionContainer}>
        {!hideBanner ? (
          <Pressable onPress={handleBannerPress}>
            <BannerIcon width={30} height={30} stroke={colors.foreground} />
          </Pressable>
        ) : null}
        {!hideVerifiedUsers ? (
          <Pressable onPress={handleVerifiedPress}>
            <QuestionNavIcon
              width={35}
              height={35}
              stroke={colors.foreground}
            />
          </Pressable>
        ) : null}
        {!hideNotification ? (
          <Pressable onPress={handleNotificationPress}>
            <NotificationIcon
              width={30}
              height={30}
              stroke={colors.foreground}
            />
            <NotificationBadge />
          </Pressable>
        ) : null}
        {!hideSearch ? (
          <Pressable onPress={handleSearchPress}>
            <SearchIcon width={30} height={30} stroke={colors.foreground} />
          </Pressable>
        ) : null}
      </View>
    </View>
  );
};

const createStyles = (colors: ColorScheme, titleType: 'default' | 'url') =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(14),
      paddingTop: verticalScale(16),
      paddingBottom: verticalScale(4),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    logo: { width: 120, height: 40 },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(8),
      flex: 1,
      height: verticalScale(37),
    },
    actionContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: moderateScale(12),
    },
    title: {
      color: titleType === 'url' ? colors.secondary : colors.foreground,
      fontSize: titleType === 'url' ? moderateScale(16) : moderateScale(20),
      fontWeight: '500',
      flex: 1,
      fontFamily: 'Antonio-SemiBold',
    },
  });
