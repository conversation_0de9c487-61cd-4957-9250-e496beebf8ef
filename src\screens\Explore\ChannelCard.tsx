import React, { useEffect, useMemo, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import CustomText from '../../components/common/text';
import Tooltip from '../../components/Tooltip';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { likeChannel } from '../../redux/slices/channelSlice';
import {
  playVideo,
  setShowMiniPlayer,
} from '../../redux/slices/miniPlayerSlice';
import { APP_ROUTES } from '../../types/routes';
import { EPostType } from '../Home/HomeFeed';

interface Props {
  categoryID: number;
  channelID: number;
  title: string;
  description: string;
  image: string;
  icon: string;
  isLiked: number;
  url: string;
}

const hapticFeedbackOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const ChannelCard: React.FC<Props> = ({
  categoryID,
  title,
  description,
  channelID,
  isLiked,
  image,
  url,
}) => {
  const [isSubscribe, setIsSubscribe] = useState(Boolean(isLiked));
  const [toastTitle, setToastTitle] = useState('');
  const { colors } = useAppSelector(state => state.theme);
  const { videoData } = useAppSelector(state => state.miniPlayer);

  const styles = useMemo(() => createStyles(colors), [colors]);
  const dispatch = useAppDispatch();
  const navigation = useTypeSafeNavigation();

  useEffect(() => {
    setIsSubscribe(Boolean(isLiked));
  }, [isLiked]);

  const handleSubscribe = async (id: number) => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticFeedbackOptions);

    await dispatch(
      likeChannel({
        channel_id: id,
        type: isSubscribe ? 'dislike' : 'like',
        categoryID,
      }),
    );
    setIsSubscribe(prev => !prev);
    setToastTitle(
      isSubscribe ? 'Removed from Favorites!' : 'Added to Favorites!',
    );
  };

  const playlistId = url.split('/').pop();

  const handleSelectPlaylist = () => {
    if (playlistId === videoData?.playlistId) return;
    dispatch(
      playVideo({
        playlistId: playlistId,
        channelId: String(channelID),
        type: EPostType.PODCAST_CAROUSEL,
      }),
    );
    dispatch(setShowMiniPlayer(true));
  };

  const handleNavigate = () => navigation.navigate(APP_ROUTES.SAVED);

  return (
    <Pressable onPress={handleSelectPlaylist} style={styles.container}>
      <FastImage
        source={{ uri: image, priority: FastImage.priority.high }}
        style={styles.mainImage}
      />
      <View style={styles.wrapper}>
        <View style={styles.textContainer}>
          <CustomText numberOfLines={1} style={styles.title}>
            {title}
          </CustomText>
          <CustomText style={styles.description} numberOfLines={1}>
            {description}
          </CustomText>

          <Pressable
            onPress={() => handleSubscribe(channelID)}
            style={styles.button}>
            <CustomText style={styles.buttonText}>
              {isSubscribe ? 'Unsubscribe' : 'Subscribe'}
            </CustomText>
          </Pressable>
        </View>
      </View>
      {toastTitle ? (
        <Tooltip
          mainTitle={toastTitle}
          btnTitle="View Saved Podcasts"
          onPress={handleNavigate}
        />
      ) : null}
    </Pressable>
  );
};

export default ChannelCard;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      width: '100%',
      backgroundColor: colors.background,
      borderRadius: moderateScale(8),
    },
    mainImage: {
      width: '100%',
      height: 'auto',
      aspectRatio: 0.9,
      borderRadius: moderateScale(8),
    },
    wrapper: {
      paddingTop: moderateScale(10),
      flexDirection: 'row',
      gap: moderateScale(10),
    },
    textContainer: {
      flex: 1,
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(15),
      textAlign: 'center',
      fontFamily: 'Antonio-Bold',
    },
    icon: {
      height: moderateScale(56),
      width: moderateScale(56),
      borderRadius: moderateScale(8),
    },
    description: {
      color: colors.secondary,
      fontSize: moderateScale(12),
      fontWeight: '400',
      flexShrink: 1,
      letterSpacing: 0,
      textAlign: 'center',
      marginTop: verticalScale(2),
    },
    button: {
      marginTop: verticalScale(4),
      backgroundColor: colors.accent,
      borderRadius: moderateScale(4),
      paddingVertical: moderateScale(6),
    },
    buttonText: {
      color: colors.white,
      textAlign: 'center',
      textTransform: 'uppercase',
      fontSize: moderateScale(14),
      fontWeight: '900',
    },
    readMore: {
      color: colors.accent,
      lineHeight: 0,
      fontSize: moderateScale(10),
      fontWeight: '400',
      flexShrink: 1,
      bottom: moderateScale(-2),
      marginLeft: moderateScale(4),
    },
  });
