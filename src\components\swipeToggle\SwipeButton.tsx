import React, {
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  AccessibilityInfo,
  ImageSourcePropType,
  LayoutChangeEvent,
  StyleSheet,
  TextStyle,
  TouchableOpacityProps,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme, THEME_MODE } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';
import CustomText from '../common/text';

import SwipeThumb from './SwipeThumb';

const SWIPE_SUCCESS_THRESHOLD = 70;
const DEFAULT_ANIMATION_DURATION = 400;

interface SwipeButtonProps extends TouchableOpacityProps {
  disabled?: boolean;
  disableResetOnTap?: boolean;
  enableReverseSwipe?: boolean;
  finishRemainingSwipeAnimationDuration?: number;
  forceCompleteSwipe?: (forceComplete: () => void) => void;
  forceReset?: (forceReset: () => void) => void;
  height: number;
  onSwipeFail?: () => void;
  onSwipeStart?: () => void;
  onSwipeSuccess?: (isForceComplete: boolean) => void;
  resetAfterSuccessAnimDelay?: number;
  screenReaderEnabled?: boolean;
  shouldResetAfterSuccess?: boolean;
  swipeSuccessThreshold?: number;
  thumbIconComponent?: () => ReactElement;
  thumbIconImageSource?: ImageSourcePropType;
  thumbIconWidth?: number;
  title: string;
  titleComponent?: () => ReactElement;
  titleMaxLines?: number;
  titleStyles?: TextStyle;
}

const SwipeButton: React.FC<SwipeButtonProps> = ({
  disabled = false,
  disableResetOnTap = false,
  enableReverseSwipe,
  finishRemainingSwipeAnimationDuration = DEFAULT_ANIMATION_DURATION,
  forceCompleteSwipe,
  forceReset,
  height,
  onSwipeFail,
  onSwipeStart,
  onSwipeSuccess,
  resetAfterSuccessAnimDelay,
  screenReaderEnabled,
  shouldResetAfterSuccess,
  swipeSuccessThreshold = SWIPE_SUCCESS_THRESHOLD,
  thumbIconComponent,
  thumbIconImageSource,
  thumbIconWidth,
  title,
  titleComponent: TitleComponent,
  titleMaxLines,
  titleStyles = {},
  ...rest
}) => {
  const { colors, theme } = useAppSelector(state => state.theme);
  const isDarkMode = theme === THEME_MODE.dark;

  const styles = useMemo(() => createStyles(colors), [colors]);

  const bgSource = isDarkMode
    ? require('../../../src/assets/png/swipe-toggle-bg-dark.png')
    : require('../../../src/assets/png/swipe-toggle-bg.png');

  const [layoutWidth, setLayoutWidth] = useState(0);
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] =
    useState(screenReaderEnabled);
  const [isUnmounting, setIsUnmounting] = useState(false);
  /**
   * Retrieve layoutWidth to set maximum swipeable area.
   * Correct layout width will be received only after first render but we need it before render.
   * So render SwipeThumb only if layoutWidth > 0
   */
  const onLayoutContainer = useCallback(
    (e: LayoutChangeEvent) => {
      const newWidth = e.nativeEvent.layout.width;
      if (!isUnmounting && newWidth !== layoutWidth) {
        setLayoutWidth(newWidth);
      }
    },
    [isUnmounting, layoutWidth],
  );

  const handleScreenReaderToggled = useCallback(
    (isEnabled: boolean) => {
      if (isUnmounting || isScreenReaderEnabled === isEnabled) {
        return;
      }
      if (screenReaderEnabled !== undefined) {
        setIsScreenReaderEnabled(screenReaderEnabled);
        // Return to avoid overriding the externally set value
        // eslint-disable-next-line newline-before-return
        return;
      }

      setIsScreenReaderEnabled(isEnabled);
    },
    [isScreenReaderEnabled, screenReaderEnabled],
  );

  useEffect(() => {
    setIsUnmounting(false);
    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      handleScreenReaderToggled,
    );

    AccessibilityInfo.isScreenReaderEnabled().then(handleScreenReaderToggled);

    return () => {
      setIsUnmounting(true);
      if (subscription) {
        subscription.remove();
      }
    };
  }, [isScreenReaderEnabled, handleScreenReaderToggled]);

  const handleFocus = useCallback(() => {
    AccessibilityInfo.isScreenReaderEnabled().then(handleScreenReaderToggled);
  }, [handleScreenReaderToggled]);

  return (
    <View
      onFocus={handleFocus}
      style={styles.container}
      onLayout={onLayoutContainer}
      {...rest}>
      <FastImage
        source={bgSource}
        resizeMode="stretch"
        style={styles.bgImage}
      />
      {TitleComponent ? (
        <View style={{ ...styles.title }}>
          <TitleComponent />
        </View>
      ) : (
        <CustomText
          ellipsizeMode={'tail'}
          numberOfLines={titleMaxLines}
          style={[styles.title, titleStyles]}>
          {title}
        </CustomText>
      )}
      {layoutWidth > 0 && (
        <SwipeThumb
          disabled={disabled}
          disableResetOnTap={disableResetOnTap}
          enableReverseSwipe={enableReverseSwipe}
          finishRemainingSwipeAnimationDuration={
            finishRemainingSwipeAnimationDuration
          }
          forceCompleteSwipe={forceCompleteSwipe}
          forceReset={forceReset}
          layoutWidth={layoutWidth - 2}
          onSwipeFail={onSwipeFail}
          onSwipeStart={onSwipeStart}
          onSwipeSuccess={onSwipeSuccess}
          resetAfterSuccessAnimDelay={resetAfterSuccessAnimDelay}
          shouldResetAfterSuccess={shouldResetAfterSuccess}
          swipeSuccessThreshold={swipeSuccessThreshold}
          thumbIconComponent={thumbIconComponent}
          thumbIconHeight={height}
          thumbIconImageSource={thumbIconImageSource}
          thumbIconWidth={thumbIconWidth}
        />
      )}
    </View>
  );
};

export default React.memo(SwipeButton);

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      borderRadius: 50,
      justifyContent: 'center',
      alignItems: 'center',
      margin: 5,
      position: 'relative',
      height: verticalScale(54),
      overflow: 'hidden',
    },
    title: {
      alignSelf: 'center',
      position: 'absolute',
      fontSize: moderateScale(20),
      fontWeight: 'semibold',
      color: colors.foreground,
    },
    bgImage: {
      position: 'absolute',
      width: '100%',
      height: '100%',
    },
  });
