import { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import {
  moderateVerticalScale,
  verticalScale,
} from 'react-native-size-matters';
import MaterialIconsIcons from 'react-native-vector-icons/MaterialIcons';

import { ColorScheme } from '../constants/theme/colors';
import { useAppSelector } from '../hooks/redux';

import CustomText from './common/text';

export const OfflineView: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      <MaterialIconsIcons
        name="wifi-off"
        size={moderateVerticalScale(40)}
        color={colors.secondary}
      />
      <CustomText style={styles.text}>No Internet Connection</CustomText>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    text: {
      color: colors.secondary,
      marginTop: verticalScale(10),
    },
  });
