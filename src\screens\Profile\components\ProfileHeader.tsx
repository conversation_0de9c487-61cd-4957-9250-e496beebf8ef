import React, { useEffect, useMemo, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import CustomText from '../../../components/common/text';
import { PLACEHOLDER_AVATAR } from '../../../constants/profile';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import { APP_ROUTES } from '../../../types/routes';
import { Userdata } from '../../../types/user';

interface Props {
  userDetails: Userdata | null;
}

const ProfileHeader: React.FC<Props> = ({ userDetails }) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [imageUri, setImageUri] = useState(userDetails?.avatar);

  useEffect(() => {
    setImageUri(userDetails?.avatar);
  }, [userDetails]);

  const navigation = useTypeSafeNavigation();

  const handleNavigate = () => {
    if (!userDetails) return;
    navigation.navigate(APP_ROUTES.VIEW_PROFILE, {
      userId: userDetails?.user_id,
    });
  };

  return (
    <View style={styles.container}>
      <FastImage
        source={imageUri ? { uri: imageUri } : PLACEHOLDER_AVATAR}
        onError={() => setImageUri('')}
        style={styles.image}
      />
      <View style={{ gap: verticalScale(5) }}>
        <CustomText style={styles.username}>
          {userDetails?.first_name
            ? `${userDetails?.first_name} ${userDetails?.last_name}`
            : userDetails?.username}
        </CustomText>
        <Pressable onPress={handleNavigate}>
          <CustomText style={styles.viewProfile}>View My Profile</CustomText>
        </Pressable>
      </View>
    </View>
  );
};

export default ProfileHeader;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(14),
    },
    image: {
      width: moderateScale(65),
      height: moderateScale(65),
      borderRadius: moderateScale(65 / 2),
    },
    username: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontWeight: '500',
    },
    viewProfile: {
      color: colors.secondary,
      fontSize: moderateScale(14),
      fontWeight: '400',
    },
  });
