import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Pressable, ScrollView, TextInput, Text, Alert, Image, KeyboardAvoidingView, Platform } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import Feather from 'react-native-vector-icons/Feather';
import { createPoll, clearPollsError, updatePoll, clearCreatePollStatus } from '../../redux/slices/pollsSlice';
import { Poll } from '../../types/polls';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { APP_ROUTES, AppStackNavigatorParamList } from '../../types/routes';
import CustomButton from '../../components/common/button';

const POLL_DURATIONS = [
  { label: '1 day', value: '1' },
  { label: '3 days', value: '3' },
  { label: '7 days', value: '7' },
];

const MAX_POLL_OPTIONS = 4;

interface CreatePollScreenProps {
  navigation: StackNavigationProp<AppStackNavigatorParamList, APP_ROUTES.CREATE_POLL>;
  route: RouteProp<AppStackNavigatorParamList, APP_ROUTES.CREATE_POLL>;
}

const CreatePollScreen: React.FC<CreatePollScreenProps> = ({ navigation, route }) => {
  const { colors, theme } = useAppSelector(state => state.theme);
  const { createPollStatus, error, polls, userPolls } = useAppSelector(state => state.polls);
  const { userDetails } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const styles = createStyles(colors, theme);

  // Get route params
  const { isEditMode = false, pollId } = route.params || {};
  const pollToEdit = isEditMode && pollId ?
    polls.find(poll => poll.id === pollId) || userPolls.find(poll => poll.id === pollId) : null;

  // UI state
  const [question, setQuestion] = useState('');
  const [options, setOptions] = useState(['', '']);
  const [duration, setDuration] = useState('');

  // Validation helper
  const isFormValid = () => {
    if (!question.trim()) return false;
    if (!duration) return false;
    const validOptions = options.filter(option => option.trim());
    return validOptions.length >= 2;
  };

  // Validation error messages
  const getValidationError = () => {
    if (!question.trim()) return 'Poll question is required.';
    if (!duration) return 'Please select a poll duration.';
    const validOptions = options.filter(option => option.trim());
    if (validOptions.length < 2) return 'Please add at least 2 options.';
    if (validOptions.length > MAX_POLL_OPTIONS) return `Maximum ${MAX_POLL_OPTIONS} options allowed.`;
    return null;
  };

  // Pre-fill form when editing
  useEffect(() => {
    if (isEditMode && pollToEdit) {
      setQuestion(pollToEdit.question);
      setOptions(pollToEdit.options.map(option => option.text));
      setDuration(pollToEdit.duration.toString());
    }
  }, [isEditMode, pollToEdit]);

  // Handlers
  const handleOptionChange = (text: string, idx: number) => {
    const newOptions = [...options];
    newOptions[idx] = text;
    setOptions(newOptions);
  };

  const handleAddOption = () => {
    if (options.length < MAX_POLL_OPTIONS) {
      setOptions([...options, '']);
    }
  };
  
  const handleDeleteOption = (idx: number) => {
    if (options.length > 2) {
      setOptions(options.filter((_, i) => i !== idx));
    }
  };

  // Handle poll creation success
  useEffect(() => {
    if (createPollStatus === 'succeeded') {
      navigation.goBack();
      // Reset form
      setQuestion('');
      setOptions(['', '']);
      setDuration('');
    }
  }, [createPollStatus, navigation]);

  // Reset status when screen opens to prevent immediate closing
  useEffect(() => {
    dispatch(clearCreatePollStatus());
  }, [dispatch]);

  // Handle errors - commented out since API service now shows proper error toasts
  // useEffect(() => {
  //   if (error) {
  //     Alert.alert('Error', error);
  //     dispatch(clearPollsError());
  //   }
  // }, [error, dispatch]);

  const handleCancel = () => {
    navigation.goBack();
  };

  const handleCreatePoll = () => {
    const validationError = getValidationError();
    if (validationError) {
      Alert.alert('Error', validationError);
      return;
    }
    
    if (!userDetails) {
      Alert.alert('Error', 'User not loaded');
      return;
    }

    const validOptions = options.filter(option => option.trim());

    if (isEditMode && pollToEdit) {
      dispatch(updatePoll({
        id: pollToEdit.id,
        question: question.trim(),
        options: validOptions,
        duration: parseInt(duration),
      }));
      return;
    }

    dispatch(createPoll({
      question: question.trim(),
      options: validOptions,
      duration: parseInt(duration),
      creator: userDetails,
    }));
  };

  const canAddMoreOptions = options.length < MAX_POLL_OPTIONS;

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      {/* Header - Fixed */}
      <View style={styles.header}>
        <CustomButton
          onPress={handleCancel}
          variant="transparent"
          title="CANCEL"
          textSyle={styles.cancelButtonText}
        />
        <Pressable
          onPress={handleCreatePoll}
          disabled={createPollStatus === 'loading' || !isFormValid()}
          style={[
            styles.shareButton,
            isFormValid() && createPollStatus !== 'loading' 
              ? styles.shareButtonActive 
              : styles.shareButtonInactive
          ]}
        >
          <Text style={[
            styles.shareButtonText,
            isFormValid() && createPollStatus !== 'loading' 
              ? styles.shareButtonTextActive 
              : styles.shareButtonTextInactive
          ]}>
            {createPollStatus === 'loading' 
              ? (isEditMode ? 'UPDATING...' : 'CREATING...') 
              : (isEditMode ? 'UPDATE' : 'SHARE')
            }
          </Text>
        </Pressable>
      </View>

      {/* Content Area - Fixed Question Section */}
      <View style={styles.content}>
        {/* Question Input Row - Fixed */}
        <View style={styles.questionContainerParent}>
        <View style={styles.questionContainer}>
          <View style={styles.questionRow}>
            <Image 
              source={
                userDetails?.avatar 
                  ? { uri: userDetails.avatar }
                  : require('../../assets/png/avatar.png')
              } 
              style={styles.avatar}
            />
            <TextInput
              value={question}
              onChangeText={setQuestion}
              placeholder="What's on your mind..........."
              placeholderTextColor={colors.foreground}
              multiline
              maxLength={250}
              style={styles.questionInput}
            />
          </View>
        </View>
        </View>

        {/* Counter and Dropdown Row - Fixed */}
        <View style={styles.counterRow}>
          <Text style={styles.counterText}>{question.length} / 250 Characters</Text>
          <View style={styles.dropdownContainer}>
            {canAddMoreOptions && (
              <Pressable onPress={handleAddOption} style={styles.pollOptButton}>
                <Feather name="plus" size={moderateScale(18)} color={colors.foreground} />
                <Text style={styles.pollOptText}>Poll Opt</Text>
              </Pressable>
            )}
            <Dropdown
              data={POLL_DURATIONS}
              value={duration}
              onChange={item => setDuration(item.value)}
              labelField="label"
              valueField="value"
              style={styles.dropdown}
              placeholder="Poll Length"
              placeholderStyle={{ color: colors.foreground, fontFamily: 'Antonio-Bold', fontSize: moderateScale(13), textAlign: 'right' }}
              selectedTextStyle={{ color: colors.foreground, fontFamily: 'Antonio-Bold', fontSize: moderateScale(13), textAlign: 'right' }}
              iconColor={colors.foreground}
              
              renderRightIcon={() => (
                <Feather name="chevron-down" size={moderateScale(28)} color={colors.foreground} />
              )}
              itemTextStyle={{ color: colors.foreground, fontFamily: 'Antonio-Bold', fontSize: moderateScale(13) }}
              containerStyle={{ borderRadius: moderateScale(8), backgroundColor: colors.background }}
              activeColor={colors.base}
              itemContainerStyle={{ borderRadius: moderateScale(4) }}
            />
          </View>
        </View>

        {/* Poll Options - Scrollable Area */}
        <ScrollView 
          style={styles.optionsScrollView} 
          showsVerticalScrollIndicator={true}
          contentContainerStyle={styles.optionsContentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {options.map((opt, idx) => (
            <View key={idx} style={styles.optionContainer}>
              <View style={styles.optionRow}>
                <View style={styles.optionInputContainer}>
                  <TextInput
                    value={opt}
                    onChangeText={text => handleOptionChange(text, idx)}
                    placeholder={`Poll Option ${idx + 1} ..........`}
                    placeholderTextColor={colors.foreground}
                    maxLength={200}
                    style={styles.optionInput}
                    textAlignVertical="top"
                  />
                  <Text style={styles.optionCounterInside}>{200-opt.length}</Text>
                </View>
                {options.length > 2 && (
                  <Pressable
                    onPress={() => handleDeleteOption(idx)}
                    style={styles.deleteBtn}
                    hitSlop={10}
                  >
                    <Feather name="x" size={16} color={colors.foreground} />
                  </Pressable>
                )}
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

const createStyles = (colors: any, theme: 'light' | 'dark') =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: moderateScale(16),
      paddingTop: verticalScale(50),
      paddingBottom: verticalScale(5),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    shareButton: {
      borderRadius: moderateScale(20),
      paddingHorizontal: moderateScale(24),
      paddingVertical: verticalScale(8),
    },
    shareButtonActive: {
      backgroundColor: colors.accent, // Red color when valid
    },
    shareButtonInactive: {
      backgroundColor: colors.foreground,
      opacity: 0.5,
    },
    shareButtonText: {
      fontFamily: 'Antonio-Bold',
      fontSize: moderateScale(14),
    },
    shareButtonTextActive: {
      color: colors.white,
    },
    shareButtonTextInactive: {
      color: colors.background,
    },
    cancelButtonText: {
      fontFamily: 'Antonio-Bold',
      fontSize: moderateScale(18),
    },
    content: {
      flex: 1,
      backgroundColor: colors.surface,
      // paddingHorizontal: moderateScale(18),
      paddingTop: verticalScale(10),
    },
    questionContainerParent:{
      paddingHorizontal: moderateScale(18),
    },
    questionContainer: {
      backgroundColor: colors.background,
      borderRadius: moderateScale(12),
      padding: moderateScale(16),
      marginBottom: verticalScale(8),
      
    },
    questionRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: moderateScale(12),
    },
    avatar: {
      width: moderateScale(32),
      height: moderateScale(32),
      borderRadius: moderateScale(16),
      marginTop: verticalScale(0),
    },
    questionInput: {
      fontFamily: 'Antonio-Regular',
      fontSize: moderateScale(16),
      color: colors.foreground,
      minHeight: verticalScale(100),
      flex: 1,
      backgroundColor: 'transparent',
      borderRadius: moderateScale(4),
      paddingHorizontal: moderateScale(2),
      paddingVertical: verticalScale(4),
      textAlignVertical: 'top',
      borderWidth: 0,
    },
    counterRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: verticalScale(8),
      paddingHorizontal: moderateScale(18),
      paddingVertical: verticalScale(0),

    },
    counterText: {
      color: colors.foreground,
      
      fontSize: moderateScale(13),
    },
    dropdownContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    pollOptButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(4),
    },
    pollOptText: {
      color: colors.foreground,
      fontFamily: 'Antonio-Bold',
      fontSize: moderateScale(13),
    },
    dropdown: {
      backgroundColor: 'transparent',
      color: colors.foreground,
      borderRadius: moderateScale(4),
      width: moderateScale(100),
      height: verticalScale(36),
      paddingHorizontal: moderateScale(4),
      paddingVertical: verticalScale(0),
      justifyContent: 'center',
      borderWidth: 0,
      fontSize: moderateScale(13),
      fontFamily: 'Antonio-Regular ',
    },
    optionContainer: {
      marginBottom: verticalScale(10),
    },
    optionInputContainer: {
      width: '100%',
      position: 'relative',
    },
    optionInput: {
      fontFamily: 'Antonio-Regular',
      fontSize: moderateScale(15),
      color: colors.foreground,
      minHeight: verticalScale(60),
      backgroundColor: colors.background,
      borderRadius: moderateScale(8),
      paddingHorizontal: moderateScale(14),
      paddingVertical: verticalScale(12),
      paddingRight: moderateScale(60),
      marginBottom: 0,
      textAlignVertical: 'top',

      
      width: '100%', // Add fixed width to prevent shrinking
    },
    optionCounterInside: {
      position: 'absolute',
      bottom: verticalScale(8),
      right: moderateScale(10),
      color: colors.descriptionSecondary,
      fontFamily: 'Antonio-Regular',
      fontSize: moderateScale(10),
    },
    optionRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: verticalScale(4),
      position: 'relative', // Add relative positioning
      width: '100%', // Full width container
    },
    deleteBtn: {
      position: 'absolute', // Position absolutely
      right: moderateScale(-18), // Move outside the input area
      top: '50%', // Center vertically
      transform: [{ translateY: -10 }], // Adjust vertical centering
      padding: moderateScale(2),
      borderRadius: moderateScale(16),
      backgroundColor: 'transparent',
    },
    optionsScrollView: {
      flex: 1,
      marginTop: verticalScale(5),
      paddingLeft: moderateScale(18),
      paddingRight: moderateScale(24),
    },
    optionsContentContainer: {
      paddingBottom: verticalScale(30), // Increased padding for keyboard space
    },
  });

export default CreatePollScreen; 