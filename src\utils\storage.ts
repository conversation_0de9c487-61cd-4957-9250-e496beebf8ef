import AsyncStorage from '@react-native-async-storage/async-storage';

export enum STORAGE_KEYS {
  AUTH_TOKEN = 'AUTH_TOKEN',
  USER_ID = 'USER_ID',
  THEME = 'THEME',
  AD_THRESHOLD = 'AD_THRESHOLD',
  VIDEO_PLAY_COUNT = 'VIDEO_PLAY_COUNT',
}

export const setTokenToAS = async (token: string) => {
  await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
};

export const getTokenFromAS = async () => {
  return (await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)) || '';
};

export const setItemToAS = async (key: string, value: string) => {
  await AsyncStorage.setItem(key, value);
};

export const getItemFromAS = async (key: STORAGE_KEYS) => {
  return await AsyncStorage.getItem(key);
};

export const removeItemFromAS = async (key: STORAGE_KEYS) => {
  await AsyncStorage.removeItem(key);
};

export const clearAS = async () => {
  await AsyncStorage.clear();
};
