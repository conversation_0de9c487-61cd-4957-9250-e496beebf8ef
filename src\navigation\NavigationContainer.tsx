import React, { useEffect, useState } from 'react';
import BootSplash from 'react-native-bootsplash';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { isGooglePixel } from '../constants/common';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { useThemeManager } from '../hooks/useThemeManager';
import { setUser } from '../redux/slices/authSlice';
import { RootStackParamList, STACKS } from '../types/routes';
import { STORAGE_KEYS } from '../utils/storage';

import AppNavigator from './AppNavigator';
import AuthNavigator from './AuthNavigator';
import { navigationRef } from './NavigationRef';

const RootStack = createNativeStackNavigator<RootStackParamList>();

const AppNavigationContainer = () => {
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAppSelector(state => state.auth);
  const { colors } = useAppSelector(state => state.theme);
  const { loadTheme } = useThemeManager();
  const dispatch = useAppDispatch();

  const getUserInfoFromAS = async () => {
    try {
      const access_token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      const userId = await AsyncStorage.getItem(STORAGE_KEYS.USER_ID);
      if (access_token && userId) {
        dispatch(setUser({ access_token, userId }));
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTheme();
  }, [user]);

  useEffect(() => {
    getUserInfoFromAS();
  }, []);

  if (isLoading) return null;

  return (
    <NavigationContainer
      onReady={() => {
        BootSplash.hide({ fade: true });
      }}
      ref={navigationRef}>
      <RootStack.Navigator
        screenOptions={{
          navigationBarColor: colors.background,
          navigationBarHidden: isGooglePixel,
        }}>
        {user?.userId ? (
          <RootStack.Screen
            name={STACKS.APP}
            component={AppNavigator}
            options={{ headerShown: false }}
          />
        ) : (
          <RootStack.Screen
            name={STACKS.AUTH}
            component={AuthNavigator}
            options={{ headerShown: false }}
          />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigationContainer;
