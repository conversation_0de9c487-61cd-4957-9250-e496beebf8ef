import React from 'react';
import { View, StyleSheet, Pressable, Text } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import FastImage from 'react-native-fast-image';
import {
  Menu,
  MenuOption,
  MenuOptions,
  MenuTrigger,
} from 'react-native-popup-menu';
import CustomText from '../../../components/common/text';
import { Poll, PollOption } from '../../../types/polls';
import { useAppSelector } from '../../../hooks/redux';
import { VerifyIcon, VotingIcon } from '../../../assets/svgs';
import { isPollExpired } from '../../../redux/slices/pollsSlice';

const PLACEHOLDER_AVATAR = require('../../../assets/png/avatar.png');

interface PollCardProps {
  poll: Poll;
  onOptionPress?: (option: PollOption) => void;
  onDeletePress?: (pollId: string) => void;
  onReportPress?: (pollId: string) => void;
  votingOptionId?: string | null;
}

const PollCard: React.FC<PollCardProps> = ({ poll, onOptionPress, onDeletePress, onReportPress, votingOptionId = null }) => {
  const { colors, theme } = useAppSelector(state => state.theme);
  const { userDetails } = useAppSelector(state => state.auth);
  const styles = createStyles(colors, theme);

  const isExpired = isPollExpired(poll);
  const isCreator = userDetails && poll.creator.user_id === userDetails.user_id;



  const handleDeletePress = () => {
    onDeletePress?.(poll.id);
  };

  const handleReportPress = () => {
    onReportPress?.(poll.id);
  };

  // Calculate percentages for each option
  const totalVotes = poll.options.reduce((sum, o) => sum + o.votes, 0) || 1;

  return (
    <View style={styles.card}>
      {/* Profile Row */}
      <View style={styles.profileRow}>
        <FastImage
          source={poll.creator.avatar ? { uri: poll.creator.avatar } : PLACEHOLDER_AVATAR}
          style={styles.avatar}
        />
        <View style={styles.profileTextCol}>
          <View style={styles.usernameRow}>
            <CustomText style={styles.username}>{poll.creator.username}</CustomText>
            {poll.creator.verified ? (
              <VerifyIcon width={moderateScale(14)} height={moderateScale(14)} style={styles.verifiedIcon} />
            ) : null}
          </View>
        </View>
        <View style={{ flex: 1 }} />
        <Menu>
          <MenuTrigger style={styles.menuButton}>
            <Icon name="more-horizontal" size={22} color={colors.secondary} />
          </MenuTrigger>
          <MenuOptions customStyles={{ optionsContainer: styles.menuContainer }}>
            {/* Only show Delete if user is creator */}
            {isCreator && (
              <MenuOption
                customStyles={{ optionText: [styles.menuText, { color: colors.accent }] }}
                onSelect={handleDeletePress}
              >
                <View style={styles.menuItem}>
                  <Icon name="trash-2" size={16} color={colors.accent} style={styles.menuIcon} />
                  <Text style={[styles.menuText, { color: colors.accent }]}>Delete</Text>
                </View>
              </MenuOption>
            )}
            {/* Only show Report for other people's polls */}
            {!isCreator && (
              <MenuOption
                customStyles={{ optionText: styles.menuText }}
                onSelect={handleReportPress}
              >
                <View style={styles.menuItem}>
                  <Icon name="flag" size={16} color={colors.secondary} style={styles.menuIcon} />
                  <Text style={styles.menuText}>Report</Text>
                </View>
              </MenuOption>
            )}
          </MenuOptions>
        </Menu>
      </View>
      {/* Voting Info - show individual option percentages when poll is active (pollActive = false) */}
      {poll.isVoted && !poll.pollActive && (
        <View style={styles.votingInfoContainer}>
          <VotingIcon width={moderateScale(16)} height={moderateScale(16)} fill={colors.secondary} style={styles.pollIcon} />
          <CustomText style={styles.pollCount}>Poll Count   </CustomText>
          <View style={styles.pollOptionsRow}>
            {poll.options.map((option, index) => (
              <CustomText key={option.id} style={styles.pollCountItem}>
                <CustomText style={[styles.pollCountItem, styles.pollCountBold]}>{index + 1}</CustomText>
                <CustomText style={styles.pollCountItem}> - {option.percentage}%</CustomText>
                {index < poll.options.length - 1 && <CustomText style={styles.pollCountItem}>  </CustomText>}
              </CustomText>
            ))}
          </View>
        </View>
      )}
      {/* Completed status - only show when poll is closed (pollActive = true) */}
      {poll.pollActive && (
        <View style={styles.votingInfoContainer}>
          <VotingIcon width={moderateScale(16)} height={moderateScale(16)} fill={colors.secondary} style={styles.pollIcon} />
          <CustomText style={styles.pollCount}>Poll Count   </CustomText>
          <CustomText style={styles.pollCountLabel}>Completed</CustomText>
        </View>
      )}
      {/* Question */}
      <CustomText style={styles.question}>{poll.question}</CustomText>
      
      {/* Show voting options when poll is active (pollActive = false) */}
      {!poll.pollActive && (
        <View style={styles.optionsWrapper}>
          <View style={styles.optionsContainer}>
            {poll.options.map((option: PollOption, idx) => (
              <Pressable
                key={option.id}
                style={[
                  styles.optionButton,
                  (poll.pollActive || isPollExpired(poll) || votingOptionId) && styles.optionButtonDisabled,
                ]}
                onPress={() => !isPollExpired(poll) && !poll.pollActive && !poll.isVoted && !votingOptionId && onOptionPress && onOptionPress(option)}
                disabled={isPollExpired(poll) || poll.pollActive || poll.isVoted || !!votingOptionId}
              >
                <View style={styles.optionContent}>
                  <CustomText style={styles.optionNumber}>{idx + 1}</CustomText>
                  <CustomText style={styles.optionText}>{option.text}</CustomText>
                  {poll.isVoted && option.isVoted && (
                    <CustomText style={[styles.optionText, styles.votedLabel]}>Voted</CustomText>
                  )}
                  {votingOptionId === option.id && (
                    <CustomText style={[styles.optionText, styles.votingLabel]}>Voting...</CustomText>
                  )}
                </View>
              </Pressable>
            ))}
          </View>
        </View>
      )}
      
      {/* Show results when poll is completed (pollActive = true) */}
      {poll.pollActive && (
        <View style={styles.resultsContainer}>
          <View style={styles.winnerContainer}>
            <VotingIcon width={moderateScale(20)} height={moderateScale(20)} fill={colors.secondary} style={styles.pollIcon} />
            <CustomText style={styles.winnerText}>
              {(() => {
                const winner = poll.options.reduce((prev, current) => 
                  prev.percentage > current.percentage ? prev : current
                );
                return `${winner.percentage}% Voted - ${winner.text}.`;
              })()}
            </CustomText>
          </View>
        </View>
      )}
    </View>
  );
};

const createStyles = (colors: any, theme: string) =>
  StyleSheet.create({
    card: {
      backgroundColor: theme === 'dark' ? colors.surface : colors.base,
      borderRadius: moderateScale(18),
      padding: moderateScale(14),
      marginBottom: verticalScale(18),
      shadowColor: theme === 'dark' ? colors.black : colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.10,
      shadowRadius: 8,
      elevation: 3,
    },
    profileRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: verticalScale(2),
    },
    avatar: {
      width: moderateScale(32),
      height: moderateScale(32),
      borderRadius: moderateScale(16),
      marginRight: moderateScale(10),
      backgroundColor: colors.base,
    },
    profileTextCol: {
      flexDirection: 'column',
      justifyContent: 'center',
      flexShrink: 1,
    },
    usernameRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: verticalScale(2),
    },
    username: {
      fontFamily: 'Antonio-Bold',
      color: theme === 'dark' ? colors.white : colors.black,
      fontSize: moderateScale(16),
      marginRight: moderateScale(4),
    },
    verifiedIcon: {
      marginTop: verticalScale(2),
      marginLeft: moderateScale(2),
    },
    menuButton: {
      padding: 4,
    },
    question: {
      color: theme === 'dark' ? colors.white : colors.black,
      fontSize: moderateScale(14),
      marginBottom: verticalScale(10),
      fontFamily: 'Antonio-SemiBold',
      marginTop: verticalScale(2),
    },
    votingInfoContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
      marginBottom: verticalScale(10),
      paddingHorizontal: moderateScale(4),
      marginLeft: moderateScale(35),
    },
    pollIcon: {
      marginRight: moderateScale(2),
      marginTop: verticalScale(-2),
    },
    pollCountLabel: {
      color: theme === 'dark' ? colors.accent : colors.accent,
      fontSize: moderateScale(12),
      fontFamily: 'Antonio-Bold',
    },
    pollCount: {
      color: theme === 'dark' ? colors.white : colors.black,
      fontSize: moderateScale(12),
 
    },
    pollCountItem: {
      color: theme === 'dark' ? colors.white : colors.secondary,
      fontSize: moderateScale(12),
      fontFamily: 'Antonio-Regular',
    },
    pollCountBold: {
      fontFamily: 'Antonio-Bold',
    },
    pollOptionsRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
    },
    optionsWrapper: {
      borderRadius: moderateScale(12),
      padding: moderateScale(16),
    },
    optionsContainer: {
      flexDirection: 'column',
      gap: verticalScale(8),
    },
    optionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme === 'dark' ? colors.base : colors.white,
      borderRadius: moderateScale(26),
      paddingVertical: verticalScale(6),
      paddingHorizontal: moderateScale(12),
      marginBottom: verticalScale(2),
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 8,
      borderWidth: 1,
      borderColor: colors.separator,
    },
    optionButtonDisabled: {
      opacity: 0.5,
      backgroundColor: theme === 'dark' ? colors.secondary : colors.base,
    },
    optionNumber: {
      color: theme === 'dark' ? colors.white : colors.secondary,
      fontFamily: 'Antonio-Bold',
      fontSize: moderateScale(14),
      marginRight: moderateScale(10),
    },
    optionText: {
      color: theme === 'dark' ? colors.white : colors.black,
      fontSize: moderateScale(14),
      fontFamily: 'Antonio-Regular',
      flexShrink: 1,
    },
    optionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      paddingRight: moderateScale(80),
    },

    menuContainer: {
      backgroundColor: theme === 'dark' ? colors.surface : colors.white,
      borderRadius: moderateScale(8),
      paddingVertical: moderateScale(4),
      minWidth: moderateScale(120),
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 5,
      borderWidth: 1,
      borderColor: colors.separator,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: moderateScale(8),
      paddingHorizontal: moderateScale(16),
    },
    menuIcon: {
      marginRight: moderateScale(8),
    },
    menuText: {
      fontSize: moderateScale(14),
      color: theme === 'dark' ? colors.white : colors.black,
      fontFamily: 'Antonio-Regular',
    },
    votedLabel: {
      color: colors.accent,
      position: 'absolute',
      right: moderateScale(16),
      fontFamily: 'Antonio-Bold',
      fontSize: moderateScale(12),
    },
    votingLabel: {
      color: colors.secondary,
      position: 'absolute',
      right: moderateScale(16),
      fontFamily: 'Antonio-Bold',
      fontSize: moderateScale(12),
    },
    resultsContainer: {
      marginTop: verticalScale(10),
      paddingHorizontal: moderateScale(4),
    },
    winnerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    winnerText: {
      color: theme === 'dark' ? colors.white : colors.black,
      fontSize: moderateScale(14),
      fontFamily: 'Antonio-Regular',
      marginLeft: moderateScale(6),
    },
  });

export default PollCard; 