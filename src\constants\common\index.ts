import { Dimensions, Platform, StatusBar } from 'react-native';
import DeviceInfo from 'react-native-device-info';
const { height: windowHeight } = Dimensions.get('window');
const { height: screenHeight } = Dimensions.get('screen');
export const COMMON_ERROR = 'Something went wrong!';
export const SOFT_MENU_BAR_HEIGHT = StatusBar.currentHeight || 0;
export const bottomNavBarH = screenHeight - windowHeight;
export const navigationBarHeight =
  screenHeight - windowHeight > 0 ? screenHeight - windowHeight : 48;
export const hasBottomNav = bottomNavBarH > 0;
export const isGooglePixel = DeviceInfo.getBrand() === 'google';

// can be used in the future
export const IS_6_INCH_Android =
  Platform.OS === 'android' && windowHeight < 870;
export const IS_5_INCH_Android =
  Platform.OS === 'android' && windowHeight < 850;
// export const IS_6_INCH_IOS = Platform.OS === 'ios' && height < 800;
export const IS_6P_INCH_Android =
  Platform.OS === 'android' && windowHeight < 900;
// export const IS_6P_INCH_IOS = Platform.OS === 'ios' && height < 900;
export const IS_7_INCH_Android =
  Platform.OS === 'android' && windowHeight < 1000;
// export const IS_7_INCH_IOS = Platform.OS === 'ios' && height < 1000;
export const IS_8_INCH_Android =
  Platform.OS === 'android' && windowHeight < 1100;
// export const IS_8_INCH_IOS = Platform.OS === 'ios' && height < 1100;
export const IS_9_INCH_Android =
  Platform.OS === 'android' && windowHeight < 1200;
// export const IS_9_INCH_IOS = Platform.OS === 'ios' && height < 1200;
