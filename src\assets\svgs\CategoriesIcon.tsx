import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';

interface CategoriesIconProps extends SvgProps {
  isFocused?: boolean;
}

export const CategoriesIcon: React.FC<CategoriesIconProps> = props => {
  return (
    <Svg id="Layer_1" x="0px" y="0px" viewBox="0 0 1059 800" {...props}>
      <G>
        {props?.isFocused ? (
          <>
            <G>
              <Path d="M510.2,120v63c0,38.4-31.3,69.7-69.7,69.7h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62 C478.9,50.2,510.2,81.5,510.2,120z" />
            </G>
            <G>
              <Path d="M766.5,120v63c0,38.4-31.3,69.7-69.7,69.7h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62 C735.3,50.2,766.5,81.5,766.5,120z" />
            </G>
            <G>
              <Path d="M510.2,375.6v63c0,38.4-31.3,69.7-69.7,69.7h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62 C478.9,305.9,510.2,337.1,510.2,375.6z" />
            </G>
            <G>
              <Path d="M766.5,375.6v63c0,38.4-31.3,69.7-69.7,69.7h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62 C735.3,305.9,766.5,337.1,766.5,375.6z" />
            </G>
          </>
        ) : (
          <>
            <G>
              <Path d="M440.5,252.7h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62c38.4,0,69.7,31.3,69.7,69.7v63 C510.2,221.4,478.9,252.7,440.5,252.7z M378.5,81.3c-21.3,0-38.7,17.4-38.7,38.7v63c0,21.3,17.4,38.7,38.7,38.7h62 c21.3,0,38.7-17.4,38.7-38.7v-63c0-21.3-17.4-38.7-38.7-38.7H378.5z" />
            </G>
            <G>
              <Path d="M696.8,252.7h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62c38.4,0,69.7,31.3,69.7,69.7v63 C766.5,221.4,735.3,252.7,696.8,252.7z M634.8,81.3c-21.3,0-38.7,17.4-38.7,38.7v63c0,21.3,17.4,38.7,38.7,38.7h62 c21.3,0,38.7-17.4,38.7-38.7v-63c0-21.3-17.4-38.7-38.7-38.7H634.8z" />
            </G>
            <G>
              <Path d="M440.5,508.3h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62c38.4,0,69.7,31.3,69.7,69.7v63 C510.2,477,478.9,508.3,440.5,508.3z M378.5,336.9c-21.3,0-38.7,17.4-38.7,38.7v63c0,21.3,17.4,38.7,38.7,38.7h62 c21.3,0,38.7-17.4,38.7-38.7v-63c0-21.3-17.4-38.7-38.7-38.7H378.5z" />
            </G>
            <G>
              <Path d="M696.8,508.3h-62c-38.4,0-69.7-31.3-69.7-69.7v-63c0-38.4,31.3-69.7,69.7-69.7h62c38.4,0,69.7,31.3,69.7,69.7v63 C766.5,477,735.3,508.3,696.8,508.3z M634.8,336.9c-21.3,0-38.7,17.4-38.7,38.7v63c0,21.3,17.4,38.7,38.7,38.7h62 c21.3,0,38.7-17.4,38.7-38.7v-63c0-21.3-17.4-38.7-38.7-38.7H634.8z" />
            </G>
          </>
        )}
        <G>
          <G>
            <Path d="M237,688.7c0,1.6,0,3.3,0.1,4.9c0,0.1,0,0.3,0,0.4c0,5,0,9.9-0.1,15c-0.1,9.2-1.3,16.8-3.6,22.7 c-3.3,8.6-8.8,14.1-16.5,16.5c-3.6,1.1-7.2,1.6-11,1.6c-4.8,0-9.3-0.9-13.5-2.6c-6.8-3.1-11.7-8.8-14.9-17 c-1.7-4.9-2.7-9.9-3.1-14.9c-0.4-5.1-0.5-9.9-0.5-14.4V646c0-5.4,0.2-10.6,0.7-15.8c0.5-5.2,1.7-10.2,3.6-15 c2.7-6.4,6.6-11,11.6-13.9c6.7-3.6,14.4-4.6,23.3-3.1c6.1,1.2,11.1,3.9,14.9,8.1c3.7,4.1,6.2,9.8,7.5,17.1c0.5,2.8,0.8,5.7,1,8.4 c0.1,2.8,0.2,5.4,0.2,7.9v18.8h-22.4v-17.4c0-2.7-0.1-5.4-0.2-8V633c-0.1-2-0.2-4.1-0.5-6.2c-0.3-2.1-0.9-3.9-1.9-5.4 c-1-1.5-2.3-2.4-4-2.7c-1.7-0.3-3.3-0.2-4.7,0.5c-2.5,1-4.2,4-4.9,8.9c-0.4,2.3-0.6,4.6-0.7,7c-0.1,2.3-0.1,4.8-0.1,7.3V688 c0,2.2,0,4.3,0,6.4c0,4.3,0,8.5,0,12.8c0,4.4,0.2,8.1,0.6,11.2c0.2,1.8,0.6,3.6,1.2,5.3c0.6,1.7,1.6,3.1,2.9,4 c1.4,0.9,2.9,1.2,4.6,1c1.7-0.2,3.1-0.9,4.2-2c2-2.7,3-6.3,3.1-10.7c0.3-2.4,0.4-5.4,0.4-8.8v-22H237V688.7z" />
          </G>
          <G>
            <Path d="M295.9,721.8h-21.3l-3,26.3h-22.4l0.5-3.9c0.3-2.5,0.7-5,1-7.5c0.2-1.3,0.4-2.6,0.6-4c0.7-4.8,1.3-9.6,2-14.4 c0.8-5.4,1.5-10.9,2.3-16.3l13.1-95.6c0.1-0.7,0.3-1.4,0.4-2c0.1-0.7,0.2-1.5,0.3-2.1l0.4-3h29.8l0.4,2.8 c0.6,4.3,1.2,8.5,1.9,12.8c0.8,5.6,1.6,11.3,2.4,17c0.9,6,1.8,12.1,2.6,18.1l13.3,91.4c0.1,0.7,0.2,1.4,0.3,2 c0,0.2,0,0.4,0.1,0.6c0,0.1,0,0.2,0,0.3l0.5,3.8h-21.6l-0.4-3c0-0.1,0-0.2,0-0.3C297.9,737.1,296.9,729.4,295.9,721.8z  M277.8,694.3c-0.3,2.4-0.6,4.8-0.9,7.2c0,0.2,0,0.4-0.1,0.6h16.6c-0.5-3.9-1-7.8-1.5-11.7c-0.5-4.1-1-8.1-1.6-12.1l-0.7-5 c-0.5-4.1-1-8.1-1.6-12.1c-0.9-7.5-1.9-15.1-2.8-22.6l-0.6-4.1L277.8,694.3z" />
          </G>
          <G>
            <Path d="M366.2,748h-23c0-75.2,0-116.1,0.1-122.9v-4.8h-16.6v-21.2h55.6v21.2h-16C366.3,702.6,366.3,745.2,366.2,748z" />
          </G>
          <G>
            <Path d="M417.6,681.9v45.2h26.9v21h-50V606.1c0-0.7,0-1.4,0-2c0-0.1,0-0.2,0-0.3c0-0.4,0-0.8,0-1.2l0.1-3.4h48.9v21.1h-25.9v41.1 h24.7v20.6H417.6z" />
          </G>
          <G>
            <Path d="M464.5,731.1c-1.8-4.7-2.9-9.4-3.3-14.2c-0.5-4.8-0.7-9.3-0.7-13.6v-35.1c0-2.9,0-5.8-0.1-8.8c0-0.3,0-0.7,0-1 c-0.1-7.1,0-14.2,0.1-21.5c0.4-14.7,4-25.3,10.8-31.8c3.7-3.7,8.6-6,14.8-7c6-1,11.8-0.8,17.4,0.5c5.3,1.4,9.5,3.9,12.6,7.5 c1.8,2.2,3.1,4.4,4,6.8c0.9,2.4,1.6,4.6,2.1,6.7c1.6,8.8,2.2,17.6,1.9,26.3c0,0.8,0,1.6,0,2.3v4h-21.7v-6.1 c0.1-3.7,0.1-7.5,0-11.3c0-2.4-0.1-4.9-0.4-7.4c-0.3-2.5-1-4.6-2.2-6.3c-1.3-1.5-3-2.3-5-2.5c-2.1-0.2-3.9,0.1-5.6,1 c-1.6,1-2.7,2.5-3.3,4.4c-0.7,1.9-1.1,3.9-1.3,6c-0.4,3.9-0.6,7.5-0.6,10.7V697c0,0.9,0,1.7,0,2.5c0,0.1,0,0.1,0,0.2 c-0.1,3.9,0,7.7,0.2,11.6c0,4.7,0.7,9.1,2.3,13.2c1,1.8,2.4,3.1,4.2,3.7c1.8,0.6,3.6,0.7,5.3,0.2c2-0.7,3.4-1.8,4.4-3.5 c1-1.7,1.7-3.4,2.1-5.1c1.1-5.8,1.6-11.5,1.4-16.9c0-3.9,0-7.9,0-11.9v-7h-10.9v-18H524v82H513l-2.2-9.4 c-5.2,6.9-12.2,10.5-21.1,10.9c-6.3-0.1-11.7-1.8-16-5.3C469.7,741,466.6,736.6,464.5,731.1z" />
          </G>
          <G>
            <Path d="M577.4,749.6c-20,0-30.7-12.2-32.1-36.5c-0.2-3.9-0.3-7.8-0.3-11.7l0.1-5.7c0-31.5,0-49.1-0.1-52.9 c0-3.8,0.1-7.6,0.4-11.5c1.8-22.6,12.6-33.9,32.3-33.9c10.8,0.1,18.7,3.4,23.7,9.9c4.6,6,7.1,14.6,7.7,25.8 c0.1,3.7,0.2,7.4,0.2,11.1v6.2c0,30.9,0,48.1,0,51.6c0,4.7-0.2,9.5-0.5,14.3c-1,10.6-3.8,18.6-8.3,23.9 C595.2,746.5,587.5,749.6,577.4,749.6z M577,728.6c0.7,0,1.4-0.1,2.2-0.2c3.5-0.4,5.6-4.1,6.4-11c0.3-3.3,0.5-6.4,0.5-9.6v-71.5 c0-2.2-0.1-4.5-0.3-6.9c-0.5-5.1-1.8-8.3-4-9.7c-1.4-0.8-2.8-1.2-4.3-1.2c-1.8,0-3.4,0.5-4.8,1.6c-1.9,1.8-3.1,4.2-3.5,7.4 c-0.5,3.2-0.7,6.1-0.7,8.9c0,45,0,68.7-0.1,71c0,4.9,0.5,9.6,1.4,14.2C570.6,726.3,573,728.6,577,728.6z" />
          </G>
          <G>
            <Path d="M693.4,748h-23c-0.5-7.4-0.7-23.5-0.7-48.4v-4.4c0-8.1-1.1-12.9-3.2-14.4c-1.4-1-3.1-1.6-5.1-1.8l-8.3-0.4V748h-23.2 c0-92.7,0.1-142.3,0.2-148.9h34.8c2.6,0,5.4,0.2,8.4,0.6c3,0.4,6,1.5,8.9,3.4c4.4,3.1,7.3,8.1,8.7,14.9c1.2,6.7,1.8,14,1.8,21.8 c0,5.5-0.6,11-1.8,16.6c-1.5,6.2-4.7,10.4-9.5,12.8c6,2.4,9.5,9.1,10.4,20.2c0.7,8.4,1.1,19.9,1.1,34.6c-0.1,6.2,0,11.7,0.2,16.4 C693.1,741.5,693.2,744.1,693.4,748z M660.2,659.5c2.1,0,3.9-0.3,5.4-1c1.8-1,3-3.6,3.6-7.8c0.4-3.9,0.6-7.7,0.6-11.5 c0-4.7-0.2-9.2-0.7-13.6c-0.5-3.1-1.4-5-2.7-5.9c-1.8-1.1-4.7-1.6-8.9-1.6l-4.2,0.1v41.4H660.2z" />
          </G>
          <G>
            <Path d="M738,748h-23.1c0-94.3,0-143.9,0.1-148.9h23.1C738.1,691,738.1,740.6,738,748z" />
          </G>
          <G>
            <Path d="M782.7,681.9v45.2h26.9v21h-50V606.1c0-0.7,0-1.4,0-2c0-0.1,0-0.2,0-0.3c0-0.4,0-0.8,0-1.2l0.1-3.4h48.9v21.1h-25.9v41.1 h24.7v20.6H782.7z" />
          </G>
          <G>
            <Path d="M854.2,749.7c-4.6,0-8.7-0.7-12.5-2.1c-9.3-3.4-15-12.5-17.1-27.5c-0.7-5.7-1-11.4-1-17l0.1-11.3h22v17.4 c0,8.1,1,13.7,2.9,16.8c1.2,1.8,3.1,2.7,5.7,2.7c1,0,2.3-0.2,3.6-0.6c1.4-0.4,2.5-1.5,3.3-3.3c1.1-3.5,1.7-7.3,1.7-11.2 c0-7.4-1.3-13.6-3.8-18.6c-2.1-4.1-5.3-8.4-9.5-13c-3.5-3.5-6.9-7.2-10.4-11.1c-9.6-10.3-14.8-21.2-15.6-32.6 c-0.1-1.8-0.2-3.5-0.2-5.2c0-4.9,0.5-9.5,1.5-13.8c2.4-10.9,7.9-17.6,16.4-20.2c4-1.2,7.9-1.8,11.4-1.8c3.6,0.1,6.2,0.2,7.9,0.5 c8.6,1.4,14.5,5.5,17.7,12.5c2.6,5.7,4.1,12.3,4.4,19.9c0.3,4.6,0.4,10.8,0.4,18.4h-21.4V640l-0.1-7.5c-0.1-4.1-0.7-7.7-2-10.9 c-1-2.1-2.9-3.2-5.6-3.4c-1,0-1.7,0.1-2.3,0.2c-4.4,0.8-6.7,5.1-6.7,12.8c0,2.3,0.3,4.5,0.8,6.7c1,4.2,4.1,9.1,9.4,14.8 c0.1,0,0.1,0,0.1,0.1c0.9,1,4.8,5.3,11.7,13.1c5.5,6.2,9.7,12.4,12.6,18.8c3.5,7.3,5.2,15.5,5.2,24.5c0,6.7-0.6,12.8-1.9,18.2 c-1.5,6.4-3.8,11.2-6.9,14.6C871.3,747.2,863.9,749.7,854.2,749.7z" />
          </G>
        </G>
      </G>
    </Svg>
  );
};
