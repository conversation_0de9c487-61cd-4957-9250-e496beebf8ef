import React, { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const ProfileSkeleton: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            height={moderateScale(90)}
            width={moderateScale(90)}
            alignSelf="center"
            marginBottom={moderateScale(20)}
            borderRadius={moderateScale(50)}
          />
          <SkeletonPlaceholder.Item alignSelf="center" gap={moderateScale(5)}>
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              width={moderateScale(80)}
              height={verticalScale(14)}
              alignSelf="center"
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              width={moderateScale(120)}
              height={verticalScale(14)}
              alignSelf="center"
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            gap={moderateScale(15)}
            marginVertical={verticalScale(20)}>
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              height={verticalScale(50)}
              marginHorizontal={moderateScale(14)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              height={verticalScale(50)}
              marginHorizontal={moderateScale(14)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              height={verticalScale(50)}
              marginHorizontal={moderateScale(14)}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            alignSelf="center"
            gap={moderateScale(15)}
            flexDirection="row"
            paddingHorizontal={moderateScale(14)}
            marginVertical={verticalScale(10)}>
            <SkeletonPlaceholder.Item
              width={moderateScale(32)}
              height={moderateScale(32)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item
              width={moderateScale(32)}
              height={moderateScale(32)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item
              width={moderateScale(32)}
              height={moderateScale(32)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item
              width={moderateScale(32)}
              height={moderateScale(32)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item
              width={moderateScale(32)}
              height={moderateScale(32)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item
              width={moderateScale(32)}
              height={moderateScale(32)}
              borderRadius={moderateScale(50)}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            width={moderateScale(100)}
            height={moderateScale(50)}
            marginTop={moderateScale(40)}
            alignSelf="center"
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      gap: verticalScale(12),
      flex: 1,
    },
  });
