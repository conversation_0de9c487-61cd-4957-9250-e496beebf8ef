import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import { VerifyIcon } from '../../../assets/svgs';
import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { playVideo } from '../../../redux/slices/miniPlayerSlice';
import { fetchYTVideoDetails } from '../../../services/api/postsService';
import { Post, YTVideoContent } from '../../../types/posts';
import { formatViews } from '../../../utils/posts';
import { EPostType } from '../HomeFeed';

dayjs.extend(relativeTime);

interface PostDetailItemProps {
  data: Post;
  selectedVideoId: string;
}

export const PostDetailItem: FC<PostDetailItemProps> = ({
  data,
  selectedVideoId,
}) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const { isAdVisible } = useAppSelector(state => state.ads);

  const styles = useMemo(
    () => createStyles(colors, selectedVideoId === data.id),
    [colors, selectedVideoId, data.id],
  );
  const timeAgo = useMemo(
    () => dayjs(Number(data.time) * 1000).fromNow(),
    [data.time],
  );
  const [videoDetails, setVideoDetails] = useState<YTVideoContent | null>(null);

  const fetchContent = useCallback(async () => {
    if (!data?.youtube) return;
    try {
      const content = await fetchYTVideoDetails({ src: data?.youtube });
      setVideoDetails(content);
    } catch (error) {
      console.error('Error fetching video details:', error);
    }
  }, [data?.postYoutube]);

  useEffect(() => {
    fetchContent();
  }, [fetchContent]);

  const thumbnail = `https://img.youtube.com/vi/${data?.postYoutube}/maxresdefault.jpg`;

  const handleSelectVideo = useCallback(() => {
    if (isAdVisible) return;
    dispatch(
      playVideo({
        id: data?.id,
        title: videoDetails?.title!,
        subtitle: data?.publisher?.first_name
          ? `${data?.publisher?.first_name} ${data?.publisher?.last_name}`
          : data?.publisher?.username,
        reaction: data?.reaction,
        videoId: data?.postYoutube,
        videoViews: data?.videoViews,
        time: timeAgo,
        type: EPostType.POST,
      }),
    );
  }, [videoDetails, data, timeAgo, isAdVisible]);

  return (
    <Pressable style={styles.container} onPress={handleSelectVideo}>
      <View style={styles.imageContainer}>
        <FastImage source={{ uri: thumbnail }} style={styles.image} />
      </View>
      <View style={styles.content}>
        <CustomText numberOfLines={2} style={styles.title}>
          {videoDetails?.title}
        </CustomText>
        <CustomText numberOfLines={1} style={styles.subtitle}>
          {data?.publisher?.username}{' '}
          {data?.publisher?.verified ? (
            <VerifyIcon width={10} height={10} />
          ) : null}
        </CustomText>
        <View style={styles.insightContainer}>
          <CustomText style={styles.views}>
            {formatViews(Number(data.videoViews))} Views
          </CustomText>
          <View style={styles.separator} />
          <CustomText style={styles.timeAgo}> {timeAgo}</CustomText>
        </View>
      </View>
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme, isSelectedEpisode: boolean) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: isSelectedEpisode ? colors.base : colors.background,
      paddingHorizontal: moderateScale(14),
      paddingVertical: verticalScale(5),
    },
    imageContainer: {
      position: 'relative',
    },
    image: {
      width: moderateScale(130),
      height: verticalScale(62),
      borderRadius: moderateScale(6),
    },
    content: {
      flex: 1,
      marginLeft: moderateScale(8),
      justifyContent: 'space-around',
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(13),
    },
    subtitle: {
      color: colors.foreground,
      fontSize: moderateScale(13),
      fontWeight: '500',
    },
    views: {
      color: colors.secondary,
      fontSize: moderateScale(11),
    },
    separator: {
      height: verticalScale(2),
      width: verticalScale(2),
      backgroundColor: colors.secondary,
      borderRadius: moderateScale(5),
      marginHorizontal: moderateScale(4),
    },
    timeAgo: {
      color: colors.secondary,
      fontSize: moderateScale(11),
    },
    insightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    duration: {
      position: 'absolute',
      bottom: verticalScale(5),
      left: moderateScale(5),
      backgroundColor: 'rgba(0,0,0,0.7)',
      color: colors.white,
      fontSize: moderateScale(8),
      paddingVertical: moderateScale(1),
      paddingHorizontal: moderateScale(4),
      borderRadius: moderateScale(3),
      overflow: 'hidden',
    },
  });
