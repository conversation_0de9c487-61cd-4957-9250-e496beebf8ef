import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, SvgProps } from 'react-native-svg';

interface FavoritesIconProps extends SvgProps {
  isFocused?: boolean;
}

export const FavoritesIcon: React.FC<FavoritesIconProps> = props => {
  return (
    <Svg id="Layer_1" x="0px" y="0px" viewBox="0 0 1059 800" {...props}>
      <G>
        <G>
          <Path d="M537.7,98.9c12.9-8.6,25.6-18.7,39.7-26.3c46.6-25.2,94.4-27.6,141.8-2.6c41.2,21.8,66.9,56.8,78.7,101.5 c12.2,46.5,6.1,91.7-13.4,135.3c-14.2,31.7-35.3,58.6-59.7,83c-26.2,26.2-55.4,48.9-84.9,71.1c-17.5,13.2-34.9,26.7-53,39.1 c-17.7,12-37.4,18.7-59.4,15.6c-13.8-2-26.5-7-37.7-14.8c-18.5-12.9-36.7-26.5-54.7-40.1c-15-11.4-29.6-23.2-44.3-34.9 c-7.1-5.7-9-13.7-5.7-21.4c5-11.9,18.6-15.5,28.9-7.4c20.2,15.8,40.1,31.9,60.6,47.3c13.5,10.2,27.5,19.8,41.9,28.8 c13.8,8.6,28.8,8.8,42.6,0c16.5-10.5,32.8-21.6,48.2-33.7c27.5-21.5,54.9-43.2,81.3-66.2c26-22.7,47.9-49.2,62-81.1 c17.3-39,22.8-79.2,8.6-120.4c-12.2-35.3-35.4-61.1-70.6-74.7c-27.4-10.6-54.9-8.8-81.7,2.6c-20.6,8.7-38.3,21.9-53.9,38 c-9.5,9.9-20.2,10.1-30,0.2c-17.4-17.5-36.9-32.2-60.3-40.6c-46.2-16.5-93.6-2.9-124.3,35.2c-22.7,28.2-31.7,60.5-29.1,96.4 c1.8,26.1,8.6,50.7,21,73.7c4.2,7.9,4.9,15.6-1.1,22.8c-8.5,10.3-23.8,8.7-30.6-3.3c-11.6-20.6-19.4-42.6-23.5-65.9 c-6.5-37-5.2-73.3,10.4-108.1c21.1-46.9,55.6-78.9,106.1-91.2c37.3-9.1,73.2-2.9,106.5,15.9C512,80.5,524.8,90.3,537.7,98.9z" />
        </G>
        <G>
          <Path d="M240.4,678.1v67.7h-23.2V606.4c0-0.7,0-1.3,0.1-2c0-0.1,0-0.2,0-0.3c0-0.4,0-0.8,0-1.2v-3.3h50.2v20.8h-27.1v36.8h25.1 v20.9H240.4z" />
          <Path d="M324.8,720h-21.4l-3,25.8h-22.5l0.5-3.8c0.3-2.5,0.7-4.9,1-7.4c0.2-1.3,0.4-2.6,0.6-3.9c0.7-4.7,1.3-9.5,2-14.2 c0.8-5.3,1.5-10.7,2.3-16l13.1-93.9c0.1-0.7,0.3-1.3,0.4-2c0.1-0.7,0.2-1.4,0.3-2.1l0.4-2.9h29.9l0.4,2.8 c0.6,4.2,1.2,8.4,1.9,12.6c0.8,5.5,1.6,11.1,2.5,16.7c0.9,5.9,1.8,11.9,2.6,17.8l13.3,89.7c0.1,0.7,0.2,1.3,0.3,2 c0,0.2,0,0.4,0.1,0.6c0,0.1,0,0.2,0,0.3l0.5,3.7h-21.7l-0.4-2.9c0-0.1,0-0.2,0-0.3C326.8,735.1,325.7,727.6,324.8,720z M306.6,693 c-0.3,2.3-0.6,4.7-0.9,7.1c0,0.2,0,0.4-0.1,0.6h16.7c-0.5-3.9-1-7.7-1.5-11.5c-0.5-4-1-8-1.6-11.9l-0.7-4.9c-0.5-4-1-8-1.6-11.9 c-0.9-7.4-1.9-14.8-2.8-22.2l-0.6-4L306.6,693z" />
          <Path d="M406.6,745.8h-28c-14.1-92.9-21.5-141.6-22.1-146.2h22.1l9.2,65.1l5.3,42.2l3.8-35.9c0.3-3.4,3.1-27.2,8.3-71.4h21.9 L406.6,745.8z" />
          <Path d="M474.3,747.4c-20,0-30.7-12-32.2-35.9c-0.2-3.9-0.3-7.7-0.3-11.5l0.1-5.6c0-30.9,0-48.2-0.1-52c0-3.7,0.1-7.5,0.4-11.3 c1.8-22.2,12.6-33.3,32.4-33.3c10.9,0.1,18.8,3.3,23.7,9.7c4.6,5.9,7.2,14.3,7.7,25.4c0.1,3.7,0.2,7.3,0.2,10.9v6.1 c0,30.3,0,47.2,0,50.7c0,4.7-0.2,9.4-0.5,14.1c-1,10.4-3.8,18.2-8.3,23.5C492.1,744.3,484.4,747.4,474.3,747.4z M473.9,726.7 c0.7,0,1.4-0.1,2.2-0.2c3.5-0.4,5.7-4,6.4-10.8c0.3-3.2,0.5-6.3,0.5-9.4v-70.2c0-2.1-0.1-4.4-0.3-6.8c-0.5-5-1.8-8.2-4-9.5 c-1.4-0.8-2.8-1.2-4.3-1.2c-1.8,0-3.4,0.5-4.8,1.6c-1.9,1.7-3.1,4.2-3.5,7.3c-0.5,3.1-0.7,6-0.7,8.7c0,44.2,0,67.5-0.1,69.7 c0,4.8,0.5,9.5,1.4,14C467.4,724.5,469.9,726.7,473.9,726.7z" />
          <Path d="M589.8,745.8h-23c-0.5-7.3-0.7-23.1-0.7-47.6v-4.3c0-7.9-1.1-12.7-3.2-14.2c-1.4-1-3.1-1.6-5.1-1.8l-8.3-0.4v68.2h-23.2 c0-91.1,0.1-139.8,0.2-146.2h34.9c2.6,0,5.4,0.2,8.4,0.5c3,0.4,6,1.5,8.9,3.3c4.4,3.1,7.4,7.9,8.7,14.6c1.2,6.6,1.8,13.7,1.8,21.4 c0,5.4-0.6,10.8-1.8,16.3c-1.5,6.1-4.7,10.3-9.5,12.6c6,2.3,9.5,9,10.4,19.9c0.7,8.3,1.1,19.6,1.1,34c-0.1,6.1,0,11.5,0.2,16.1 C589.5,739.4,589.6,741.9,589.8,745.8z M556.4,658.9c2.1,0,3.9-0.3,5.4-1c1.8-1,3-3.6,3.6-7.7c0.4-3.8,0.6-7.6,0.6-11.3 c0-4.6-0.2-9.1-0.7-13.4c-0.5-3-1.4-4.9-2.7-5.8c-1.8-1.1-4.7-1.6-8.9-1.6l-4.2,0.1v40.7H556.4z" />
          <Path d="M633.8,745.8h-23.1c0-92.6,0-141.3,0.1-146.2h23.1C633.9,689.8,633.9,738.5,633.8,745.8z" />
          <Path d="M686.2,745.8h-23c0-73.8,0-114,0.1-120.7v-4.7h-16.7v-20.8h55.7v20.8h-16C686.3,701.2,686.3,743,686.2,745.8z" />
          <Path d="M738.4,680.9v44.4h27v20.6h-50.1V606.4c0-0.7,0-1.3,0-2c0-0.1,0-0.2,0-0.3c0-0.4,0-0.8,0-1.2l0.1-3.3h49v20.7h-26v40.4 h24.7v20.2H738.4z" />
          <Path d="M810.9,747.5c-4.6,0-8.8-0.7-12.6-2.1c-9.3-3.3-15-12.3-17.2-27c-0.7-5.6-1-11.2-1-16.7l0.1-11.1h22.1v17.1 c0,7.9,1,13.4,2.9,16.5c1.2,1.8,3.1,2.7,5.7,2.7c1,0,2.3-0.2,3.6-0.5c1.4-0.4,2.5-1.4,3.3-3.2c1.1-3.5,1.7-7.1,1.7-11 c0-7.3-1.3-13.4-3.8-18.3c-2.1-4-5.3-8.3-9.5-12.8c-3.5-3.5-6.9-7.1-10.4-10.9c-9.6-10.1-14.8-20.8-15.6-32 c-0.1-1.7-0.2-3.4-0.2-5.1c0-4.8,0.5-9.3,1.5-13.6c2.4-10.7,7.9-17.3,16.5-19.9c4.1-1.2,7.9-1.8,11.5-1.8c3.6,0.1,6.2,0.2,7.9,0.5 c8.6,1.3,14.5,5.4,17.8,12.3c2.6,5.6,4.1,12.1,4.4,19.6c0.3,4.5,0.4,10.6,0.4,18.1h-21.5v-8.6l-0.1-7.4c-0.1-4-0.7-7.6-2-10.7 c-1-2.1-2.9-3.2-5.6-3.3c-1,0-1.7,0.1-2.3,0.2c-4.4,0.8-6.7,5-6.7,12.6c0,2.3,0.3,4.5,0.8,6.6c1,4.1,4.1,9,9.4,14.5 c0.1,0,0.1,0,0.1,0.1c0.9,0.9,4.8,5.2,11.8,12.9c5.5,6.1,9.7,12.2,12.7,18.5c3.5,7.2,5.2,15.2,5.2,24.1c0,6.6-0.6,12.6-1.9,17.9 c-1.5,6.3-3.8,11-7,14.3C828.1,745,820.7,747.5,810.9,747.5z" />
        </G>
      </G>
      {props?.isFocused ? (
        <G>
          <Path d="M530.8,99.3c15.5-13.5,33-25.5,52.7-34C648.1,37.6,718.3,56.6,761,112c28.2,36.7,38.3,78.4,34.2,124.2 c-2.8,30.7-11.4,59.5-26.4,86.4c-17.6,31.4-41,58.1-67.6,82.1c-35.2,31.8-73.4,60-111.3,88.4c-12.9,9.7-27.1,17.5-43,21.3 c-21.7,5.3-42.1,0.6-60.6-10.9c-16.6-10.4-32.5-22-47.9-34.1c-26.7-20.9-53.5-41.8-79.1-64.1c-34.1-29.8-63.1-64.1-80.1-106.7 c-18-45.2-22.6-91.5-6.7-138.2c14.9-43.7,43-76.6,85.7-95.4c44.8-19.7,89.1-15.8,131.6,7.4C504,80,516.8,90,530.8,99.3z" />
        </G>
      ) : null}
    </Svg>
  );
};
