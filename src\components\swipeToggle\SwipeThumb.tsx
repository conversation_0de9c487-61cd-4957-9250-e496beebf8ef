/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Animated,
  I18nManager,
  Image,
  ImageSourcePropType,
  PanResponder,
  PanResponderGestureState,
  StyleSheet,
  View,
} from 'react-native';
import { moderateScale } from 'react-native-size-matters';

import { ColorScheme, THEME_MODE } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

const RESET_AFTER_SUCCESS_DEFAULT_DELAY = 1000;
const DEFAULT_ANIMATION_DURATION = 400;
const SWIPE_SUCCESS_THRESHOLD = 70;
const TRANSPARENT_COLOR = '#00000000';
const margin = 1;

interface SwipeThumbProps {
  disabled?: boolean;
  disableResetOnTap?: boolean;
  enableReverseSwipe?: boolean;
  finishRemainingSwipeAnimationDuration?: number;
  forceCompleteSwipe?: (forceComplete: () => void) => void;
  forceReset?: (forceReset: () => void) => void;
  layoutWidth?: number;
  onSwipeFail?: () => void;
  onSwipeStart?: () => void;
  onSwipeSuccess?: (isForceComplete: boolean) => void;
  resetAfterSuccessAnimDelay?: number;
  shouldResetAfterSuccess?: boolean;
  swipeSuccessThreshold?: number;
  thumbIconComponent?: () => ReactElement;
  thumbIconHeight?: number;
  thumbIconImageSource?: ImageSourcePropType | undefined;
  thumbIconWidth?: number;
}

const SwipeThumb: React.FC<SwipeThumbProps> = React.memo(props => {
  const {
    disabled = false,
    disableResetOnTap = false,
    enableReverseSwipe,
    finishRemainingSwipeAnimationDuration = DEFAULT_ANIMATION_DURATION,
    forceCompleteSwipe,
    forceReset,
    layoutWidth = 0,
    onSwipeFail,
    onSwipeStart,
    onSwipeSuccess,
    resetAfterSuccessAnimDelay,
    shouldResetAfterSuccess,
    swipeSuccessThreshold,
    thumbIconComponent: ThumbIconComponent,
    thumbIconHeight,
    thumbIconImageSource,
    thumbIconWidth,
  } = props;

  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const paddingAndMarginsOffset = 2 * margin;
  let defaultContainerWidth = moderateScale(56);
  if (thumbIconWidth === undefined && thumbIconHeight !== undefined) {
    defaultContainerWidth = thumbIconHeight;
  } else if (thumbIconWidth !== undefined) {
    defaultContainerWidth = thumbIconWidth;
  }
  const maxWidth = layoutWidth - paddingAndMarginsOffset;
  const { isRTL } = I18nManager;

  const animatedWidth = useRef(
    new Animated.Value(defaultContainerWidth),
  ).current;
  const [shouldDisableTouch, disableTouch] = useState(false);

  const [backgroundColor, setBackgroundColor] = useState(TRANSPARENT_COLOR);

  useEffect(() => {
    forceReset && forceReset(reset);
  }, [forceReset]);

  useEffect(() => {
    forceCompleteSwipe && forceCompleteSwipe(forceComplete);
  }, [forceCompleteSwipe]);

  function updateWidthWithAnimation(newWidth: number) {
    Animated.timing(animatedWidth, {
      toValue: newWidth,
      duration: finishRemainingSwipeAnimationDuration,
      useNativeDriver: false,
    }).start();
  }

  function updateWidthWithoutAnimation(newWidth: number) {
    Animated.timing(animatedWidth, {
      toValue: newWidth,
      duration: 0,
      useNativeDriver: false,
    }).start();
  }

  function onSwipeNotMetSuccessThreshold() {
    // Animate to initial position
    updateWidthWithAnimation(defaultContainerWidth);
    setBackgroundColor(TRANSPARENT_COLOR);
    onSwipeFail && onSwipeFail();
  }

  function onSwipeMetSuccessThreshold(newWidth: number) {
    if (newWidth !== maxWidth) {
      // Animate to final position
      finishRemainingSwipe();

      return;
    }
    invokeOnSwipeSuccess(false);
  }

  function onPanResponderStart() {
    if (disabled) {
      return;
    }
    onSwipeStart && onSwipeStart();
  }

  const onPanResponderMove = useCallback(
    (_: any, gestureState: PanResponderGestureState) => {
      if (disabled) return;

      const reverseMultiplier = enableReverseSwipe ? -1 : 1;
      const rtlMultiplier = isRTL ? -1 : 1;
      const newWidth =
        defaultContainerWidth +
        rtlMultiplier * reverseMultiplier * gestureState.dx;
      if (gestureState.dx > 0 && gestureState.moveX < maxWidth) {
        setBackgroundColors();
        updateWidthWithoutAnimation(gestureState.moveX);
      }
    },
    [
      disabled,
      defaultContainerWidth,
      maxWidth,
      isRTL,
      enableReverseSwipe,
      reset,
      setBackgroundColors,
      animatedWidth,
    ],
  );

  function onPanResponderRelease(
    _: any,
    gestureState: PanResponderGestureState,
  ) {
    if (disabled) {
      return;
    }
    const threshold = swipeSuccessThreshold
      ? swipeSuccessThreshold
      : SWIPE_SUCCESS_THRESHOLD;
    const successThresholdWidth = maxWidth * (threshold / 100);
    gestureState.moveX < successThresholdWidth
      ? onSwipeNotMetSuccessThreshold()
      : onSwipeMetSuccessThreshold(gestureState.moveX);
  }

  function setBackgroundColors() {
    setBackgroundColor(TRANSPARENT_COLOR);
  }

  function finishRemainingSwipe() {
    // Animate to final position
    updateWidthWithAnimation(maxWidth);
    invokeOnSwipeSuccess(false);

    //Animate back to initial position after successfully swiped
    const resetDelay =
      DEFAULT_ANIMATION_DURATION +
      (resetAfterSuccessAnimDelay !== undefined
        ? resetAfterSuccessAnimDelay
        : RESET_AFTER_SUCCESS_DEFAULT_DELAY);
    setTimeout(() => {
      shouldResetAfterSuccess && reset();
    }, resetDelay);
  }

  function invokeOnSwipeSuccess(isForceComplete: boolean) {
    disableTouch(disableResetOnTap);
    onSwipeSuccess && onSwipeSuccess(isForceComplete);
  }

  function reset() {
    disableTouch(false);
    updateWidthWithAnimation(defaultContainerWidth);
  }

  function forceComplete() {
    updateWidthWithAnimation(maxWidth);
    invokeOnSwipeSuccess(true);
  }

  const renderThumbIcon = useCallback(() => {
    return (
      <View>
        {!ThumbIconComponent && thumbIconImageSource && (
          <Image resizeMethod="resize" source={thumbIconImageSource} />
        )}
        {ThumbIconComponent && (
          <View>
            <ThumbIconComponent />
          </View>
        )}
      </View>
    );
  }, [ThumbIconComponent, thumbIconImageSource]);

  const panResponder = useCallback(
    PanResponder.create({
      onStartShouldSetPanResponder: (e: any, s: any) => true,
      onStartShouldSetPanResponderCapture: (e: any, s: any) => true,
      onMoveShouldSetPanResponder: (e: any, s: any) => true,
      onMoveShouldSetPanResponderCapture: (e: any, s: any) => true,
      onShouldBlockNativeResponder: (e: any, s: any) => true,
      onPanResponderGrant: onPanResponderStart,
      onPanResponderMove: onPanResponderMove,
      onPanResponderRelease: onPanResponderRelease,
    }) as any,
    [props],
  );

  const panStyle = {
    width: animatedWidth,
    height: '95%',
    backgroundColor,
    marginLeft: moderateScale(10),
  };

  return (
    <Animated.View
      style={[panStyle, styles.container]}
      {...panResponder.panHandlers}
      pointerEvents={shouldDisableTouch ? 'none' : 'auto'}>
      {renderThumbIcon()}
    </Animated.View>
  );
});

export default SwipeThumb;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      alignItems: 'flex-end',
      alignSelf: 'flex-start',
      justifyContent: 'center',
      borderRadius: 50,
      margin,
    },
    containerRTL: {
      alignItems: 'flex-start',
      alignSelf: 'flex-end',
      justifyContent: 'center',
      borderRadius: 50,
      margin,
    },
  });
