import { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import { LikeSymbolIcon } from '../../../../assets/svgs';
import { ColorScheme } from '../../../../constants/theme/colors';
import { useAppSelector } from '../../../../hooks/redux';
import { Reaction } from '../../../../types/posts';
import { formatViews } from '../../../../utils/posts';
import CustomText from '../../../common/text';

dayjs.extend(relativeTime);

interface PostInsightsProps {
  views: string;
  reactions?: Reaction;
  time?: string;
}

export const PostInsights: FC<PostInsightsProps> = ({
  views,
  reactions,
  time,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      <View>
        <CustomText style={styles.viewsText}>
          {formatViews(Number(views))} Views
        </CustomText>
      </View>
      <View style={styles.separator} />
      {time ? (
        <View>
          <CustomText style={styles.viewsText}>{time}</CustomText>
        </View>
      ) : null}
      {reactions ? (
        <View style={styles.reactionContainer}>
          <View style={styles.likeContainer}>
            <LikeSymbolIcon
              width={moderateScale(14)}
              height={moderateScale(14)}
              fill={colors.foreground}
            />
          </View>
          <View>
            <CustomText style={styles.reactionText}>
              {reactions?.count} Reactions
            </CustomText>
          </View>
        </View>
      ) : null}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(2),
      marginBottom: verticalScale(-2),
    },
    likeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    viewsText: {
      color: colors.foreground,
      fontSize: moderateScale(10),
      fontWeight: '600',
    },
    reactionText: {
      color: colors.secondary,
      fontSize: moderateScale(11),
      fontWeight: '400',
    },
    reactionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(3),
    },
    reactionIconsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    firstReact: {
      width: moderateScale(18),
      height: moderateScale(18),
      resizeMode: 'contain',
    },
    secondReaction: {
      marginLeft: moderateScale(-8),
      width: moderateScale(18),
      height: moderateScale(18),
      resizeMode: 'contain',
    },
    separator: {
      height: verticalScale(8),
      width: verticalScale(1),
      backgroundColor: colors.secondary,
      borderRadius: moderateScale(5),
      marginHorizontal: moderateScale(4),
    },
    reactImageStyle: {
      width: moderateScale(10),
      height: moderateScale(10),
    },
  });
