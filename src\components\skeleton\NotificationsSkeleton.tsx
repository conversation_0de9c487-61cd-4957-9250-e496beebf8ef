import React, { FC, Fragment, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const NotificationsSkeleton: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const data = Array.from({ length: 11 }).map((_, index) => index);

  return (
    <View style={styles.container}>
      {data.map(item => (
        <Fragment key={item}>
          <SkeletonPlaceholder
            backgroundColor={colors.surface}
            highlightColor={colors.highlightSurface}>
            <SkeletonPlaceholder.Item
              flexDirection="row"
              justifyContent="space-between"
              marginVertical={verticalScale(6)}
              gap={moderateScale(14)}>
              <SkeletonPlaceholder.Item
                height={moderateScale(40)}
                width={moderateScale(40)}
                borderRadius={moderateScale(50)}
              />

              <View style={styles.subContainer}>
                <SkeletonPlaceholder.Item
                  height={verticalScale(8)}
                  width={moderateScale(200)}
                  borderRadius={moderateScale(5)}
                />
                <SkeletonPlaceholder.Item
                  height={verticalScale(8)}
                  width={moderateScale(170)}
                  borderRadius={moderateScale(5)}
                  marginTop={verticalScale(5)}
                />
                <SkeletonPlaceholder.Item
                  height={verticalScale(6)}
                  width={moderateScale(80)}
                  borderRadius={moderateScale(5)}
                  marginTop={verticalScale(5)}
                />
              </View>
              <View style={styles.subContainer}>
                <SkeletonPlaceholder.Item
                  height={moderateScale(50)}
                  width={moderateScale(85)}
                  borderRadius={moderateScale(5)}
                />
              </View>
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder>
        </Fragment>
      ))}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(10),
    },
    subContainer: {
      justifyContent: 'center',
    },
  });
