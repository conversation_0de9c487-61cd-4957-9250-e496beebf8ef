import React, { useEffect, useMemo, useState } from 'react';
import { KeyboardAvoidingView, StyleSheet, View } from 'react-native';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Toast from 'react-native-toast-message';

import CustomButton from '../../components/common/button';
import { ColorScheme } from '../../constants/theme/colors';
import { TOAST_TITLE, TOAST_TYPE } from '../../constants/toast';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { fetchUserDetails } from '../../redux/slices/authSlice';
import {
  FeaturedCardValues,
  getUserFeaturedCards,
  updateUserProfile,
} from '../../redux/slices/userSlice';

import { FeaturedCard } from './components/FeaturedCard';
import SocialIconsModal from './components/SocialIconsModal';
import { UpdateProfileFooter } from './components/UpdateProfileFooter';
import { UpdateProfileHeader } from './components/UpdateProfileHeader';

export interface IUserData {
  featuredCards: FeaturedCardValues[];
  username: string;
  bio: string;
  avatar: string;
}

const UpdateProfile = () => {
  const dispatch = useAppDispatch();
  const [isVisible, setIsVisible] = useState(false);
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { userDetails, user } = useAppSelector(state => state.auth);
  const { featuredCards } = useAppSelector(state => state.user);
  const { updatingStatus } = useAppSelector(state => state.user);

  const navigation = useTypeSafeNavigation();
  const [userData, setUserData] = useState<IUserData>({
    featuredCards: [],
    username: '',
    bio: '',
    avatar: '',
  });

  useEffect(() => {
    dispatch(getUserFeaturedCards({}));
  }, [dispatch]);

  useEffect(() => {
    if (featuredCards.length < 3) {
      const demoCards: FeaturedCardValues[] = Array.from(
        { length: 3 - featuredCards.length },
        (_, index) => ({
          id: featuredCards.length + index + 1,
          title: '',
          link: '',
          image: '',
          sort: featuredCards.length + index + 1,
          enabled: 0,
          isNew: true,
        }),
      );
      setUserData(prev => ({
        ...prev,
        featuredCards: [...featuredCards, ...demoCards],
      }));
    } else {
      setUserData(prev => ({ ...prev, featuredCards }));
    }
  }, [featuredCards]);

  useEffect(() => {
    setUserData(prev => ({
      ...prev,
      username: userDetails?.username || '',
      bio: userDetails?.about || '',
      avatar: userDetails?.avatar || '',
    }));
  }, [userDetails]);

  const toggleModal = () => setIsVisible(prev => !prev);

  const handleOnDragEnd = ({ data }: { data: FeaturedCardValues[] }) => {
    setUserData(prev => ({ ...prev, featuredCards: data }));
  };

  const onSubmit = async () => {
    if (userData.bio.length > 100) {
      Toast.show({
        type: TOAST_TYPE.ERROR,
        text1: TOAST_TITLE.PROFILE,
        text2: 'Bio should not exceed 100 characters',
      });

      return;
    }
    await dispatch(updateUserProfile(userData));

    await dispatch(
      fetchUserDetails({
        data: {
          user_id: user?.userId as string,
          fetch: 'user_data',
        },
      }),
    );
    navigation.goBack();
  };

  return (
    <>
      <KeyboardAvoidingView
        keyboardVerticalOffset={80}
        enabled
        behavior="position"
        style={styles.container}>
        <View style={styles.subContainer}>
          <DraggableFlatList
            style={styles.list}
            data={userData.featuredCards}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={
              <UpdateProfileHeader
                userData={userData}
                setUserData={setUserData}
              />
            }
            ListFooterComponent={
              <View style={styles.buttonWrapper}>
                <CustomButton
                  title="Save"
                  onPress={onSubmit}
                  loading={updatingStatus === 'loading'}
                />
              </View>
            }
            onDragEnd={handleOnDragEnd}
            keyExtractor={item => item?.id?.toString()!}
            renderItem={item => (
              <FeaturedCard {...item} setUserData={setUserData} />
            )}
            // eslint-disable-next-line react/no-unstable-nested-components
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
          <UpdateProfileFooter toggleModal={toggleModal} />
        </View>
      </KeyboardAvoidingView>

      <SocialIconsModal isVisible={isVisible} setIsVisible={setIsVisible} />
    </>
  );
};

export default UpdateProfile;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    subContainer: {
      marginTop: moderateScale(14),
    },
    separator: {
      height: verticalScale(14),
    },
    buttonWrapper: {
      margin: moderateScale(20),
      marginTop: verticalScale(14),
    },
    list: { marginBottom: verticalScale(40) },
  });
