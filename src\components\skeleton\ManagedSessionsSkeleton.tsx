import React, { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const ManagedSessionsSkeleton: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const data = Array.from({ length: 4 }).map((_, index) => index);

  return (
    <View style={styles.container}>
      {data.map(item => (
        <SkeletonPlaceholder
          key={item}
          backgroundColor={colors.surface}
          highlightColor={colors.highlightSurface}>
          <SkeletonPlaceholder.Item marginVertical={verticalScale(10)}>
            <SkeletonPlaceholder.Item
              height={moderateScale(160)}
              borderRadius={moderateScale(5)}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder>
      ))}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(20),
    },
  });
