import { createStackNavigator } from '@react-navigation/stack';

import { useAppSelector } from '../hooks/redux';
import ManageSessions from '../screens/ManageSessions';
import NotificationSettings from '../screens/NotificationSettings';
import Profile from '../screens/Profile';
import ChangePassword from '../screens/Profile/ChangePassword';
import DeleteAccount from '../screens/Profile/DeleteAccount';
import GeneralSettings from '../screens/Profile/GeneralSettings';
import UpdateProfile from '../screens/Profile/UpdateProfile';
import { APP_ROUTES, AppStackNavigatorParamList } from '../types/routes';

import { CombinedHeaderNavigationProp, Header } from './components/Header';

const ProfileStack = createStackNavigator<AppStackNavigatorParamList>();

export const ProfileStackNavigator = () => {
  const { colors } = useAppSelector(state => state.theme);
  const renderHeader = (
    navigation: CombinedHeaderNavigationProp,
    routeName?: string,
    showLogo?: boolean,
  ) => {
    return (
      <Header
        routeName={routeName}
        colors={colors}
        navigation={navigation}
        showLogo={showLogo}
      />
    );
  };

  return (
    <ProfileStack.Navigator>
      <ProfileStack.Screen
        name={APP_ROUTES.PROFILE_SETTINGS}
        component={Profile}
        options={({ navigation }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, '', true),
        })}
      />
      <ProfileStack.Screen
        name={APP_ROUTES.UPDATE_PROFILE}
        component={UpdateProfile}
        options={({ navigation, route }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, route.name),
        })}
      />
      <ProfileStack.Screen
        name={APP_ROUTES.NOTIFICATION_SETTINGS}
        component={NotificationSettings}
        options={({ navigation, route }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, route.name),
        })}
      />
      <ProfileStack.Screen
        name={APP_ROUTES.MANAGE_SESSIONS}
        component={ManageSessions}
        options={({ navigation, route }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, route.name),
        })}
      />
      <ProfileStack.Screen
        name={APP_ROUTES.GENERAL_SETTINGS}
        component={GeneralSettings}
        options={({ navigation, route }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, route.name),
        })}
      />
      <ProfileStack.Screen
        name={APP_ROUTES.CHANGE_PASSWORD}
        component={ChangePassword}
        options={({ navigation, route }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, route.name),
        })}
      />
      <ProfileStack.Screen
        name={APP_ROUTES.DELETE_ACCOUNT}
        component={DeleteAccount}
        options={({ navigation, route }) => ({
          headerShown: true,
          header: () => renderHeader(navigation, route.name),
        })}
      />
    </ProfileStack.Navigator>
  );
};
