import React, { FC, useEffect, useMemo } from 'react';
import { Linking, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon6 from 'react-native-vector-icons/FontAwesome6';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { VerifyIconWithStroke } from '../../assets/svgs';
import DarkLogo from '../../assets/svgs/DarkLogo';
import Logo from '../../assets/svgs/Logo';
import CustomText from '../../components/common/text';
import { ProfileSkeleton } from '../../components/skeleton';
import { ColorScheme, THEME_MODE } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchUserDetails } from '../../redux/slices/authSlice';
import { getUserFeaturedCards } from '../../redux/slices/userSlice';
import {
  APP_ROUTES,
  AppStackNavigatorParamList,
  STACKS,
} from '../../types/routes';
import { openInAppBrowser } from '../../utils/media';

import { ShowFeaturedCards } from './components/ShowFeaturedCards';
import { UserPolls } from './components/UserPolls';

type ViewProfileProps = NativeStackScreenProps<
  AppStackNavigatorParamList,
  APP_ROUTES.OTHER_USER_PROFILE | APP_ROUTES.VIEW_PROFILE,
  STACKS.APP
>;

const ViewProfile: FC<ViewProfileProps> = ({ route }) => {
  const { userId, otherUserProfile } = route.params;
  const dispatch = useAppDispatch();
  const { colors, theme } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { userDetails, otherUserDetails, status } = useAppSelector(
    state => state.auth,
  );

  useEffect(() => {
    if (!userId) return;
    dispatch(
      fetchUserDetails({
        data: { user_id: userId, fetch: 'user_data' },
        otherUserProfile,
      }),
    );
  }, [userId]);

  useEffect(() => {
    dispatch(
      getUserFeaturedCards({
        user_id: otherUserProfile ? userId : undefined,
      }),
    );
  }, [dispatch, userId]);

  const userData = useMemo(() => {
    if (otherUserProfile) return otherUserDetails;

    return userDetails;
  }, [userDetails, otherUserDetails]);

  const handleOpenUrl = async (url?: string) => {
    if (!url) return;
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    } else {
      openInAppBrowser(url);
    }
  };

  if (status === 'loading') return <ProfileSkeleton />;

  return (
    <ScrollView style={styles.container}>
      <View style={styles.avatarContainer}>
        <FastImage source={{ uri: userData?.avatar }} style={styles.image} />
        {Number(userData?.verified) ? (
          <VerifyIconWithStroke
            width={26}
            height={26}
            style={styles.verifiedIcon}
          />
        ) : null}
      </View>
      <View style={styles.titleContainer}>
        <CustomText numberOfLines={1} style={styles.title}>
          {userData?.username}
        </CustomText>
      </View>
      <CustomText style={styles.description}>{userData?.about}</CustomText>
      <View style={styles.socialIcons}>
        {Number(userData?.is_instagram) ? (
          <Pressable onPress={() => handleOpenUrl(userData?.instagram)}>
            <Icon6 name="instagram" size={32} color={colors.secondary} />
          </Pressable>
        ) : null}
        {Number(userData?.is_facebook) ? (
          <Pressable onPress={() => handleOpenUrl(userData?.facebook)}>
            <Icon6 name="facebook" size={32} color={colors.secondary} />
          </Pressable>
        ) : null}
        {Number(userData?.is_twitter) ? (
          <Pressable onPress={() => handleOpenUrl(userData?.twitter)}>
            <Icon6 name="twitter" size={32} color={colors.secondary} />
          </Pressable>
        ) : null}
        {Number(userData?.is_youtube) ? (
          <Pressable onPress={() => handleOpenUrl(userData?.youtube)}>
            <Icon6 name="youtube" size={32} color={colors.secondary} />
          </Pressable>
        ) : null}
        {Number(userData?.is_tiktok) ? (
          <Pressable onPress={() => handleOpenUrl(userData?.tiktok)}>
            <Icon6 name="tiktok" size={32} color={colors.secondary} />
          </Pressable>
        ) : null}
      </View>
      <ShowFeaturedCards />
      {userData?.user_id && (
        <UserPolls userId={userData.user_id} isOtherUser={otherUserProfile} />
      )}
      
      <View style={styles.logoWrapper}>
        {theme === THEME_MODE.dark ? <DarkLogo /> : <Logo />}
      </View>
    </ScrollView>
  );
};

export default ViewProfile;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    image: {
      width: moderateScale(90),
      height: moderateScale(90),
      borderRadius: moderateScale(50),
      marginHorizontal: 'auto',
    },
    avatarContainer: {
      position: 'relative',
      marginHorizontal: 'auto',
      marginBottom: moderateScale(16),
    },
    verifiedIcon: {
      position: 'absolute',
      left: 0,
      bottom: 0,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: moderateScale(5),
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(24),
      fontWeight: '600',
      textAlign: 'center',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      maxWidth: moderateScale(250),
      gap: moderateScale(10),
    },
    description: {
      color: colors.foreground,
      fontSize: moderateScale(14),
      fontWeight: '400',
      textAlign: 'center',
      marginVertical: moderateScale(10),
      marginHorizontal: moderateScale(44),
      lineHeight: moderateScale(20),
    },
    socialIcons: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: moderateScale(35),
      marginVertical: moderateScale(20),
    },
    logoWrapper: {
      alignItems: 'center',
      marginTop: verticalScale(10),
      marginBottom: verticalScale(10),
    },
  });
