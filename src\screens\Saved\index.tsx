import React, { useEffect, useMemo, useRef } from 'react';
import { Animated, FlatList, Pressable, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import CustomText from '../../components/common/text';
import { OfflineView } from '../../components/OfflineView';
import { PostCard } from '../../components/posts/card';
import { SavedSkeleton } from '../../components/skeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { fetchLikedChannels } from '../../redux/slices/channelSlice';
import { fetchSavedPosts } from '../../redux/slices/postsSlice';
import { APP_ROUTES } from '../../types/routes';

import FavoriteCard from './FavoriteCard';

const viewabilityConfig = {
  itemVisiblePercentThreshold: 50,
};

const Saved = () => {
  const { isConnected } = useConnectionStatus();
  const dispatch = useAppDispatch();
  const navigation = useTypeSafeNavigation();
  const { savedPosts, savedPostStatus } = useAppSelector(state => state.posts);
  const { likedChannels, status: channelStatus } =
    useAppSelector(state => state.channels) ?? {};
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  useEffect(() => {
    dispatch(fetchSavedPosts({ type: 'saved', id: '2' }));
    dispatch(fetchLikedChannels({ type: 'liked' }));
  }, [dispatch]);

  const handleViewAll = () => {
    navigation.navigate(APP_ROUTES.FAVORITE_CHANNELS);
  };

  const scrollY = useRef(new Animated.Value(0)).current;

  const scaleValue = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.8],
    extrapolate: 'clamp',
  });

  const marginTopValue = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -25],
    extrapolate: 'clamp',
  });

  const heightValue = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [verticalScale(100), verticalScale(50)],
    extrapolate: 'clamp',
  });

  const marginValue = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [6, 0],
    extrapolate: 'clamp',
  });

  const hideValue = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  if (savedPostStatus === 'loading' || channelStatus === 'loading')
    return <SavedSkeleton />;

  if (savedPostStatus === 'failed')
    return <CustomText>Error loading posts.</CustomText>;
  if (!isConnected) return <OfflineView />;

  return (
    <View style={[styles.container]}>
      <Animated.View style={[styles.topView, { height: heightValue }]}>
        {likedChannels?.length ? (
          <Animated.View
            style={[
              styles.topTextView,
              {
                opacity: hideValue,
                marginTop: marginTopValue,
              },
            ]}>
            <CustomText style={styles.favoriteText}>Your Favorites</CustomText>
            <Pressable onPress={handleViewAll}>
              <CustomText style={styles.seeAll}>See All</CustomText>
            </Pressable>
          </Animated.View>
        ) : null}
        <FlatList
          data={likedChannels?.slice(0, 12)}
          horizontal
          keyExtractor={item => item.id.toString()}
          renderItem={({ item }) => (
            <FavoriteCard
              {...item}
              ITEMS_PER_ROW={5}
              scaleValue={scaleValue}
              marginValue={marginValue}
              hideValue={hideValue}
            />
          )}
          showsHorizontalScrollIndicator={false}
        />
      </Animated.View>
      <FlatList
        data={savedPosts}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        viewabilityConfig={viewabilityConfig}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false },
        )}
        renderItem={({ item }) => {
          if (item) {
            return <PostCard {...item} />;
          }

          return null;
        }}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <CustomText style={{ color: colors.foreground }}>
              No favorite posts found
            </CustomText>
          </View>
        }
      />
    </View>
  );
};

export default Saved;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    topView: {
      backgroundColor: colors.background,
      overflow: 'hidden',
    },
    topTextView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginHorizontal: moderateScale(15),
      marginTop: verticalScale(10),
    },
    favoriteText: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      fontWeight: '500',
    },
    seeAll: {
      color: colors.accent,
      fontSize: moderateScale(12),
      fontWeight: '500',
    },
    emptyContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
      height: verticalScale(300),
    },
    separator: {
      height: verticalScale(4),
      marginVertical: verticalScale(7),
      backgroundColor: colors.separator,
    },
  });
