export interface Post {
  id: string;
  post_id: string;
  youtube: string;
  user_id: string;
  recipient_id: string;
  postText: string;
  page_id: string;
  group_id: string;
  event_id: string;
  page_event_id: string;
  postLink: string;
  postLinkTitle: string;
  postLinkImage: string;
  postLinkContent: string;
  postVimeo: string;
  postDailymotion: string;
  postFacebook: string;
  postFile: string;
  postFileName: string;
  postFileThumb: string;
  postYoutube: string;
  postVine: string;
  postSoundCloud: string;
  postPlaytube: string;
  postDeepsound: string;
  postMap: string;
  postShare: string;
  postPrivacy: string;
  postType: string;
  postFeeling: string;
  postListening: string;
  postTraveling: string;
  postWatching: string;
  postPlaying: string;
  postPhoto: string;
  time: string;
  registered: string;
  album_name: string;
  multi_image: string;
  multi_image_post: string;
  boosted: string;
  product_id: string;
  poll_id: string;
  blog_id: string;
  forum_id: string;
  thread_id: string;
  videoViews: string;
  postRecord: string;
  postSticker: string | null;
  shared_from: boolean;
  post_url: string | null;
  parent_id: string;
  cache: string;
  comments_status: string;
  blur: string;
  color_id: string;
  job_id: string;
  offer_id: string;
  fund_raise_id: string;
  fund_id: string;
  active: string;
  stream_name: string;
  agora_token: string | null;
  live_time: string;
  live_ended: string;
  agora_resource_id: string | null;
  agora_sid: string;
  send_notify: string;
  '240p': string;
  '360p': string;
  '480p': string;
  '720p': string;
  '1080p': string;
  '2048p': string;
  '4096p': string;
  processing: string;
  last_views: string;
  yt_likes: string;
  duration: string;
  publisher: Publisher;
  limit_comments: number;
  limited_comments: boolean;
  is_group_post: boolean;
  group_recipient_exists: boolean;
  group_admin: boolean;
  post_is_promoted: number;
  postText_API: string;
  Orginaltext: string;
  post_time: string;
  page: number;
  url: string;
  seo_id: string;
  via_type: string;
  recipient_exists: boolean;
  recipient: string;
  admin: boolean;
  post_share: string;
  is_post_saved: boolean;
  is_post_reported: boolean;
  is_post_boosted: number;
  is_liked: boolean;
  is_wondered: boolean;
  post_comments: string;
  post_shares: string;
  post_likes: string;
  post_wonders: string;
  is_post_pinned: boolean;
  get_post_comments: string[];
  photo_album: string[];
  options: string[];
  voted_id: number;
  postFile_full: string;
  reaction: Reaction;
  job: string[];
  offer: string[];
  fund: string[];
  fund_data: string[];
  forum: string[];
  thread: string[];
  is_still_live: boolean;
  live_sub_users: number;
  have_next_image: boolean;
  have_pre_image: boolean;
  poll_active: boolean;
  shared_info: [] | null;
  user_data: [] | null;
  // Temporary
  thumbnail?: string;
}

export interface Reaction {
  '1': number;
  '2': number;
  '3': number;
  '4': number;
  '5': number;
  '6': number;
  is_reacted: boolean;
  type: string;
  count: number;
}

export interface Publisher {
  user_id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar: string;
  cover: string;
  background_image: string;
  relationship_id: string;
  address: string;
  working: string;
  working_link: string;
  about: string;
  school: string;
  gender: string;
  birthday: string;
  country_id: string;
  website: string;
  facebook: string;
  google: string;
  twitter: string;
  linkedin: string;
  youtube: string;
  vk: string;
  instagram: string;
  qq: string | null;
  wechat: string | null;
  discord: string | null;
  mailru: string | null;
  okru: string;
  language: string;
  ip_address: string;
  is_discord: null;
  is_facebook: string;
  is_following_me: 0;
  is_google: string;
  is_instagram: string;
  is_linkedin: string;
  is_mailru: null;
  is_okru: null;
  is_open_to_work: 0;
  is_providing_service: 0;
  is_qq: null;
  is_reported: false;
  is_reported_user: 0;
  is_story_muted: false;
  is_twitter: string;
  is_vk: string;
  is_wechat: null;
  is_youtube: string;
  follow_privacy: string;
  friend_privacy: string;
  post_privacy: string;
  message_privacy: string;
  confirm_followers: string;
  show_activities_privacy: string;
  birth_privacy: string;
  visit_privacy: string;
  verified: string;
  lastseen: string;
  emailNotification: string;
  e_liked: string;
  e_wondered: string;
  e_shared: string;
  e_followed: string;
  e_commented: string;
  e_visited: string;
  e_liked_page: string;
  e_mentioned: string;
  e_joined_group: string;
  e_accepted: string;
  e_profile_wall_post: string;
  e_sentme_msg: string;
  e_last_notif: string;
  notification_settings: NotificationSettings;
  status: string;
  active: string;
  admin: string;
  registered: string;
  phone_number: string;
  is_pro: string;
  pro_type: string;
  pro_remainder: string;
  timezone: string;
  referrer: string;
  ref_user_id: string;
  ref_level: string | null;
  balance: string;
  paypal_email: string;
  notifications_sound: string;
  order_posts_by: string;
  android_m_device_id: string;
  ios_m_device_id: string;
  android_n_device_id: string;
  ios_n_device_id: string;
  web_device_id: string;
  wallet: string;
  lat: string;
  lng: string;
  last_location_update: string;
  share_my_location: string;
  last_data_update: string;
  details: UserDetails;
  last_avatar_mod: string;
  last_cover_mod: string;
  points: string;
  daily_points: string;
  converted_points: string;
  point_day_expire: string;
  last_follow_id: string;
  share_my_data: string;
  last_login_data: string | null;
  two_factor: string;
  two_factor_hash: string;
  new_email: string;
  two_factor_verified: string;
  new_phone: string;
  info_file: string;
  city: string;
  state: string;
  zip: string;
  school_completed: string;
  weather_unit: string;
  paystack_ref: string;
  code_sent: string;
  time_code_sent: string;
  permission: Permissions;
  skills: string | null;
  languages: string | null;
  currently_working: string;
  banned: string;
  banned_reason: string;
  likes_count: string;
  last_count: string;
  oneId: string;
  color: string;
  avatar_post_id: number;
  cover_post_id: number;
  avatar_full: string;
  user_platform: string;
  url: string;
  name: string;
  API_notification_settings: NotificationSettings;
  is_notify_stopped: number;
  mutual_friends_data: string[];
  lastseen_status: string;
  lastseen_unix_time: string;
  open_to_work_data: string;
  providing_service: number;
  title: string;
}

interface NotificationSettings {
  e_liked: number;
  e_shared: number;
  e_wondered: number;
  e_commented: number;
  e_followed: number;
  e_accepted: number;
  e_mentioned: number;
  e_joined_group: number;
  e_liked_page: number;
  e_visited: number;
  e_profile_wall_post: number;
  e_memory: number;
}

interface UserDetails {
  post_count: string;
  album_count: string;
  following_count: string;
  followers_count: string;
  groups_count: string;
  likes_count: string;
  mutual_friends_count: number;
}

interface Permissions {
  [key: string]: number | string;
}

export interface YTVideoContent {
  provider_url: string;
  height: number;
  type: string;
  html: string;
  thumbnail_url: string;
  author_name: string;
  author_url: string;
  title: string;
  thumbnail_width: number;
  width: number;
  provider_name: string;
  thumbnail_height: number;
  version: string;
  url: string;
}
