import React from 'react';
import { Control, Controller, FieldErrors } from 'react-hook-form';

import Input from '../../../../components/common/input';

import { FormValues } from '.';

interface Props {
  step: number;
  control: Control<FormValues>;
  errors: FieldErrors;
  getValues: (name: string) => string;
}

const StepContent: React.FC<Props> = ({ step, control, errors, getValues }) => {
  switch (step) {
    case 1:
      return (
        <Controller
          control={control}
          name="username"
          rules={{ required: 'Username is required' }}
          render={({ field: { onChange, value, name } }) => (
            <Input
              name={name}
              value={value}
              placeholder="Email or Username"
              onChangeText={onChange}
              errors={errors}
              autoCapitalize="none"
            />
          )}
        />
      );
    case 2:
      return (
        <Controller
          control={control}
          name="verificationCode"
          rules={{ required: 'Verification code is required' }}
          render={({ field: { onChange, value, name } }) => (
            <Input
              name={name}
              value={value}
              placeholder="Verification Code"
              onChangeText={onChange}
              errors={errors}
            />
          )}
        />
      );
    case 3:
      return (
        <>
          <Controller
            control={control}
            name="newPassword"
            rules={{ required: 'New password is required' }}
            render={({ field: { onChange, value, name } }) => (
              <Input
                name={name}
                value={value}
                placeholder="New Password"
                onChangeText={onChange}
                secureTextEntry={true}
                errors={errors}
              />
            )}
          />
          <Controller
            control={control}
            name="confirmPassword"
            rules={{
              required: 'Confirm password is required',
              validate: value =>
                value === getValues('newPassword') || 'Passwords do not match',
            }}
            render={({ field: { onChange, value, name } }) => (
              <Input
                name={name}
                value={value}
                placeholder="Confirm New Password"
                onChangeText={onChange}
                secureTextEntry={true}
                errors={errors}
              />
            )}
          />
        </>
      );
  }
};

export default StepContent;
