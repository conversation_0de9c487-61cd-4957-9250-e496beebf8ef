import React, { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

import CustomText from './text';

interface DropdownItem {
  label: string;
  value: string;
}

interface Props extends Partial<React.ComponentProps<typeof Dropdown>> {
  data: DropdownItem[];
  placeholder?: string;
  label?: string;
}

const DropdownPicker: React.FC<Props> = ({
  data,
  placeholder,
  label,
  onChange,
  ...props
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <>
      {label ? (
        <CustomText style={styles.inputLabel}>{label}</CustomText>
      ) : null}
      <View style={styles.container}>
        <Dropdown
          {...props}
          containerStyle={styles.dropdownContainer}
          selectedTextStyle={{ color: colors.foreground }}
          activeColor={colors.base}
          data={data}
          maxHeight={300}
          labelField={'label' as never}
          valueField={'value' as never}
          placeholder={placeholder}
          iconStyle={styles.iconStyle}
          itemTextStyle={styles.dropdown}
          onChange={item => onChange?.((item as DropdownItem).value)}
        />
      </View>
    </>
  );
};

export default DropdownPicker;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      marginVertical: verticalScale(7),
      backgroundColor: colors.base,
      color: colors.foreground,
      fontSize: moderateScale(14),
      borderRadius: moderateScale(100),
      paddingHorizontal: moderateScale(20),
      height: moderateScale(50),
      justifyContent: 'center',
    },
    inputLabel: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontWeight: '400',
      marginTop: verticalScale(10),
    },
    iconStyle: {
      width: moderateScale(24),
      height: moderateScale(24),
    },
    dropdownContainer: {
      backgroundColor: colors.background,
      borderColor: colors.background,
      paddingVertical: verticalScale(10),
      borderRadius: moderateScale(16),
    },
    dropdown: {
      color: colors.foreground,
    },
  });
