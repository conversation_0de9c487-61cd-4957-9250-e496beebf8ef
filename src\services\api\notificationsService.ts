import { CPN, REST_SUB_URL } from '../../constants/common/api';
import {
  GetNotificationsResponse,
  Notifications,
} from '../../types/notifications';

import { API } from './ApiInstance';

export const getNotificationsService = async (): Promise<Notifications[]> => {
  const response = await API.Post<unknown, GetNotificationsResponse>(
    `${CPN}${REST_SUB_URL.GET_NOTIFICATIONS}`,
  );
  if (response.status) return response.data?.list || [];

  return [];
};

export const actionNotificationService = async (payload: {
  id?: string;
  ids?: string;
  type: 'delete' | 'seen';
}) => {
  const response = await API.Post(
    `${CPN}${REST_SUB_URL.GET_NOTIFICATIONS}`,
    payload,
  );
  if (response.status) return response.data;

  return [];
};
