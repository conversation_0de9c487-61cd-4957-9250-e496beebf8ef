import * as React from 'react';
import Svg, { <PERSON><PERSON><PERSON><PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const BusinessIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.25}
      clipPath="url(#a)">
      <Path d="M1.063 17.438h16.875M5 12.563H2.75a.57.57 0 0 0-.563.562v4.313h3.376v-4.313A.57.57 0 0 0 5 12.562ZM10.625 6.563h-2.25a.57.57 0 0 0-.563.562v10.313h3.375V7.124a.57.57 0 0 0-.562-.563ZM16.25 10.313H14a.57.57 0 0 0-.563.562v6.563h3.376v-6.563a.57.57 0 0 0-.563-.563ZM1.063 7.875a1.125 1.125 0 1 0 2.25 0 1.125 1.125 0 0 0-2.25 0ZM6.125 1.688a1.125 1.125 0 1 0 2.25 0 1.125 1.125 0 0 0-2.25 0ZM15.688 4.5a1.125 1.125 0 1 0 2.25 0 1.125 1.125 0 0 0-2.25 0ZM2.9 7.005l3.638-4.447M15.732 4.185 8.33 2.003" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path d="M.5 0h18v18H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
