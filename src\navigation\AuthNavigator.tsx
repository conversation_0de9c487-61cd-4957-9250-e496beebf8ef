import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import Login from '../screens/Auth/Login';
import Signup from '../screens/Auth/Signup';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '../types/routes';

const Stack = createStackNavigator<AuthStackNavigatorParamList>();

const AuthNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name={AUTH_ROUTES.SIGN_IN}
        options={{
          animationEnabled: false,
        }}
        component={Login}
      />
      <Stack.Screen name={AUTH_ROUTES.SIGN_UP} component={Signup} />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
