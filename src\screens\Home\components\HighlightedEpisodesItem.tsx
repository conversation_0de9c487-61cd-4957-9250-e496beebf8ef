import { FC, memo, useCallback, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import {
  playVideo,
  setShowMiniPlayer,
} from '../../../redux/slices/miniPlayerSlice';
import { HighlightedEpisode } from '../../../types/podcasts';
import { formatViews } from '../../../utils/posts';
import { EPostType } from '../HomeFeed';

interface HighlightedEpisodesItemProps {
  data: HighlightedEpisode;
  selectedEpisodeId: number;
}

const HighlightedEpisodesItem: FC<HighlightedEpisodesItemProps> = ({
  data,
  selectedEpisodeId,
}) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const { isAdVisible } = useAppSelector(state => state.ads);

  const styles = useMemo(
    () => createStyles(colors, selectedEpisodeId === data.id),
    [colors, selectedEpisodeId, data.id],
  );

  const handleSelectEpisode = useCallback(() => {
    if (isAdVisible) return;
    if (data?.vidId && data?.list) {
      dispatch(
        playVideo({
          id: String(data?.id),
          title: data?.title,
          subtitle: 'Highlighted Episodes',
          videoId: data?.vidId,
          videoViews: String(data?.views),
          time: data?.time,
          list: data?.list,
          type: EPostType.HIGHLIGHTED_EPISODES,
        }),
      );
      dispatch(setShowMiniPlayer(true));

      return;
    }
  }, [data, isAdVisible]);

  return (
    <Pressable style={styles.container} onPress={handleSelectEpisode}>
      <View style={styles.imageContainer}>
        <FastImage source={{ uri: data.small }} style={styles.image} />
      </View>
      <View style={styles.content}>
        <CustomText numberOfLines={2} style={styles.title}>
          {data.title}
        </CustomText>
        <CustomText style={styles.subtitle}>Highlighted Episode</CustomText>
        <View style={styles.insightContainer}>
          <CustomText style={styles.timeAgo}> {data.time}</CustomText>
          <View style={styles.separator} />
          <CustomText style={styles.views}>
            {formatViews(Number(data.views))} Views
          </CustomText>
        </View>
      </View>
    </Pressable>
  );
};

export default memo(HighlightedEpisodesItem);

const createStyles = (colors: ColorScheme, isSelectedEpisode: boolean) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: isSelectedEpisode ? colors.base : colors.background,
      paddingHorizontal: moderateScale(14),
      paddingVertical: verticalScale(5),
    },
    imageContainer: {
      position: 'relative',
    },
    image: {
      width: moderateScale(130),
      height: verticalScale(62),
      borderRadius: moderateScale(6),
    },
    duration: {
      position: 'absolute',
      bottom: verticalScale(8),
      left: moderateScale(5),
      backgroundColor: 'rgba(0,0,0,0.7)',
      color: colors.white,
      fontSize: moderateScale(8),
      paddingVertical: moderateScale(1),
      paddingHorizontal: moderateScale(4),
      borderRadius: moderateScale(3),
      overflow: 'hidden',
    },
    content: {
      flex: 1,
      marginLeft: moderateScale(8),
      justifyContent: 'space-around',
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(13),
    },
    subtitle: {
      color: colors.foreground,
      fontSize: moderateScale(13),
      fontWeight: '500',
    },
    views: {
      color: colors.secondary,
      fontSize: moderateScale(11),
    },
    separator: {
      height: verticalScale(2),
      width: verticalScale(2),
      backgroundColor: colors.secondary,
      borderRadius: moderateScale(5),
      marginHorizontal: moderateScale(4),
    },
    timeAgo: {
      color: colors.secondary,
      fontSize: moderateScale(11),
    },
    insightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
  });
