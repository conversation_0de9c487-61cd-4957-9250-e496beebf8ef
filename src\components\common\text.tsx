import React, { FC, ReactNode } from 'react';
import {
  StyleProp,
  StyleSheet,
  Text,
  TextProps,
  TextStyle,
} from 'react-native';
import { decode } from 'html-entities';

interface CustomTextProps extends TextProps {
  style?: StyleProp<TextStyle>;
  children?: ReactNode;
}

const CustomText: FC<CustomTextProps> = ({ style, ...props }) => {
  const decodedText = decode(props.children as string);

  return (
    <Text style={[styles.defaultFontStyle, style]} {...props}>
      {decodedText}
    </Text>
  );
};

const styles = StyleSheet.create({
  defaultFontStyle: {
    fontFamily: 'Roboto-Regular',
  },
});

export default CustomText;
