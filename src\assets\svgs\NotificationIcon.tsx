import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';

export const NotificationIcon: React.FC<SvgProps> = props => {
  return (
    <Svg width={22} height={22} fill="none" viewBox="0 0 24 24" {...props}>
      <G strokeWidth={1.5}>
        <Path
          strokeLinecap="round"
          d="M14.873 19.443c0 .304-.078.604-.228.884-.151.28-.372.535-.65.75-.28.214-.61.384-.974.5a3.79 3.79 0 0 1-1.148.176 3.79 3.79 0 0 1-1.148-.175 3.176 3.176 0 0 1-.973-.501c-.278-.215-.5-.47-.65-.75a1.86 1.86 0 0 1-.228-.884"
        />
        <Path d="m19.615 13.19.264.57a3.673 3.673 0 0 1-2.665 5.158l-.16.03a28.504 28.504 0 0 1-10.362 0c-2.379-.44-3.637-3.072-2.486-5.2l.226-.418a7.819 7.819 0 0 0 .942-3.72V8.287a5.86 5.86 0 0 1 3.25-5.247 7.566 7.566 0 0 1 6.544-.094l.205.094a6.028 6.028 0 0 1 3.5 5.473v1.31a8 8 0 0 0 .742 3.366Z" />
      </G>
    </Svg>
  );
};
