import React, { FC, useEffect, useMemo, useState } from 'react';
import { Alert, Pressable, Share, StyleSheet, View } from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import {
  LikeIcon,
  PauseIcon,
  PlayIcon,
  SavedIcon,
  ShareIcon,
} from '../../../../assets/svgs';
import { HideEyedIcon } from '../../../../assets/svgs/HideEyedIcon';
import { ColorScheme } from '../../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../../hooks/redux';
import {
  hidePost,
  postAction,
  removeSavedPost,
  setPostsData,
} from '../../../../redux/slices/postsSlice';
import { Reaction as IReaction } from '../../../../types/posts';
import AlertModal from '../../../common/Modal';

interface PostActionsProps {
  id: string;
  playing: boolean;
  url: string;
  title?: string;
  is_post_saved: boolean;
  reactions: IReaction;
  togglePlaying: () => void;
  setLocalReaction: (reaction: IReaction) => void;
}

export const PostActions: FC<PostActionsProps> = ({
  id,
  playing,
  url,
  title,
  is_post_saved,
  reactions,
  togglePlaying,
  setLocalReaction,
}) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const [isLiked, setIsLiked] = useState<boolean>(false);
  const [isPostSaved, setIsPostSaved] = useState<boolean>(is_post_saved);
  const [isVisible, setIsVisible] = useState(false);
  const { posts } = useAppSelector(state => state.posts);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const hapticFeedbackOptions = {
    enableVibrateFallback: true,
    ignoreAndroidSystemSettings: false,
  };

  useEffect(() => {
    if (reactions?.is_reacted && reactions?.type === '1') {
      setIsLiked(true);
    }
  }, [reactions?.is_reacted, reactions?.type]);

  const onShare = async () => {
    try {
      await Share.share({
        title,
        url,
        message: url,
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        Alert.alert(error.message);
      } else {
        console.error('Unknown error:', error);
      }
    }
  };

  const handleReaction = async () => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticFeedbackOptions);

    setIsLiked(!isLiked);

    await dispatch(
      postAction({
        post_id: id,
        action: 'reaction',
        reaction: isLiked ? '0' : '1',
      }),
    );

    let updatedCount;
    if (!isLiked) {
      updatedCount = reactions['1'] ? Number(reactions['1']) + 1 : 1;
    } else {
      updatedCount = reactions['1']
        ? Math.max(Number(reactions['1']) - 1, 0)
        : 0;
    }

    let updatedTotalCount = reactions.count;
    if (!isLiked) {
      if (!reactions.is_reacted) {
        updatedTotalCount = reactions.count + 1;
      }
    } else {
      if (reactions.is_reacted) {
        updatedTotalCount = Math.max(reactions.count - 1, 0);
      }
    }

    const updatedReaction = {
      ...reactions,
      is_reacted: !isLiked,
      type: !isLiked ? '1' : '0',
      count: updatedTotalCount,
      '1': updatedCount,
    };

    setLocalReaction(updatedReaction);
  };

  const handleSavePost = () => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticFeedbackOptions);

    setIsPostSaved(!isPostSaved);
    dispatch(
      postAction({
        post_id: id,
        action: 'save',
      }),
    );
    if (isPostSaved) {
      dispatch(removeSavedPost(id));
    }
  };

  const hidePostHandler = async () => {
    await dispatch(hidePost(id));
    dispatch(setPostsData(posts.filter(post => post.id !== id)));
  };

  const handleHidePost = () => setIsVisible(true);

  const handlePlayAndPause = () => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticFeedbackOptions);
    togglePlaying();
  };

  return (
    <View style={styles.container}>
      <View style={styles.actionItemContainer}>
        <Pressable style={styles.actionItem} onPress={handleReaction}>
          <LikeIcon fill={colors.foreground} isLiked={isLiked} />
        </Pressable>
      </View>
      <View style={styles.actionItemContainer}>
        <Pressable style={styles.actionItem} onPress={onShare}>
          <ShareIcon fill={colors.foreground} />
        </Pressable>
      </View>
      <View style={styles.actionItemContainer}>
        <Pressable style={styles.actionItem} onPress={handleSavePost}>
          <SavedIcon fill={colors.foreground} isSaved={isPostSaved} />
        </Pressable>
      </View>
      <AlertModal
        title="Confirm Hide Post"
        message="Are you sure you want to hide this post? It will no longer be visible to you."
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        onConfirm={hidePostHandler}
        confirmText="Hide Post"
        cancelText="Cancel"
      />
      <View style={styles.actionItemContainer}>
        <Pressable style={styles.actionItem} onPress={handleHidePost}>
          <HideEyedIcon fill={colors.foreground} />
        </Pressable>
      </View>
      <View style={styles.actionItemContainer}>
        {playing ? (
          <Pressable style={styles.actionItem} onPress={handlePlayAndPause}>
            <PauseIcon fill={colors.foreground} />
          </Pressable>
        ) : (
          <Pressable style={styles.actionItem} onPress={handlePlayAndPause}>
            <PlayIcon fill={colors.foreground} />
          </Pressable>
        )}
      </View>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: verticalScale(2),
      gap: moderateScale(8),
    },
    actionItemContainer: {
      flex: 1,
    },
    actionItem: {
      gap: moderateScale(2),
      backgroundColor: colors.base,
      borderRadius: moderateScale(50),
      height: verticalScale(24),
      paddingHorizontal: moderateScale(6),
    },
    text: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontFamily: 'Antonio-Regular',
      textTransform: 'uppercase',
    },
    playText: {
      color: colors.secondary,
      fontSize: moderateScale(12),
      fontWeight: '600',
    },
  });
