export enum TOAST_TYPE {
  SUCCESS = 'success',
  ERROR = 'error',
}

export const TOAST_TITLE = {
  LOGIN: 'Login',
  SIGNUP: 'Signup',
  FORGOT_PASSWORD: 'Forgot Password',
  SET_PASSWORD: 'Set Password',
  OTP: 'OTP',
  PROFILE: 'Profile',
  SESSION: 'Session',
  FAILED_TO_POST: 'Failed to Post',
  POST_SUCCESSFULLY: 'Post Successfully',
  FAILED_TO_HIDE_POST: 'Failed to Hide Post',
  POLL_CREATED: 'Poll Created Successfully',
  FAILED_TO_CREATE_POLL: 'Failed to Create Poll',
  POLL_UPDATED: 'Poll Updated Successfully',
  FAILED_TO_UPDATE_POLL: 'Failed to Update Poll',
  POLL_DELETED: 'Poll Deleted Successfully',
  FAILED_TO_DELETE_POLL: 'Failed to Delete Poll',
  FAILED_TO_REPORT_POLL: 'Failed to Report Poll',
  VOTE_RECORDED: 'Your vote has been recorded',
  FAILED_TO_VOTE: 'Failed to record your vote',
  FAILED_TO_FETCH_POLLS: 'Failed to Fetch Polls',
  FAILED_TO_FETCH_POLL: 'Failed to Fetch Poll',
};
