import { CPN, REST_SUB_URL } from '../../constants/common/api';
import { Ad } from '../../types/ads';
import { BannerResponse } from '../../types/banner';

import { API } from './ApiInstance';

export const fetchAdThreshold = async (): Promise<number | null> => {
  try {
    const formData = new FormData();

    const response = await API.Post<unknown, { config: { add_gap: number } }>(
      `${CPN}${REST_SUB_URL.GET_SITE_SETTINGS}`,
      formData,
      true,
    );

    return response?.data?.config?.add_gap || null;
  } catch (err) {
    console.error('Failed to fetch ad threshold', (err as Error).message);

    return null;
  }
};

export const adAction = async (payload: {
  type: 'random' | 'view' | 'click';
  id?: number;
}): Promise<Ad | null> => {
  try {
    const response = await API.Post<unknown, Ad>(
      `${CPN}${REST_SUB_URL.ADS}`,
      payload,
    );

    return response.data;
  } catch (err) {
    console.error('Failed to fetch random ad', (err as Error).message);

    return null;
  }
};

export const fetchAdsBannersService = async (payload: {
  slider_id: number;
}) => {
  const response = await API.Post<unknown, BannerResponse>(
    `${CPN}${REST_SUB_URL.BANNERS}`,
    payload,
  );
  if (!response.status) {
    throw new Error(response.message || 'Failed to fetch banners');
  }

  return response.data?.banners || [];
};
