import React, { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const TabBarContentSkeleton: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const renderChannelSkeleton = () => (
    <View style={styles.cardContainer}>
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item style={styles.card}>
          <SkeletonPlaceholder.Item
            style={styles.mainImage}
            borderRadius={moderateScale(8)}
          />
          <SkeletonPlaceholder.Item style={styles.wrapper}>
            <SkeletonPlaceholder.Item
              width="100%"
              height={verticalScale(10)}
              borderRadius={moderateScale(4)}
              alignSelf="center"
            />
            <SkeletonPlaceholder.Item
              width="100%"
              height={verticalScale(20)}
              borderRadius={moderateScale(4)}
              alignSelf="center"
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        {renderChannelSkeleton()}
        {renderChannelSkeleton()}
      </View>
      <View style={styles.row}>
        {renderChannelSkeleton()}
        {renderChannelSkeleton()}
      </View>
      <View style={styles.row}>
        {renderChannelSkeleton()}
        {renderChannelSkeleton()}
      </View>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(6),
      marginTop: moderateScale(10),
      flex: 1,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    cardContainer: {
      flex: 1,
    },
    card: {
      backgroundColor: colors.background,
      borderRadius: moderateScale(8),
      paddingHorizontal: moderateScale(6),
      paddingVertical: moderateScale(10),
    },
    mainImage: {
      width: '100%',
      aspectRatio: 0.9,
      borderRadius: moderateScale(8),
    },
    wrapper: {
      paddingVertical: moderateScale(10),
      gap: moderateScale(10),
    },
  });
