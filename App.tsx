import React from 'react';
import { StatusBar } from 'react-native';
import { LogLevel, OneSignal } from 'react-native-onesignal';
import { MenuProvider } from 'react-native-popup-menu';
import Toast from 'react-native-toast-message';
import { Provider } from 'react-redux';

import AdModal from './src/components/AdModal';
import { ONE_SIGNAL_APP_ID } from './src/constants/common/api';
import { THEME_MODE } from './src/constants/theme/colors';
import { useAppSelector } from './src/hooks/redux';
import AppNavigationContainer from './src/navigation/NavigationContainer';
import { store } from './src/redux/store';

const App: React.FC = () => {
  const { theme, colors } = useAppSelector(state => state.theme);
  const backgroundStyle = {
    backgroundColor: colors.background,
  };

  // Remove this method to stop OneSignal Debugging
  OneSignal.Debug.setLogLevel(LogLevel.Verbose);

  // OneSignal Initialization
  OneSignal.initialize(ONE_SIGNAL_APP_ID!);

  // Method for listening for notification clicks
  OneSignal.Notifications.addEventListener('click', event => {
    console.log('OneSignal: notification clicked:', event);
  });

  return (
    <>
      <StatusBar
        barStyle={theme === THEME_MODE.dark ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <MenuProvider>
        <AppNavigationContainer />
        <AdModal />
      </MenuProvider>
      <Toast topOffset={60} />
    </>
  );
};

const MainApp: React.FC = () => (
  <Provider store={store}>
    <App />
  </Provider>
);

export default MainApp;
