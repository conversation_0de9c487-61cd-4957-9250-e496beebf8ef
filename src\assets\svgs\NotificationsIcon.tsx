import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const NotificationsIcon = (props: SvgProps) => (
  <Svg width={14} height={16} viewBox="0 0 14 16" fill="none" {...props}>
    <Path
      d="M9.25 11.75V12.5C9.25 13.7426 8.24264 14.75 7 14.75C5.75736 14.75 4.75 13.7426 4.75 12.5V11.75M9.25 11.75H4.75M9.25 11.75H11.9429C12.2298 11.75 12.3739 11.75 12.4901 11.7108C12.712 11.636 12.8856 11.4617 12.9604 11.2398C12.9998 11.1232 12.9998 10.9786 12.9998 10.6895C12.9998 10.5629 12.9996 10.4997 12.9897 10.4393C12.971 10.3253 12.9268 10.2173 12.8594 10.1234C12.8238 10.0738 12.7786 10.0286 12.6894 9.93937L12.3972 9.64722C12.303 9.55296 12.25 9.4251 12.25 9.2918V6.5C12.25 3.6005 9.8995 1.24999 7 1.25C4.10051 1.25001 1.75 3.60051 1.75 6.5V9.29182C1.75 9.42513 1.69693 9.55296 1.60267 9.64722L1.31055 9.93934C1.22107 10.0288 1.17628 10.0738 1.14062 10.1234C1.07324 10.2173 1.02861 10.3253 1.0099 10.4393C1 10.4997 1 10.5629 1 10.6895C1 10.9786 1 11.1232 1.03934 11.2398C1.11419 11.4617 1.2886 11.636 1.5105 11.7108C1.62667 11.75 1.77026 11.75 2.05717 11.75H4.75"
      stroke="#C41208"
      strokeWidth={1.25}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
