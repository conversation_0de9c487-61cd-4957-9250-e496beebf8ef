import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, SvgProps } from 'react-native-svg';

interface ProfileIconProps extends SvgProps {
  isFocused?: boolean;
}

export const ProfileIcon: React.FC<ProfileIconProps> = props => {
  return (
    <Svg id="Layer_1" x="0px" y="0px" viewBox="0 0 1059 800" {...props}>
      <G>
        <G>
          <Path d="M447.1,275.7c-63.3-50-62.9-137-12.6-187.1c49.8-49.6,128.6-47.9,176.4,0.6c50.5,51.3,48.7,137.5-12.8,186.1 c8.9,4.6,18.2,8.6,26.7,13.8c62.9,39,96.6,95.3,100,169.4c0.4,9.8,0.2,19.6,0.1,29.5c-0.1,12.9-8.2,21.8-19.7,21.7 c-11.5-0.2-19.1-9.1-19.5-22.1c-0.6-17.3-0.1-34.8-2.7-51.7c-5.8-37.1-23.5-68.3-51.5-93.3c-55.5-49.7-137-56-199.2-14.7 c-40.2,26.7-64.4,64.4-71.1,112.3c-2.2,16.1-1.6,32.5-2.1,48.8c-0.4,10.6-6.1,18-15.9,20.2c-10.9,2.4-22.3-5.4-23-16.6 c-3.6-54,6.1-104.7,39.3-149.1c22.2-29.7,50.3-52,84.5-66.5C444.8,276.7,445.7,276.3,447.1,275.7z M522.4,91.7 c-47,0-83.9,37.3-84.8,84.4c-0.8,45.3,38.2,85.1,84.5,85.3c46.7,0.1,85.8-40,85-85.3C606.3,129.1,569.4,91.7,522.4,91.7z" />
        </G>
        <G>
          <Path d="M316.7,746.2h-23.5c0-92.4,0-140.9,0.1-145.4H327c2.1,0,4.4,0.2,7.2,0.5c15.4,2.1,23.1,15.5,23.1,40.1v4.7 c0,9.3-1.1,17.7-3.4,25.3c-2.8,9.9-9.1,15.7-18.9,17.6c-3.4,0.5-6.7,0.8-10,0.8c-1.5,0-3-0.1-4.5-0.2h-3.8V746.2z M320.8,670 c1.5,0,3-0.1,4.8-0.3c3.2-0.3,5.4-2.3,6.5-6c1.1-3.6,1.6-9.4,1.6-17.4c0-8.5-0.4-14.8-1.2-18.9c-0.9-4.5-3-7.1-6.6-7.9 c-1.5-0.3-3.1-0.4-5.1-0.4l-4.2,0.1v50.6L320.8,670z" />
          <Path d="M436.2,746.2h-23.4c-0.5-7.2-0.7-23-0.7-47.3v-4.3c0-7.9-1.1-12.6-3.3-14.1c-1.4-1-3.1-1.6-5.2-1.8l-8.4-0.4v67.9h-23.6 c0-90.6,0.1-139,0.2-145.4h35.4c2.6,0,5.5,0.2,8.5,0.5c3,0.4,6.1,1.5,9,3.3c4.5,3,7.5,7.9,8.8,14.5c1.2,6.6,1.8,13.6,1.8,21.3 c0,5.4-0.6,10.8-1.8,16.2c-1.5,6-4.7,10.2-9.6,12.5c6.1,2.3,9.6,8.9,10.5,19.8c0.7,8.2,1.1,19.5,1.1,33.8c-0.1,6.1,0,11.4,0.2,16 C435.9,739.9,436,742.4,436.2,746.2z M402.4,659.8c2.1,0,3.9-0.3,5.5-1c1.9-1,3.1-3.5,3.7-7.7c0.4-3.8,0.6-7.5,0.6-11.2 c0-4.6-0.2-9-0.7-13.3c-0.5-3-1.4-4.9-2.8-5.8c-1.8-1.1-4.8-1.6-9-1.6l-4.3,0.1v40.4H402.4z" />
          <Path d="M487.8,747.8c-20.3,0-31.1-11.9-32.6-35.7c-0.2-3.8-0.3-7.7-0.3-11.4l0.1-5.6c0-30.7,0-48-0.1-51.7 c0-3.7,0.1-7.5,0.4-11.2c1.9-22.1,12.8-33.1,32.8-33.1c11,0.1,19,3.3,24.1,9.6c4.6,5.8,7.3,14.2,7.9,25.2 c0.1,3.6,0.2,7.3,0.2,10.8v6.1c0,30.1,0,46.9,0,50.4c0,4.6-0.2,9.3-0.5,14c-1.1,10.3-3.9,18.1-8.4,23.4 C505.9,744.8,498,747.8,487.8,747.8z M487.4,727.3c0.7,0,1.4-0.1,2.2-0.2c3.6-0.4,5.7-4,6.5-10.7c0.3-3.2,0.5-6.3,0.5-9.3v-69.9 c0-2.1-0.1-4.4-0.3-6.8c-0.5-5-1.8-8.1-4.1-9.4c-1.4-0.8-2.8-1.2-4.4-1.2c-1.8,0-3.4,0.5-4.9,1.6c-1.9,1.7-3.1,4.1-3.6,7.3 c-0.5,3.1-0.7,6-0.7,8.6c0,44,0,67.1-0.1,69.4c0,4.8,0.5,9.4,1.4,13.9C480.8,725,483.3,727.3,487.4,727.3z" />
          <Path d="M563.1,678.9v67.4h-23.6V607.6c0-0.7,0-1.3,0.1-2c0-0.1,0-0.2,0-0.3c0-0.4,0-0.8,0-1.2v-3.3h50.9v20.7h-27.4v36.6h25.4 v20.8H563.1z" />
          <Path d="M629.5,746.2H606c0-92.1,0-140.6,0.1-145.4h23.5C629.6,690.6,629.5,739,629.5,746.2z" />
          <Path d="M702.1,746.2h-51.2V605.6c0.1-0.5,0.1-2.1,0.1-4.8h23.4V726h27.7V746.2z" />
          <Path d="M738.5,681.6v44.1h27.3v20.5H715V607.6c0-0.7,0-1.3,0-2c0-0.1,0-0.2,0-0.3c0-0.4,0-0.8,0-1.2l0.1-3.3h49.7v20.6h-26.3 v40.2h25v20.1H738.5z" />
        </G>
        {props?.isFocused ? (
          <G>
            <Path d="M604.9,275.8c19.8,8.1,38.1,18.6,54.2,32.5c46.6,40,71.2,90.7,72.8,152.2c0.2,9.3,0.2,18.6,0.1,28 c-0.1,13.5-8.2,21.3-21.5,21.3c-120.2,0-240.4,0-360.6,0c-15.2,0-22.7-7.4-22.9-22.6c-0.2-24.1-0.5-48.3,5.8-71.9 c17.5-65,57-111.2,118.7-138.4c0.8-0.3,1.5-0.7,2.8-1.3c-34.6-28.4-51.7-64.5-48.5-109.1c2.5-34.8,18-63.8,44.7-86 c49.7-41.2,121.8-37.1,167.6,8.5C664.6,135.5,670.6,225,604.9,275.8z" />
          </G>
        ) : null}
      </G>
    </Svg>
  );
};
