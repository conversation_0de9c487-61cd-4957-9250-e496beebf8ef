export interface PlaylistVideos {
  id: number;
  video_id: string;
  video_title: string;
  thumbnail_url: string;
  playlist_id: string;
  created_at: string;
  duration: number;
  thumb: number;
  updated_at: string;
  i: number;
  views?: string;
}

export interface Playlist {
  playlist: {
    id: number;
    playlist_id: string;
    playlist_title: string;
    playlist_description: string;
    playlist_thumbnail: string;
    created_at: string;
    video_count: number;
    total_duration: number;
    update_at: number;
    nextPageToken: string;
  };
  channel: {
    category_id: number;
    id: number;
    name: string;
    icon: string;
    url: string;
    description: string;
    like_count: number;
    is_liked: number;
  };
  videos: PlaylistVideos[];
}

export interface RecentSearch {
  id: number;
  text: string;
  time: string;
}
