import { Userdata } from './user';

export interface PollOption {
  id: string;
  text: string;
  votes: number;
  percentage: number;
  isVoted: boolean;
}

export interface Poll {
  id: string;
  question: string;
  options: PollOption[];
  creator: {
    user_id: string;
    username: string;
    first_name: string;
    last_name: string;
    avatar: string;
    verified: boolean;
  };
  totalVotes: number;
  duration: number; // in days
  createdAt: string;
  expiresAt: string;
  isCompleted: boolean;
  userVotedOptionId?: string;
  isVoted: boolean;
  pollActive: boolean; // true = closed/show results, false = active/can vote
}

export interface CreatePollRequest {
  question: string;
  options: string[];
  duration: number; // in days
  creator: Userdata;
}

export interface VoteRequest {
  pollId: string;
  optionId: string;
}

export interface PollsResponse {
  polls: Poll[];
  hasMore: boolean;
  nextPage: number | null;
}

export interface CreatePollResponse {
  poll: Poll;
}

export interface VoteResponse {
  poll: Poll;
}

export type PollDuration = 1 | 3 | 7; // 1 day, 3 days, 1 week

export interface PollFormData {
  question: string;
  options: string[];
  duration: PollDuration;
} 