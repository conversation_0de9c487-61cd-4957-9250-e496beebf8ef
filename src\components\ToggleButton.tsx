import React from 'react';
import { StyleProp, StyleSheet, Switch, ViewStyle } from 'react-native';

import { useAppSelector } from '../hooks/redux';

interface Props extends React.ComponentProps<typeof Switch> {
  style?: StyleProp<ViewStyle>;
  isEnabled?: boolean;
  toggleSwitch?: () => void;
}

const ToggleButton: React.FC<Props> = ({ isEnabled, style, toggleSwitch }) => {
  const { colors } = useAppSelector(state => state.theme);

  return (
    <Switch
      style={[styles.switch, style]}
      trackColor={{ false: colors.accent, true: colors.accent }}
      ios_backgroundColor={colors.base}
      thumbColor={colors.white}
      onValueChange={toggleSwitch}
      value={isEnabled}
    />
  );
};

const styles = StyleSheet.create({
  switch: {
    transform: [{ scale: 0.8 }],
  },
});

export default ToggleButton;
