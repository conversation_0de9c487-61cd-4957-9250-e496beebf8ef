export type LoginRequest = {
  username: string;
  password: string;
};

export type LoginResponse = {
  access_token: string;
  api_status: number;
  membership: boolean;
  timezone: string;
  user_id: string;
};

export type SignupRequest = {
  username: string;
  email: string;
  birthday: string;
  password: string;
  confirm_password: string;
};

export type SignupResponse = {
  message: string;
};

export type ForgotPasswordRequest = {
  email: string;
  type: string;
  otp?: string;
};

export type ForgotPasswordResponse = {
  message: string;
  api_status: number;
};

export type SetPasswordRequest = {
  email: string;
  otp: string;
  password: string;
};

export type SetPasswordResponse = {
  message: string;
  api_status: number;
};

interface Category {
  id: number;
  channel: number;
  title: string;
}

export interface FetchChannelRequest {
  id?: string;
  type?: string;
}

export interface Channel {
  category_id: number;
  id: number;
  name: string;
  icon: string;
  url: string;
  youtube_url: string;
  image: string;
  description: string;
  like_count: number;
  is_liked: number;
}

export type ChannelsResponse = {
  api_status: number;
  channels: Channel[];
  categories: Category[];
};

export type LikeChannelRequest = {
  channel_id: number;
  type: 'like' | 'dislike';
};

export type LikeChannelResponse = {
  api_status: number;
  channel_id: number;
  now_liked: boolean;
};
