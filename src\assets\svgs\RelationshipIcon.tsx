import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const RelationshipIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path
      fill={props.stroke}
      stroke={props.stroke}
      strokeWidth={0.25}
      d="M7.387 13.38a.28.28 0 0 0 .157-.155c-3.331-1.811-4.609-4.92-4.064-7.023.101-.39.261-.73.473-1.011a3.927 3.927 0 0 0-.075-.02 2.171 2.171 0 0 1-.32-.071c-.479-.086-.851-.048-1.126.095-.321.167-.55.498-.683 1.048-.673 2.746-.697 5.33-.076 7.684A54.338 54.338 0 0 0 2.92 19.22l-.121.035.12-.***************.598a.719.719 0 0 0 .681.41l3.608-6.887Zm0 0c-.108.046-.264.06-.459.033m.46-.033-.46.033M8.28 5.966a.296.296 0 0 1-.332-.005l-2.05-.808c.47.024 1.113.31 1.979.911.141.098.328.1.472.006m-.069-.104.069.104m-.069-.104c.874-.57 1.543-.856 2.045-.873L8.03 12.518c1.813-.897 2.774-2.088 3.262-2.938.61-1.063.83-2.18.622-3.065-.102-.436-.3-.766-.566-.983-.266-.216-.61-.328-1.02-.314-.465.016-1.11.284-1.98.852m-.069-.104.07.104h-.001m-3.23 7.534c.024 1.018.07 3 .138 6.283a.428.428 0 0 1-.02.14m-.119-6.423-2.694 6.522-.115.05a1.574 1.574 0 0 0 1.513.947h.003a1.577 1.577 0 0 0 1.412-1.097m-.119-6.421c.371.226.791.428 1.206.55.489.146.99.189 1.398.014a1.16 1.16 0 0 0 .279-.17c.09.075.184.128.272.166m-3.155-.56 3.155.56m-3.036 5.861-.12-.038m.12.038-.12-.038m0 0a.303.303 0 0 0 .015-.099l-.014.1Zm3.156-5.823.05-.114-.05.114Zm0 0c.408.178.91.137 1.4-.008a5.237 5.237 0 0 0 1.21-.553c-.025 1.016-.071 2.998-.14 6.281l.126.003c-.001.034.004.068.014.1l-.119.037a1.574 1.574 0 0 0 1.413 1.097h.002l.076.002c.618 0 1.188-.374 1.437-.95m-5.42-6.009 5.42 6.01m0 0a.504.504 0 0 0 .019-.053l.001-.004.175-.626.01-.036.002-.008c.339-1.204.762-2.707 1.252-5.311.657-2.498.63-5.223-.074-8.098-.184-.76-.554-1.308-1.113-1.6a1.964 1.964 0 0 0-.4-.158c.249-.353.395-.783.395-1.247A2.15 2.15 0 0 0 11.822.875a2.15 2.15 0 0 0-2.138 2.159c0 .505.174.97.464 1.34m3.544 15.8-3.544-15.8m0 0c-.57.057-1.224.329-2.024.827l2.024-.828Zm-3.22 9.04a2.831 2.831 0 0 1-.644-.18m.644.18-.644-.18m0 0a4.869 4.869 0 0 1-1.305-.777m1.305.777-1.305-.777m0 0c-.45-.589-1.152-1.592-2.091-2.987l2.09 2.987Zm9.346 1.475v.002a53.942 53.942 0 0 1-1.246 5.287l-.01.037c-.059.205-.114.403-.168.598a.721.721 0 0 1-.681.411.723.723 0 0 1-.625-.444c.09-4.36.142-6.398.155-6.918.463-.612 1.156-1.605 2.064-2.954a.43.43 0 0 0-.112-.595.423.423 0 0 0-.591.114c-.94 1.395-1.642 2.399-2.09 2.987-.368.312-.852.599-1.31.779-.23.09-.45.153-.643.178-.195.026-.35.012-.458-.035a.276.276 0 0 1-.14-.125c1.957-.984 3.012-2.295 3.558-3.244.71-1.234.976-2.58.713-3.693a2.754 2.754 0 0 0-.557-1.157c.088-.015.175-.035.261-.062.48-.085.852-.046 1.125.098.32.168.548.498.68 1.046v.001c.675 2.746.697 5.333.075 7.689Zm-9.92 5.891a.723.723 0 0 1-.625.444L2.888 9.47a.423.423 0 0 0-.591-.114.431.431 0 0 0-.113.594c.908 1.35 1.602 2.342 2.064 2.955.014.52.066 2.557.156 6.918Zm-2.29-.327.174.621L5.934 4.424l.007-.125-.033-.001c.255-.356.406-.792.406-1.263A2.15 2.15 0 0 0 4.177.875c-1.18 0-2.137.97-2.137 2.16 0 .464.146.894.394 1.247-.138.038-.268.09-.392.154-.562.293-.934.842-1.119 1.604-.704 2.875-.73 5.6-.074 8.098.492 2.61.915 4.112 1.255 5.318l.011.04ZM4.177 1.73c.709 0 1.288.583 1.288 1.303a1.297 1.297 0 0 1-1.417 1.295 4.476 4.476 0 0 0-.291-.065 1.304 1.304 0 0 1-.867-1.23c0-.72.579-1.303 1.287-1.303Zm7.645 0c.709 0 1.288.583 1.288 1.303 0 .57-.364 1.054-.868 1.23-.094.018-.19.04-.288.065a1.296 1.296 0 0 1-1.419-1.295c0-.72.58-1.303 1.287-1.303Z"
    />
  </Svg>
);
