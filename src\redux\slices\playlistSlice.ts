import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { COMMON_ERROR } from '../../constants/common';
import { fetchPlaylistService } from '../../services/api/playlistService';
import { Playlist } from '../../types/playlist';

interface PlaylistState {
  playlist: Playlist | null;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: PlaylistState = {
  playlist: {
    playlist: {
      id: 0,
      playlist_id: '',
      playlist_title: '',
      playlist_description: '',
      playlist_thumbnail: '',
      created_at: '',
      video_count: 0,
      total_duration: 0,
      update_at: 0,
      nextPageToken: '',
    },
    channel: {
      category_id: 0,
      id: 0,
      name: '',
      icon: '',
      url: '',
      description: '',
      like_count: 0,
      is_liked: 0,
    },
    videos: [],
  },
  status: 'idle',
  error: null,
};

export const fetchPlaylist = createAsyncThunk<
  Playlist | null,
  { id: string; channel_id?: string }
>('posts/fetchPlaylist', async (payload, { rejectWithValue }) => {
  try {
    const playlist = await fetchPlaylistService(payload);

    return playlist;
  } catch (error) {
    return rejectWithValue('Failed to fetch posts');
  }
});

export const playlistSlice = createSlice({
  name: 'playlist',
  initialState,
  reducers: {
    resetState: () => {
      return initialState;
    },
  },
  extraReducers: builder => {
    builder
      // Handle fetchPlaylist thunk
      .addCase(fetchPlaylist.pending, state => {
        state.status = 'loading';
      })
      .addCase(
        fetchPlaylist.fulfilled,
        (state, action: PayloadAction<Playlist | null>) => {
          state.playlist = action.payload;
          state.status = 'succeeded';
        },
      )
      .addCase(fetchPlaylist.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      });
  },
});

export const { resetState } = playlistSlice.actions;
