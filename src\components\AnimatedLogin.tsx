import React from 'react';
import { StyleSheet, View } from 'react-native';
import { verticalScale } from 'react-native-size-matters';
import LottieView from 'lottie-react-native';

import { THEME_MODE } from '../constants/theme/colors';
import { useAppSelector } from '../hooks/redux';

const AnimatedLogin = () => {
  const { theme } = useAppSelector(state => state.theme);

  const src =
    theme === THEME_MODE.dark
      ? require('./../assets/animations/LoginAnimationDark.json')
      : require('./../assets/animations/LoginAnimationLight.json');

  return (
    <View style={styles.container}>
      <LottieView
        source={src}
        autoPlay
        loop
        speed={1}
        style={styles.lottieView}
      />
    </View>
  );
};

export default AnimatedLogin;

const styles = StyleSheet.create({
  container: {
    height: verticalScale(270),
  },
  lottieView: {
    flex: 1,
    transform: [{ scale: 1.8 }],
  },
});
