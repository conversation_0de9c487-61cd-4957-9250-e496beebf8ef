import Toast from 'react-native-toast-message';

import { CPN, REST_SUB_URL } from '../constants/common/api';
import { TOAST_TITLE, TOAST_TYPE } from '../constants/toast';

import { API } from './api/ApiInstance';
import {
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  LoginRequest,
  LoginResponse,
  SetPasswordRequest,
  SetPasswordResponse,
  SignupRequest,
} from './types';

export const loginUser = async (payload: LoginRequest) => {
  const response = await API.Post<LoginRequest, LoginResponse>(
    `${CPN}${REST_SUB_URL.LOGIN}`,
    payload,
  );
  if (response.data?.api_status === 200) return response.data;

  Toast.show({
    type: TOAST_TYPE.ERROR,
    text1: TOAST_TITLE.LOGIN,
    text2: response.message,
  });

  return;
};

export const signupUser = async (payload: SignupRequest) => {
  const response = await API.Post<
    SignupRequest,
    Omit<LoginResponse, 'timezone'>
  >(`${CPN}${REST_SUB_URL.SIGNUP}`, payload);
  if (response.data?.api_status === 200) return response.data;

  Toast.show({
    type: TOAST_TYPE.ERROR,
    text1: TOAST_TITLE.SIGNUP,
    text2: response.message,
  });

  return;
};

export const forgotPassword = async (payload: ForgotPasswordRequest) => {
  const response = await API.Post<
    ForgotPasswordRequest,
    ForgotPasswordResponse
  >(`${CPN}${REST_SUB_URL.OTP}`, payload);
  if (response.data?.api_status === 200) return response.data;

  Toast.show({
    type: TOAST_TYPE.ERROR,
    text1: TOAST_TITLE.FORGOT_PASSWORD,
    text2: response.message,
  });

  return;
};

export const setNewPassword = async (payload: SetPasswordRequest) => {
  const response = await API.Post<SetPasswordRequest, SetPasswordResponse>(
    `${CPN}${REST_SUB_URL.SET_PASSWORD}`,
    payload,
  );
  if (response?.data?.api_status === 200) return response.data;

  Toast.show({
    type: TOAST_TYPE.ERROR,
    text1: TOAST_TITLE.SET_PASSWORD,
    text2: response.message,
  });

  return;
};
