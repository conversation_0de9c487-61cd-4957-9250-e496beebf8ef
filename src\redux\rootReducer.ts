import { combineReducers } from '@reduxjs/toolkit';

import { adSlice } from './slices/adSlice';
import { authSlice } from './slices/authSlice';
import { bannerSlice } from './slices/bannerSlice';
import { channelsSlice } from './slices/channelSlice';
import { miniPlayerSlice } from './slices/miniPlayerSlice';
import { notificationsSlice } from './slices/notificationsSlice';
import { playlistSlice } from './slices/playlistSlice';
import { pollsSlice } from './slices/pollsSlice';
import { postsSlice } from './slices/postsSlice';
import { searchSlice } from './slices/searchSlice';
import { themeSlice } from './slices/themeSlice';
import { toastSlice } from './slices/toastSlice';
import { userSlice } from './slices/userSlice';

const rootReducer = combineReducers({
  [authSlice.name]: authSlice.reducer,
  [themeSlice.name]: themeSlice.reducer,
  [postsSlice.name]: postsSlice.reducer,
  [channelsSlice.name]: channelsSlice.reducer,
  [playlistSlice.name]: playlistSlice.reducer,
  [miniPlayerSlice.name]: miniPlayerSlice.reducer,
  [pollsSlice.name]: pollsSlice.reducer,
  [searchSlice.name]: searchSlice.reducer,
  [toastSlice.name]: toastSlice.reducer,
  [userSlice.name]: userSlice.reducer,
  [adSlice.name]: adSlice.reducer,
  [notificationsSlice.name]: notificationsSlice.reducer,
  [bannerSlice.name]: bannerSlice.reducer,
});

export { rootReducer };
