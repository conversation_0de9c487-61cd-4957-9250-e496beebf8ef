import React, { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const HomeFeedSkeleton: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item
          marginHorizontal={moderateScale(8)}
          marginBottom={verticalScale(20)}>
          <SkeletonPlaceholder.Item
            height={verticalScale(160)}
            borderRadius={moderateScale(10)}
          />
          <SkeletonPlaceholder.Item
            marginTop={verticalScale(8)}
            gap={moderateScale(6)}>
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              width={'95%'}
              height={verticalScale(10)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              width={'85%'}
              height={verticalScale(10)}
            />
          </SkeletonPlaceholder.Item>
          <View style={styles.subContainer}>
            <SkeletonPlaceholder.Item
              width={moderateScale(40)}
              height={moderateScale(40)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item gap={moderateScale(6)}>
              <SkeletonPlaceholder.Item
                borderRadius={moderateScale(4)}
                width={moderateScale(110)}
                height={verticalScale(10)}
              />
              <SkeletonPlaceholder.Item
                borderRadius={moderateScale(4)}
                width={moderateScale(220)}
                height={verticalScale(8)}
              />
            </SkeletonPlaceholder.Item>
          </View>
          <View style={styles.actionContainer}>
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1.4}
              height={verticalScale(20)}
            />
          </View>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item
          marginHorizontal={moderateScale(8)}
          marginBottom={verticalScale(20)}>
          <SkeletonPlaceholder.Item
            height={verticalScale(160)}
            borderRadius={moderateScale(10)}
          />
          <SkeletonPlaceholder.Item
            marginTop={verticalScale(8)}
            gap={moderateScale(6)}>
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              width={'95%'}
              height={verticalScale(10)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(4)}
              width={'85%'}
              height={verticalScale(10)}
            />
          </SkeletonPlaceholder.Item>
          <View style={styles.subContainer}>
            <SkeletonPlaceholder.Item
              width={moderateScale(40)}
              height={moderateScale(40)}
              borderRadius={moderateScale(50)}
            />
            <SkeletonPlaceholder.Item gap={moderateScale(6)}>
              <SkeletonPlaceholder.Item
                borderRadius={moderateScale(4)}
                width={moderateScale(110)}
                height={verticalScale(10)}
              />
              <SkeletonPlaceholder.Item
                borderRadius={moderateScale(4)}
                width={moderateScale(220)}
                height={verticalScale(8)}
              />
            </SkeletonPlaceholder.Item>
          </View>
          <View style={styles.actionContainer}>
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1}
              height={verticalScale(20)}
            />
            <SkeletonPlaceholder.Item
              borderRadius={moderateScale(50)}
              flex={1.4}
              height={verticalScale(20)}
            />
          </View>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingTop: verticalScale(10),
    },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: moderateScale(8),
      gap: moderateScale(10),
    },
    actionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: verticalScale(8),
      gap: moderateScale(6),
    },
  });
