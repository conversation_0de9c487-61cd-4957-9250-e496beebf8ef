{"name": "cpnent", "version": "0.0.1", "author": {"name": "<PERSON>", "github": "https://github.com/Riyaancode"}, "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "prepare": "husky"}, "dependencies": {"@hookform/error-message": "^2.0.1", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "html-entities": "^2.5.2", "lottie-react-native": "^7.1.0", "react": "18.3.1", "react-hook-form": "^7.53.1", "react-native": "0.75.4", "react-native-bootsplash": "^6.3.3", "react-native-date-picker": "^5.0.10", "react-native-device-info": "^14.0.4", "react-native-draggable-flatlist": "^4.0.1", "react-native-element-dropdown": "^2.12.2", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.20.0", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^7.1.2", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-onesignal": "^5.2.8", "react-native-pager-view": "^6.4.1", "react-native-popup-menu": "^0.16.1", "react-native-reanimated": "^3.16.1", "react-native-safe-area-context": "^4.11.1", "react-native-screens": "^3.34.0", "react-native-size-matters": "^0.4.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "^15.8.0", "react-native-swipe-list-view": "^3.2.9", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.13.0", "react-native-webview": "^13.12.3", "react-native-youtube-iframe": "^2.3.0", "react-redux": "^9.1.2", "redux": "^5.0.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@hookform/resolvers": "^3.9.0", "@react-native-community/eslint-config": "^3.2.0", "@react-native/babel-preset": "0.75.4", "@react-native/eslint-config": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.6", "jest": "^29.6.3", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "react-native-dotenv": "^3.4.11", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4", "lint-staged": {"*/**/*.{js,jsx,ts,tsx}": ["eslint --fix"], "*/**/*.{json,css,md}": ["prettier --write"]}}