import { FC, MutableRefObject, useMemo } from 'react';
import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import WebView, { WebViewMessageEvent } from 'react-native-webview';
import { PLAYER_STATES } from 'react-native-youtube-iframe';

import { ColorScheme } from '../../../../constants/theme/colors';
import { useAppSelector } from '../../../../hooks/redux';
import {
  getYoutubeURL,
  injectedCSS,
  injectedJavaScript,
} from '../../../../utils/posts';

interface PostContentProps {
  title?: string;
  code: string;
  playing: boolean;
  videoPlayingStatus: PLAYER_STATES;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  webviewRef: MutableRefObject<any>;
  togglePlaying: (state: PLAYER_STATES) => void;
  handleSelectVideo: () => void;
}

const { width } = Dimensions.get('screen');

export const PostContent: FC<PostContentProps> = ({
  title,
  code,
  playing,
  webviewRef,
  videoPlayingStatus,
  togglePlaying,
  handleSelectVideo,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const { isAdVisible, resumeVideo } = useAppSelector(state => state.ads);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const thumbnail = `https://img.youtube.com/vi/${code}/maxresdefault.jpg`;
  const youtubeURL = getYoutubeURL(
    code,
    (playing && !isAdVisible) || resumeVideo,
  );

  const handleOnMessage = (event: WebViewMessageEvent) => {
    const message = event.nativeEvent.data;
    if (message === 'playing') {
      togglePlaying(PLAYER_STATES.PLAYING);
    } else if (message === 'paused') {
      togglePlaying(PLAYER_STATES.PAUSED);
    } else if (message === 'ended') {
      togglePlaying(PLAYER_STATES.ENDED);
    }
  };

  return (
    <View style={styles.container}>
      <View>
        {videoPlayingStatus === PLAYER_STATES.UNSTARTED && !playing ? (
          <View style={styles.thumbnailCover}>
            <Pressable onPress={() => togglePlaying(PLAYER_STATES.PLAYING)}>
              <FastImage
                resizeMode="cover"
                source={{ uri: thumbnail }}
                style={styles.thumbnail}
              />
            </Pressable>
          </View>
        ) : (
          <View style={styles.bgCover}>
            <WebView
              ref={webviewRef}
              style={styles.webView}
              source={{ uri: youtubeURL }}
              javaScriptEnabled={true}
              injectedJavaScript={injectedJavaScript}
              injectedCSS={injectedCSS}
              onMessage={handleOnMessage}
              mediaPlaybackRequiresUserAction={false}
              allowsFullscreenVideo={true}
            />
          </View>
        )}
      </View>
      <Pressable onPress={handleSelectVideo}>
        <Text numberOfLines={2} style={styles.title}>
          {title}
        </Text>
      </Pressable>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      gap: verticalScale(4),
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      lineHeight: 24,
      fontFamily: 'Antonio-Bold',
    },
    bgCover: {
      width: '100%',
      height: (width / 16) * 8.5,
      borderRadius: moderateScale(10),
      overflow: 'hidden',
    },
    thumbnailCover: {
      width: '100%',
      height: (width / 16) * 8.5,
      borderRadius: moderateScale(10),
      overflow: 'hidden',
    },
    expandButton: {
      position: 'absolute',
      bottom: 10,
      right: 10,
      zIndex: 2,
    },
    webView: { borderRadius: moderateScale(10) },
    thumbnail: { width: '100%', height: '100%' },
  });
