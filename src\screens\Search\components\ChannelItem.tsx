import { FC, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';

import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import {
  playVideo,
  setShowMiniPlayer,
} from '../../../redux/slices/miniPlayerSlice';
import { Channel } from '../../../services/types';
import { EPostType } from '../../Home/HomeFeed';

export const ChannelItem: FC<Channel> = ({ id, name, icon, url }) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const navigation = useTypeSafeNavigation();

  const handleNavigate = () => {
    const playlistId = url?.split('/').pop();
    if (playlistId) {
      navigation.goBack();
      dispatch(
        playVideo({
          playlistId: playlistId,
          channelId: String(id),
          type: EPostType.PODCAST_CAROUSEL,
        }),
      );
      dispatch(setShowMiniPlayer(true));
    }
  };

  return (
    <Pressable onPress={handleNavigate} style={styles.container}>
      <View style={styles.contentContainer}>
        <FastImage
          source={{
            uri: icon,
          }}
          style={styles.thumbnail}
        />
        <View style={styles.content}>
          <CustomText style={styles.title} numberOfLines={1}>
            {name}
          </CustomText>
        </View>
      </View>
      <Icon name="arrow-up-left" size={26} color={colors.secondary} />
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: moderateScale(16),
      paddingVertical: verticalScale(10),
      backgroundColor: colors.background,
      width: '100%',
      gap: moderateScale(10),
    },
    contentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(14),
      width: '100%',
      flex: 1,
    },
    thumbnail: {
      width: moderateScale(45),
      height: moderateScale(45),
      borderRadius: moderateScale(5),
    },
    title: {
      fontSize: moderateScale(16),
      color: colors.foreground,
    },
    content: {
      flex: 1,
    },
  });
