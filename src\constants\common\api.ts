export const BASE_URL = process.env.REACT_APP_SERVER_URL;
export const SERVER_KEY = process.env.REACT_APP_SERVER_KEY;
export const NO_EMBED_API_URL = process.env.REACT_APP_NO_EMBED_API_URL;
export const ONE_SIGNAL_APP_ID = process.env.REACT_APP_ONE_SIGNAL_APP_ID;
export const CPN = '/cpn';

const AUTH_SUB_URL = {
  LOGIN: '/auth',
  SIGNUP: '/create-account',
  OTP: '/otp',
  SET_PASSWORD: '/set_password',
};

const POST_SUB_URL = {
  CREATE_POST: '/new_post',
  GET_ALL_POSTS: '/posts',
  GET_POST_DATA: '/get-post-data',
  POST_ACTION: '/post-actions',
  HIDE_POST: '/hide_post',
  CHANNEL: '/channel',
  LIKE_CHANNEL: '/likeChannel',
  PLAYLIST: '/playlist1',
  SEARCH: '/search',
  RECENT_SEARCHES: '/recent_search',
  REMOVE_RECENT_SEARCH: '/rmhistory',
  GET_USERS_COUNT: '/users',
  GET_USER_DATA: '/get-user-data',
  UPDATE_USER_DATA: '/update-user-data',
  PODCASTS: '/podcasts',
  GET_ALL_SESSIONS: '/sessions',
  SESSIONS: '/sessions',
  DELETE_ACCOUNT: '/delete_account',
  GET_USER_FEATURED_CARDS: '/select_cards',
  ADD_USER_FEATURED_CARDS: '/add_card',
  UPDATE_USER_FEATURED_CARDS: '/update_card',
  DELETE_USER_FEATURED_CARDS: '/delete_card',
  HIGHLIGHTED_EPISODES: '/sliders',
  GET_NOTIFICATIONS: '/notifications',
  GET_SITE_SETTINGS: '/get-site-settings',
  ADS: '/ads',
  BANNERS: '/banners',
  GET_VERIFIED_USERS: '/verified_users',
  VOTE_UP: '/vote_up',
  GET_POLLS: '/polls',
};

export const REST_SUB_URL = {
  ...POST_SUB_URL,
  ...AUTH_SUB_URL,
};
