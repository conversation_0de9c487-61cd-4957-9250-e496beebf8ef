import React, { <PERSON> } from 'react';
import { StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import {
  APP_ROUTES,
  AppStackNavigatorParamList,
  STACKS,
} from '../../types/routes';

type WebViewScreenProps = NativeStackScreenProps<
  AppStackNavigatorParamList,
  APP_ROUTES.WEB_VIEW,
  STACKS.APP
>;

const WebViewScreen: FC<WebViewScreenProps> = ({ route }) => {
  const { url } = route.params;

  return (
    <View style={styles.container}>
      <WebView source={{ uri: url }} />
    </View>
  );
};
export default WebViewScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
