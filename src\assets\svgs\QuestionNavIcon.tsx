import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const QuestionNavIcon: React.FC<SvgProps> = props => {
  return (
    <Svg 
      width={22} 
      height={22} 
      viewBox="0 0 24 24" 
      fill="none" 
      {...props}
    >
      <Path
        d="M12 19V18.99M8.12598 8C8.57002 6.27477 10.1361 5 12 5C12.7285 5 13.4116 5.1948 14 5.53515M12 16C12 11.5 16 12.5 16 9C16 8.6547 15.9562 8.31962 15.874 8"
        stroke={props.stroke || '#000000'}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}; 