import React, { FC, useMemo } from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/FontAwesome6';

import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';

export interface UpdateProfileFooterProps {
  toggleModal: () => void;
}

export const UpdateProfileFooter: FC<UpdateProfileFooterProps> = ({
  toggleModal,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <Pressable style={styles.bottomSection} onPress={toggleModal}>
      <Icon name="plus" size={moderateScale(16)} color={colors.accent} />
      <CustomText style={styles.bottomText}>Add Social Icons</CustomText>
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    bottomSection: {
      width: '100%',
      height: verticalScale(50),
      backgroundColor: colors.background,
      flexDirection: 'row',
      gap: moderateScale(8),
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      bottom: 0,
    },
    bottomText: {
      color: colors.accent,
      fontSize: moderateScale(16),
    },
  });
