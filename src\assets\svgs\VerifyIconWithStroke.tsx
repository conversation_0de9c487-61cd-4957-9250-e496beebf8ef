import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const VerifyIconWithStroke = (props: SvgProps) => (
  <Svg width={22} height={22} fill="none" {...props}>
    <Path fill="#fff" d="M6.9 7.567h8.182v6.545H6.9z" />
    <Path
      fill="#3897F0"
      stroke="#fff"
      d="m14.217 1.754 1.446 2.55 2.732.637.424.099-.039.434-.268 2.963 1.872 2.243.267.32-.267.32-1.872 2.242.268 2.964.04.434-.425.099-2.732.635-1.446 2.552-.224.396-.415-.186L11 19.305l-2.578 1.151-.415.186-.224-.396-1.447-2.552-2.732-.635-.423-.099.039-.434.267-2.964-1.87-2.242-.268-.32.267-.32 1.871-2.242-.267-2.964-.04-.434.424-.099 2.732-.636 1.447-2.551.224-.396.415.186L11 2.694l2.578-1.15.415-.186.224.396Zm-3.714 10.191-.357.374-.363-.37-1.397-1.43-.46.48 2.215 2.318 3.936-4.124-.466-.505-3.108 3.257Z"
    />
  </Svg>
);
