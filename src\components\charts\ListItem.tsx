import { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import {
  playVideo,
  setShowMiniPlayer,
} from '../../redux/slices/miniPlayerSlice';
import { EPostType } from '../../screens/Home/HomeFeed';
import { Channel } from '../../services/types';
import CustomButton from '../common/button';
import CustomText from '../common/text';

interface ListItemProps extends Channel {
  index: number;
}

export const ListItem: FC<ListItemProps> = ({
  id,
  url,
  index,
  name,
  description,
  image,
}) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const handleSelectPlaylist = () => {
    const playlistId = url.split('/').pop();

    dispatch(
      playVideo({
        playlistId: playlistId,
        channelId: String(id),
        type: EPostType.PODCAST_CAROUSEL,
      }),
    );
    dispatch(setShowMiniPlayer(true));
  };

  return (
    <View style={styles.container}>
      <View style={styles.avatarWrapper}>
        <FastImage source={{ uri: image }} style={styles.avatar} />
        <View>
          <CustomText style={styles.numberSeries}>{index + 1}</CustomText>
        </View>
      </View>
      <View style={styles.contentWrapper}>
        <CustomText numberOfLines={1} style={styles.title}>
          {name}
        </CustomText>
        <CustomText numberOfLines={3} style={styles.description}>
          {description}
        </CustomText>
      </View>
      <CustomButton
        onPress={handleSelectPlaylist}
        textSyle={styles.buttonText}
        style={styles.button}>
        Watch
      </CustomButton>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: moderateScale(10),
      paddingVertical: verticalScale(10),
      paddingHorizontal: moderateScale(20),
      height: verticalScale(70),
      backgroundColor: colors.background,
    },
    avatarWrapper: {
      flexDirection: 'row',
      gap: moderateScale(10),
    },
    avatar: {
      width: moderateScale(50),
      height: moderateScale(50),
      borderRadius: moderateScale(5),
    },
    numberSeries: {
      color: colors.foreground,
      fontWeight: '600',
      fontSize: moderateScale(14),
      marginTop: verticalScale(-2),
    },
    contentWrapper: {
      flex: 1,
      gap: verticalScale(3),
    },
    title: {
      color: colors.foreground,
      fontWeight: '600',
      fontSize: moderateScale(14),
      marginTop: verticalScale(-3),
    },
    description: {
      color: colors.descriptionSecondary,
      fontSize: moderateScale(9),
    },
    button: {
      paddingVertical: moderateScale(6),
      paddingHorizontal: moderateScale(14),
      borderRadius: moderateScale(20),
      marginTop: verticalScale(6),
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: colors.foreground,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
    },
    buttonText: {
      fontSize: moderateScale(12),
    },
  });
