import React, { useMemo } from 'react';
import { StyleSheet, useWindowDimensions, View } from 'react-native';
import { verticalScale } from 'react-native-size-matters';
import { TabBar, TabBarProps, TabView } from 'react-native-tab-view';

import CustomText from '../../components/common/text';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

import TabBarContent from './TabBarContent';

interface Route {
  key: string;
  title: string;
}

const renderScene = ({ route }: { route: Route }) => {
  const key = Number(route.key);
  switch (route.key) {
    case '1':
      return <TabBarContent id={key} />;
    case '2':
      return <TabBarContent id={key} />;
    case '3':
      return <TabBarContent id={key} />;
    case '4':
      return <TabBarContent id={key} />;
    case '5':
      return <TabBarContent id={key} />;
    case '6':
      return <TabBarContent id={key} />;
    case '7':
      return <TabBarContent id={key} />;
    case '8':
      return <TabBarContent id={key} />;
    case '9':
      return <TabBarContent id={key} />;
    case '10':
      return <TabBarContent id={key} />;
    default:
      return null;
  }
};

const Explore = () => {
  const layout = useWindowDimensions();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const [index, setIndex] = React.useState(0);
  const [routes] = React.useState([
    { key: '1', title: 'Business' },
    { key: '2', title: 'Comedy' },
    { key: '3', title: 'Sports' },
    { key: '4', title: 'News' },
    { key: '5', title: 'Entertainment' },
    { key: '6', title: 'Music' },
    { key: '7', title: 'Culture' },
    { key: '8', title: 'Relationships' },
    { key: '9', title: 'Mental Health' },
    { key: '10', title: 'Leisure' },
  ]);

  const renderTabBar = (props: TabBarProps<Route>) => (
    <TabBar
      {...props}
      scrollEnabled={true}
      renderLabel={({ route, focused }) => (
        <CustomText
          style={[
            styles.tabText,
            { color: focused ? colors.foreground : colors.secondary },
          ]}>
          {route.title}
        </CustomText>
      )}
      indicatorStyle={styles.indicator}
      style={styles.tabBar}
      tabStyle={styles.tabStyle}
    />
  );

  return (
    <View style={styles.container}>
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        initialLayout={{ width: layout.width }}
        renderTabBar={renderTabBar}
        lazy
      />
    </View>
  );
};

export default Explore;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: { flex: 1, backgroundColor: colors.background },
    tabBar: {
      backgroundColor: colors.background,
    },
    indicator: {
      backgroundColor: colors.accent,
      height: verticalScale(2),
    },
    tabStyle: {
      width: 'auto',
      height: verticalScale(30),
    },
    tabText: {
      fontFamily: 'Antonio-Bold',
      textTransform: 'uppercase',
    },
  });
