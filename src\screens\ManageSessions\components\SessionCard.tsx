import React, { useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Pressable } from 'react-native-gesture-handler';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import BarBottomIcon from '../../../assets/svgs/BarBottomIcon';
import ClockIcon from '../../../assets/svgs/ClockIcon';
import CrossIcon from '../../../assets/svgs/CrossIcon';
import SessionIcon from '../../../assets/svgs/SessionIcon';
import AlertModal from '../../../components/common/Modal';
import CustomText from '../../../components/common/text';
import { MANAGE_SESSIONS_TEXT } from '../../../constants/manageSessions';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import { SessionData } from '../../../types/manageSessions';

interface Props {
  item: SessionData;
  onDeleteSession: (item: SessionData) => void;
}

const SessionCard: React.FC<Props> = ({ item, onDeleteSession }) => {
  const [isVisible, setIsVisible] = useState(false);
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const handleDelete = () => setIsVisible(true);

  return (
    <View style={styles.card}>
      <View>
        <SessionIcon />
        <CustomText style={styles.deviceName}>{item.platform}</CustomText>
        <View style={styles.itemRow}>
          <ClockIcon color={colors.secondary} />
          <CustomText style={styles.browser}>{item.browser}</CustomText>
        </View>
        <View style={styles.itemRow}>
          <BarBottomIcon color={colors.secondary} />
          <CustomText style={styles.browser}>{item.time}</CustomText>
        </View>
        <View style={styles.itemRow}>
          <CustomText style={styles.browser}>
            {MANAGE_SESSIONS_TEXT.ipAddress}
            {item.ip_address}
          </CustomText>
        </View>
      </View>
      <Pressable onPress={handleDelete}>
        <CrossIcon color={colors.secondary} />
      </Pressable>
      <AlertModal
        title="Delete Session"
        message="Are you sure you want to delete this session?"
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        onConfirm={() => onDeleteSession(item)}
        confirmText="OK"
        cancelText="Cancel"
      />
    </View>
  );
};

export default SessionCard;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    card: {
      backgroundColor: colors.background,
      padding: moderateScale(10),
      flexDirection: 'row',
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.12,
      shadowRadius: 2.22,
      elevation: 3,
      borderRadius: 8,
      marginVertical: verticalScale(10),
      justifyContent: 'space-between',
    },
    deviceName: {
      fontSize: moderateScale(20),
      fontWeight: '700',
      marginTop: verticalScale(8),
      marginBottom: verticalScale(5),
      color: colors.foreground,
    },
    browser: {
      fontSize: moderateScale(10),
      color: colors.secondary,
      marginLeft: moderateScale(3),
    },
    itemRow: {
      flexDirection: 'row',
      marginBottom: verticalScale(5),
    },
  });
