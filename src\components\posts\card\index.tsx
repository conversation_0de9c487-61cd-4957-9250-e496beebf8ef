import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { PLAYER_STATES, YoutubeIframeRef } from 'react-native-youtube-iframe';

import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { setResumeVideo } from '../../../redux/slices/adSlice';
import {
  checkAdDisplay,
  playVideo,
  setShowMiniPlayer,
  setVideoTimestamp,
} from '../../../redux/slices/miniPlayerSlice';
import { EPostType } from '../../../screens/Home/HomeFeed';
import { fetchYTVideoDetails } from '../../../services/api/postsService';
import { Post, Reaction, YTVideoContent } from '../../../types/posts';

import { PostActions, PostContent, PostHeader } from './components';

export const PostCard: FC<Post> = ({
  user_id,
  id,
  publisher,
  reaction,
  post_time,
  youtube,
  postYoutube,
  url,
  is_post_saved,
  videoViews,
}) => {
  const webviewRef = useRef(null);

  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const { isAdVisible } = useAppSelector(state => state.ads);

  const style = useMemo(() => createStyles(colors), [colors]);
  const [playing, setPlaying] = useState(false);
  const [videoPlayingStatus, setVideoPlayingStatus] = useState<PLAYER_STATES>(
    PLAYER_STATES.UNSTARTED,
  );
  const [localReaction, setLocalReaction] = useState<Reaction>(reaction);
  const [videoDetails, setVideoDetails] = useState<YTVideoContent | null>(null);
  const videoRef = useRef<YoutubeIframeRef | null>(null);

  const togglePlaying = useCallback(
    async (state: PLAYER_STATES) => {
      const videoTime = await videoRef.current?.getCurrentTime();
      setVideoPlayingStatus(state);
      dispatch(setResumeVideo(false));
      const shouldShowAd = await dispatch(checkAdDisplay());

      if (!shouldShowAd) {
        setPlaying(false);
      } else {
        setPlaying(true);
      }

      switch (state) {
        case PLAYER_STATES.PLAYING:
          dispatch(setVideoTimestamp(videoTime!));
          dispatch(
            playVideo({
              id: id,
              title: videoDetails?.title!,
              subtitle: publisher?.first_name
                ? `${publisher?.first_name} ${publisher?.last_name}`
                : publisher?.username,
              reaction: localReaction,
              videoId: postYoutube,
              videoViews: videoViews,
              type: EPostType.POST,
            }),
          );
          setPlaying(true);
          break;
        case PLAYER_STATES.PAUSED:
          setPlaying(false);
          break;
        default:
          break;
      }
    },
    [videoDetails?.title],
  );

  const fetchContent = useCallback(async () => {
    if (!youtube) return;
    try {
      const content = await fetchYTVideoDetails({ src: youtube });
      setVideoDetails(content);
    } catch (error) {
      console.error('Error fetching video details:', error);
    }
  }, [youtube]);

  useEffect(() => {
    fetchContent();
  }, [fetchContent]);

  const handleSelectVideo = useCallback(() => {
    dispatch(
      playVideo({
        id: id,
        title: videoDetails?.title!,
        subtitle: publisher?.first_name
          ? `${publisher?.first_name} ${publisher?.last_name}`
          : publisher?.username,
        videoId: postYoutube,
        videoViews: videoViews,
        time: post_time,
        type: EPostType.POST,
      }),
    );
    dispatch(setShowMiniPlayer(true));
  }, [videoDetails?.title, post_time, postYoutube, videoViews, id, publisher]);

  const handlePlayAndPause = () => {
    if (isAdVisible) return;

    setPlaying(prev => !prev);

    if (!webviewRef.current) return;
    const jsCode = `document.querySelector('video').${playing ? 'pause' : 'play'}();`;
    // @ts-ignore
    webviewRef.current.injectJavaScript(jsCode);
  };

  return (
    <View style={style.container}>
      <PostContent
        playing={playing}
        code={postYoutube}
        title={videoDetails?.title}
        videoPlayingStatus={videoPlayingStatus}
        webviewRef={webviewRef}
        togglePlaying={togglePlaying}
        handleSelectVideo={handleSelectVideo}
      />
      <PostHeader
        username={
          publisher?.first_name
            ? `${publisher?.first_name} ${publisher?.last_name}`
            : publisher.username
        }
        avatarSrc={publisher.avatar}
        postedTime={post_time}
        userId={user_id}
        views={videoViews}
        reactions={localReaction}
        isVerified={Boolean(publisher?.verified)}
        postId={id}
      />
      <PostActions
        reactions={localReaction}
        id={id}
        is_post_saved={is_post_saved}
        title={videoDetails?.title}
        url={url}
        playing={playing}
        togglePlaying={handlePlayAndPause}
        setLocalReaction={setLocalReaction}
      />
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(8),
      gap: verticalScale(8),
    },
  });
