import { EFieldType } from '../types';

export const SIGNUP_FIELDS = [
  {
    name: 'username',
    placeholder: 'Username',
    keyboardType: 'email-address',
    type: EFieldType.TEXT,
  },
  { name: 'email', placeholder: 'Email', keyboardType: 'email-address' },
  {
    name: 'birthday',
    placeholder: 'Birthday (mm,dd,yy)',
    type: EFieldType.DATE,
  },
  {
    name: 'password',
    placeholder: 'Password',
    secureTextEntry: true,
    type: EFieldType.PASSWORD,
  },
  {
    name: 'confirm_password',
    placeholder: 'Confirm Password',
    secureTextEntry: true,
    type: EFieldType.PASSWORD,
  },
];

export const SIGNUP = {
  title: 'Signup',
  instruction: 'To sign up, you should be at least 17+.',
  agree: 'By Selecting Agree & Continue, I agree with ',
  terms: {
    content: 'Terms & Conditions, Payment Terms of Service',
    url: 'https://cpnpodcast.com/terms/terms',
  },
  policy: {
    content: 'Privacy Policy.',
    url: 'https://cpnpodcast.com/terms/privacy-policy',
  },
  submitBtn: 'Agree & Continue',
};

export const LOGIN_FIELDS = [
  {
    name: 'username',
    placeholder: 'Email or Username',
    keyboardType: 'email-address',
    EFieldType: EFieldType.TEXT,
  },
  {
    name: 'password',
    placeholder: 'Password',
    secureTextEntry: true,
    EFieldType: EFieldType.PASSWORD,
  },
  {
    name: 'saveLoginInfo',
    placeholder: 'Save login info',
  },
];

export const LOGIN = {
  title: 'Watch Anywhere, Stream Anytime',
  subTitle: '• Business • Sports • Relationship • Mental Health & More',
  forgotPassword: 'Forgot Password?',
  bottomText: 'Cancel anytime, it’s simple and hassle free.',
};

export const AUTH_TAB = {
  login: 'Log In',
  signup: 'Sign Up',
};

export const FORGOT_PASSWORD = {
  forgotPassword: 'Forgot Password?',
  resetPassword: 'Reset Password',
  forgotDescription:
    'Please enter the email address you’d like your password reset information sent to.',
  codeDescription: 'Please enter the code that we’ve sent on your email.',
  resetPasswordDescription: 'Please enter your new password.',
};
