import React, { useMemo, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { yupResolver } from '@hookform/resolvers/yup';

import CustomButton from '../../components/common/button';
import Input from '../../components/common/input';
import { CHANGE_PASSWORD_FIELDS } from '../../constants/profile';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { fetchUserDetails } from '../../redux/slices/authSlice';
import { updateUserData } from '../../services/api/userService';
import { changePasswordValidationSchema } from '../../utils/validationSchema';

interface IChangePassword {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const INITIAL_VALUES: IChangePassword = {
  current_password: '',
  new_password: '',
  confirm_password: '',
};

const ChangePassword = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { colors } = useAppSelector(state => state.theme);
  const { user } = useAppSelector(state => state.auth);
  const navigation = useTypeSafeNavigation();
  const dispatch = useAppDispatch();

  const styles = useMemo(() => createStyles(colors), [colors]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<IChangePassword>({
    resolver: yupResolver(changePasswordValidationSchema),
    defaultValues: INITIAL_VALUES,
  });

  const onSubmit: SubmitHandler<IChangePassword> = async data => {
    setIsLoading(true);
    const payload = {
      current_password: data.current_password,
      new_password: data.new_password,
    };
    const response = await updateUserData(payload);
    if (response) {
      dispatch(
        fetchUserDetails({
          data: { user_id: user?.userId as string, fetch: 'user_data' },
        }),
      );
      navigation.goBack();
    }
    setIsLoading(false);
  };

  return (
    <ScrollView style={styles.container}>
      {CHANGE_PASSWORD_FIELDS.map(field => (
        <Controller
          key={field.name}
          control={control}
          name={field.name as keyof IChangePassword}
          render={({ field: { onChange, value } }) => (
            <Input
              name={field.name}
              label={field.label}
              value={value as keyof IChangePassword}
              placeholder={field.placeholder}
              secureTextEntry={field.secureTextEntry}
              onChangeText={onChange}
              errors={errors}
            />
          )}
        />
      ))}
      <View style={styles.btnWrapper}>
        <CustomButton
          title="Save"
          onPress={handleSubmit(onSubmit)}
          loading={isLoading}
        />
      </View>
    </ScrollView>
  );
};

export default ChangePassword;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: moderateScale(20),
    },
    btnWrapper: {
      marginVertical: verticalScale(33),
    },
  });
