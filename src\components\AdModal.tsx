import React, { useMemo, useRef, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { moderateScale } from 'react-native-size-matters';
import Video, { VideoRef } from 'react-native-video';

import { ColorScheme, THEME_MODE } from '../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { hideAd } from '../redux/slices/adSlice';
import {
  resetVideoPlayCount,
  togglePlay,
} from '../redux/slices/miniPlayerSlice';
import { adAction } from '../services/api/adService';
import { formatDuration } from '../utils/ads';
import { openInAppBrowser } from '../utils/media';

import CustomText from './common/text';
import SwipeButton from './swipeToggle/SwipeButton';
import CustomModal from './Modal';

const AD_SKIP_DURATION = 15;

const AdModal: React.FC = () => {
  const videoRef = useRef<VideoRef>(null);
  const dispatch = useAppDispatch();
  const { ad, isAdVisible } = useAppSelector(state => state.ads);
  const { colors, theme } = useAppSelector(state => state.theme);
  const isDarkMode = theme === THEME_MODE.dark;
  const logoSource = isDarkMode
    ? require('../assets/png/toggle-thumb-icon-dark.png')
    : require('../assets/png/toggle-thumb-icon.png');

  const [timer, setTimer] = useState(15);
  const [duration, setDuration] = useState(0);
  const [timeLeft, setTimeLeft] = useState<number>(0);

  const styles = useMemo(
    () => createStyles(colors, isAdVisible),
    [colors, isAdVisible],
  );

  const handleAdSkip = async () => {
    if (ad) await adAction({ type: 'view', id: ad.data.id });
    dispatch(resetVideoPlayCount());
    dispatch(togglePlay(true));
    dispatch(hideAd());
  };

  const handleAdClick = async () => {
    if (ad) {
      await adAction({ type: 'click', id: ad.data.id });
      await openInAppBrowser(ad.data.url);
    }
    handleAdSkip();
  };

  return (
    <CustomModal
      containerStyle={styles.modalStyles}
      isVisible={isAdVisible}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationOutTiming={1000}
      hideModalContentWhileAnimating
      setIsVisible={() => {}}>
      <GestureHandlerRootView style={styles.container}>
        <View style={styles.modalContent}>
          <CustomText style={styles.adHeader}>Community Ad...</CustomText>
          <View style={styles.videoAdWrapper}>
            <Video
              source={{ uri: ad?.data.video }}
              controls={false}
              ref={videoRef}
              style={styles.adVideo}
              resizeMode="cover"
              playWhenInactive
              playInBackground={false}
              onLoad={({ duration: videoDuration }) => {
                const roundedDuration = Math.round(videoDuration);
                setDuration(roundedDuration);
                setTimeLeft(roundedDuration);
                setTimer(AD_SKIP_DURATION);
              }}
              onProgress={({ currentTime }) => {
                setTimeLeft(() => {
                  const remainingTime = Math.round(duration - currentTime);

                  return remainingTime >= 0 ? remainingTime : 0;
                });
                setTimer(
                  Math.max(AD_SKIP_DURATION - Math.round(currentTime), 0),
                );
              }}
            />

            <View style={styles.durationContainer}>
              <CustomText style={styles.durationText}>
                Ad {formatDuration(timeLeft)}
              </CustomText>
            </View>
          </View>

          <View style={styles.bottomContainer}>
            <TouchableOpacity onPress={handleAdClick}>
              <CustomText style={styles.learnMoreText}>
                {ad?.data?.button_title}
              </CustomText>
            </TouchableOpacity>

            {timer ? (
              <CustomText style={styles.skipText}>
                Skip in {timer} Sec
              </CustomText>
            ) : null}
          </View>
          <View style={styles.slideContainer}>
            <SwipeButton
              disabled={timer > 0}
              onSwipeSuccess={() => {
                handleAdSkip();
              }}
              height={moderateScale(60)}
              // eslint-disable-next-line react/no-unstable-nested-components
              thumbIconComponent={() => (
                <FastImage
                  source={logoSource}
                  style={{
                    width: moderateScale(69),
                    height: moderateScale(69),
                    marginBottom: moderateScale(-3),
                  }}
                />
              )}
              title="Slide to Skip"
            />
          </View>
        </View>
      </GestureHandlerRootView>
    </CustomModal>
  );
};

const createStyles = (colors: ColorScheme, isAdVisible: boolean) =>
  StyleSheet.create({
    modalStyles: {
      justifyContent: 'flex-end',
      margin: 0,
    },
    adModalContainer: {
      flex: isAdVisible ? 1 : 0,
    },
    container: {
      justifyContent: 'flex-end',
      backgroundColor: 'transparent',
    },
    bottomContainer: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: moderateScale(20),
      paddingHorizontal: moderateScale(10),
    },
    modalContent: {
      padding: moderateScale(18),
      height: 'auto',
      alignItems: 'center',
      paddingBottom: moderateScale(30),
      borderTopLeftRadius: 35,
      borderTopRightRadius: 35,
      overflow: 'hidden',
      backgroundColor: colors.background,
    },
    adHeader: {
      fontSize: moderateScale(16),
      marginBottom: moderateScale(10),
      color: colors.foreground,
    },
    adVideo: {
      width: '100%',
      height: '100%',
      borderRadius: moderateScale(12),
      overflow: 'hidden',
    },
    skipText: {
      fontSize: moderateScale(15),
      color: colors.foreground,
    },
    learnMoreText: {
      fontSize: moderateScale(15),
      color: colors.link,
      textDecorationLine: 'underline',
    },
    slideContainer: {
      width: '100%',
      marginTop: moderateScale(20),
    },
    slideText: {
      position: 'absolute',
      fontSize: moderateScale(18),
      zIndex: 1,
      color: colors.foreground,
    },
    slider: {
      width: moderateScale(65),
      height: moderateScale(65),
      borderRadius: moderateScale(50),
      backgroundColor: colors.background,
      position: 'absolute',
      left: 6,
      zIndex: 1,
    },
    durationText: {
      fontSize: moderateScale(14),
      fontWeight: 'bold',
      color: colors.white,
    },
    durationContainer: {
      position: 'absolute',
      left: 14,
      bottom: 14,
      height: moderateScale(30),
      borderRadius: moderateScale(6),
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      paddingHorizontal: moderateScale(10),
      alignItems: 'center',
      justifyContent: 'center',
    },
    videoAdWrapper: {
      position: 'relative',
      height: moderateScale(200),
      width: '100%',
    },
  });

export default AdModal;
