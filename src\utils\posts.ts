import { EpisodeCarouselType } from '../types/podcasts';

export const YT_URL_PATTERN =
  /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\/v=)([^#\&\?]*).*/;

export const formatViews = (views: number): string => {
  if (!views) return '0';
  if (views < 1_000) {
    return views.toString();
  } else if (views < 1_000_000) {
    return `${Math.floor((views / 1_000) * 10) / 10}K`;
  } else if (views < 1_000_000_000) {
    return `${Math.floor((views / 1_000_000) * 10) / 10}M`;
  }

  return `${Math.floor((views / 1_000_000_000) * 10) / 10}B`;
};

export const getCarouselListType = (list: number): EpisodeCarouselType => {
  switch (list) {
    case 1:
      return EpisodeCarouselType.carousel1;
    case 2:
      return EpisodeCarouselType.carousel2;
    case 3:
      return EpisodeCarouselType.carousel3;
    default:
      return EpisodeCarouselType.carousel1;
  }
};

export const injectedJavaScript = `
  (function() {
    function sendStateChange(state) {
      window.ReactNativeWebView.postMessage(state);
    }

    var video = document.querySelector('video');

    if (video) {
      video.onplay = function() { sendStateChange('playing'); };
      video.onpause = function() { sendStateChange('paused'); };
      video.onended = function() { sendStateChange('ended'); };
    }


  // To prevent video from being paused when swiping
     document.addEventListener('touchstart', function(event) {
      event.stopPropagation();
    }, true);
    document.addEventListener('touchmove', function(event) {
      event.stopPropagation();
    }, true);
    document.addEventListener('touchend', function(event) {
      event.stopPropagation();
    }, true);

  })();
`;

export const injectedCSS = `
    const style = document.createElement('style');
    style.innerHTML = 'body, html, iframe { margin: 0; padding: 0; height: 20px !important; width: 100% !important; display: block; }';
    document.head.appendChild(style);
  `;

export const getYoutubeURL = (code: string, isPlaying: boolean) =>
  `https://www.youtube.com/embed/${code}?rel=0&modestbranding=1&autoplay=${isPlaying ? '1' : '0'}&playsinline=1&enablejsapi=1`;
