import React, { useEffect, useMemo } from 'react';
import { FlatList, Pressable, StyleSheet, Text, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import BannerSkeleton from '../../components/skeleton/BannerSkeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchAdsBanners } from '../../redux/slices/bannerSlice';
import { Banner } from '../../types/banner';
import { openInAppBrowser } from '../../utils/media';

const BannerScreen = () => {
  const dispatch = useAppDispatch();
  const { banners, loading, error } = useAppSelector(state => state.banner);
  const { colors } = useAppSelector(state => state.theme);

  const styles = useMemo(() => createStyles(colors), [colors]);

  useEffect(() => {
    dispatch(fetchAdsBanners({ slider_id: 1 }));
  }, [dispatch]);

  const renderBannerItem = ({ item }: { item: Banner }) => (
    <Pressable
      style={styles.bannerItem}
      onPress={() => openInAppBrowser(item.url)}>
      <FastImage
        source={{ uri: item.image, priority: FastImage.priority.high }}
        style={styles.bannerImage}
      />
    </Pressable>
  );

  if (loading) {
    return <BannerSkeleton />;
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.error}>{error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={banners}
        renderItem={renderBannerItem}
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContainer: {
      padding: moderateScale(16),
      gap: verticalScale(10),
    },
    bannerItem: {
      shadowColor: colors.foreground,
      shadowOffset: {
        width: 2,
        height: 5,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    bannerImage: {
      width: '100%',
      height: verticalScale(80),
      borderRadius: moderateScale(10),
      overflow: 'hidden',
    },
    bannerContent: {
      padding: moderateScale(12),
    },
    mainhead: {
      fontSize: moderateScale(18),
      fontWeight: 'bold',
      marginBottom: 4,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    error: {
      color: colors.accent,
      fontSize: moderateScale(16),
    },
  });

export default BannerScreen;
