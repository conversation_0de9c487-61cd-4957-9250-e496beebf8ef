import * as React from 'react';
import Svg, { Path, SvgProps, G } from 'react-native-svg';

export const VotingIcon: React.FC<SvgProps> = props => {
  return (
    <Svg 
      width={22} 
      height={22} 
      viewBox="0 0 299.993 299.993" 
      {...props}
    >
      <G>
        <G>
          <G>
            <Path
              fill={props.fill || props.stroke || '#000000'}
              d="M181.985,124.5l-1.458,0.909l5.004,13.454c0.734,1.973,2.928,2.977,4.9,2.243l21.011-7.814
              c1.973-0.733,2.977-2.927,2.243-4.9l-13.933-37.463c-0.734-1.973-2.928-2.977-4.9-2.243l-7.587,2.822
              c0.646,0.775,1.251,1.6,1.8,2.481C195.534,104.369,192.365,118.029,181.985,124.5z"
            />
            <Path
              fill={props.fill || props.stroke || '#000000'}
              d="M176.571,115.814c5.584-3.48,7.289-10.828,3.808-16.412c-3.48-5.584-10.828-7.29-16.412-3.808l-17.356,10.819
              c-0.059-11.799-0.046-9.247-0.106-21.107c-0.077-15.469-12.724-28.053-28.192-28.053c-7.243,0-56.063,0-63.958,0
              c-15.468,0-28.116,12.583-28.193,28.052l-0.441,87.524c-0.033,6.579,5.273,11.94,11.853,11.973c0.02,0,0.041,0,0.061,0
              c6.46,0,11.879-5.236,11.912-11.853c0.011-2.148,0.438-87.126,0.44-87.524c0.016-1.266,1.05-2.281,2.315-2.272
              c1.265,0.008,2.286,1.035,2.286,2.301c0.001,29.865,0.005,200.243,0.005,200.243c0,7.896,6.401,14.297,14.296,14.297
              c7.89,0,14.297-6.401,14.297-14.297V171.424h6.172v114.271c0,7.896,6.411,14.297,14.296,14.297
              c7.896,0,14.296-6.401,14.296-14.297c0-53.144-0.246-145.072-0.259-200.244c-0.001-1.372,1.108-2.486,2.479-2.493
              c1.372-0.008,2.492,1.095,2.506,2.467c0.021,4.207,0.192,38.193,0.213,42.446c0.047,9.338,10.349,14.956,18.215,10.05
              L176.571,115.814z"
            />
            <Path
              fill={props.fill || props.stroke || '#000000'}
              d="M86.434,49.382c13.634,0,24.691-11.053,24.691-24.691C111.125,11.057,100.072,0,86.434,0
              C72.729,0,61.742,11.145,61.742,24.691C61.742,38.328,72.797,49.382,86.434,49.382z"
            />
            <Path
              fill={props.fill || props.stroke || '#000000'}
              d="M195.204,206.231c-3.565,0-5.572,2.852-5.572,6.432c0,3.521,2.007,6.432,5.606,6.432c3.602,0,5.572-2.913,5.572-6.501
              C200.81,209.012,198.725,206.231,195.204,206.231z"
            />
            <Path
              fill={props.fill || props.stroke || '#000000'}
              d="M262.432,152.258H147.202c-6.539,0-11.84,5.301-11.84,11.84v101.652c0,6.539,5.301,11.84,11.84,11.84h6.915v17.123
              c0,2.916,2.364,5.28,5.28,5.28h11.54c2.916,0,5.28-2.364,5.28-5.28v-17.123h57.2v17.123c0,2.916,2.364,5.28,5.28,5.28h11.54
              c2.916,0,5.28-2.364,5.28-5.28v-17.123h6.915c6.539,0,11.84-5.301,11.84-11.84V164.098
              C274.272,157.559,268.971,152.258,262.432,152.258z M174.291,224.839h-5.572l-9.081-24.421h7.085l4.781,15.993l4.712-15.993
              h7.051L174.291,224.839z M195.169,225.046c-7.152,0-12.348-5.944-12.348-12.451c0-6.523,5.3-12.314,12.486-12.314
              c7.307,0,12.313,6.072,12.313,12.451C207.62,219.206,202.319,225.046,195.169,225.046z M229.117,206.265H222.1v18.574h-6.707
              v-18.574h-7.051v-5.847h20.775V206.265z M248.964,224.839L248.964,224.839h-17.405v-24.421h17.095v5.847h-10.387v3.44h8.874
              v5.434h-8.874v3.853h10.697V224.839z"
            />
          </G>
        </G>
      </G>
    </Svg>
  );
}; 