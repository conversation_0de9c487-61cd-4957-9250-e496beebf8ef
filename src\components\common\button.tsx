import React, { useMemo } from 'react';
import {
  Pressable,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';
import ButtonLoader from '../ButtonLoader';

import CustomText from './text';

const CustomButton = ({
  title,
  variant = 'default',
  disabled = false,
  loading = false,
  size = 'default',
  textSyle = {},
  children,
  ...props
}: TouchableOpacityProps & {
  title?: string;
  variant?: 'default' | 'transparent' | 'secondary' | 'tertiary';
  size?: 'default' | 'small' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: StyleProp<ViewStyle>;
  textSyle?: StyleProp<TextStyle>;
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors, size), [colors, size]);

  return (
    <Pressable
      {...props}
      disabled={disabled}
      style={[
        styles.container,
        variant === 'transparent' && styles.transparentContainer,
        variant === 'secondary' && styles.secondaryContainer,
        variant === 'tertiary' && styles.tertiaryContainer,
        props.style,
      ]}>
      <View style={styles.text}>
        {loading ? (
          <View style={styles.loader}>
            <ButtonLoader />
          </View>
        ) : (
          <CustomText
            style={[
              styles.title,
              disabled && styles.disabled,
              variant === 'transparent' && styles.transparentTitle,
              variant === 'secondary' && styles.secondaryTitle,
              variant === 'tertiary' && styles.tertiaryTitle,
              textSyle,
            ]}>
            {title ? title : children}
          </CustomText>
        )}
      </View>
    </Pressable>
  );
};

export default CustomButton;

const createStyles = (
  color: ColorScheme,
  size: 'default' | 'small' | 'large',
) => {
  let sizeStyles;
  switch (size) {
    case 'small':
      sizeStyles = smallSizeStyles;
      break;
    case 'large':
      sizeStyles = largeSizeStyles;
      break;
    default:
      sizeStyles = defaultSizeStyles;
      break;
  }

  return StyleSheet.create({
    container: {
      backgroundColor: color.accent,
      justifyContent: 'center',
      ...sizeStyles.container,
    },
    transparentContainer: {
      backgroundColor: 'transparent',
      marginBottom: 0,
      marginHorizontal: 0,
      paddingVertical: moderateScale(5),
      paddingHorizontal: moderateScale(5),
    },
    secondaryContainer: {
      backgroundColor: color.base,
    },
    tertiaryContainer: {
      backgroundColor: color.background,
    },
    title: {
      color: color.white,
      textAlign: 'center',
      fontWeight: '500',
      alignItems: 'center',
      ...sizeStyles.title,
    },
    transparentTitle: {
      color: color.foreground,
    },
    secondaryTitle: {
      color: color.foreground,
    },
    tertiaryTitle: {
      color: color.foreground,
    },
    disabled: {
      color: color.background,
    },
    text: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      columnGap: 5,
    },
    loader: {
      height: 18,
      alignItems: 'center',
      justifyContent: 'center',
      display: 'flex',
    },
  });
};

const defaultSizeStyles = {
  container: {
    borderRadius: moderateScale(50),
    marginBottom: verticalScale(10),
    paddingVertical: moderateScale(16),
  },
  title: {
    fontSize: moderateScale(14),
  },
};

const smallSizeStyles = {
  container: {
    borderRadius: moderateScale(25),
    height: verticalScale(25),
    paddingHorizontal: moderateScale(10),
  },
  title: {
    fontSize: moderateScale(12),
  },
};

const largeSizeStyles = {
  container: {
    borderRadius: moderateScale(50),
    height: verticalScale(50),
    paddingHorizontal: moderateScale(16),
  },
  title: {
    fontSize: moderateScale(14),
  },
};
