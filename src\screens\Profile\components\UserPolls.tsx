import React, { FC, useEffect, useMemo } from 'react';
import { Alert, StyleSheet, View, ScrollView } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { fetchPolls, deletePoll } from '../../../redux/slices/pollsSlice';
import { ColorScheme } from '../../../constants/theme/colors';
import PollCard from '../../Polls/components/PollCard';
import CustomText from '../../../components/common/text';
import SinglePollSkeleton from './SinglePollSkeleton';

interface UserPollsProps {
  userId: string;
}

export const UserPolls: FC<UserPollsProps> = ({ userId }) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const { polls, status } = useAppSelector(state => state.polls);
  const styles = useMemo(() => createStyles(colors), [colors]);

  useEffect(() => {
    console.log('Fetching polls with params:', { action: 'user_polls', user_id: userId });
    dispatch(fetchPolls({ action: 'user_polls', user_id: userId }));
  }, [dispatch, userId]);

  if (status === 'loading') {
    return <SinglePollSkeleton />;
  }

  if (polls.length === 0) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.title}>My Polls</CustomText>
        <CustomText style={styles.emptyText}>No polls yet</CustomText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {polls.length > 0 && (
        <PollCard
          key={polls[0].id}
          poll={polls[0]}
          onDeletePress={pollId => {
            Alert.alert(
              'Delete Poll',
              'Are you sure you want to delete this poll?',
              [
                {
                  text: 'Cancel',
                  style: 'cancel',
                },
                {
                  text: 'Delete',
                  style: 'destructive',
                  onPress: () => {
                    dispatch(deletePoll(pollId));
                  },
                },
              ]
            );
          }}
        />
      )}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      marginVertical: verticalScale(10),
      paddingHorizontal: moderateScale(16),
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      fontWeight: '500',
      marginBottom: verticalScale(10),
      paddingHorizontal: moderateScale(20),
    },
    emptyText: {
      color: colors.secondary,
      fontSize: moderateScale(14),
      textAlign: 'center',
      marginTop: verticalScale(20),
    }
  });
