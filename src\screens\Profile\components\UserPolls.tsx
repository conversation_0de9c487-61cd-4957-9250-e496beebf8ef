import React, { FC, useEffect, useMemo } from 'react';
import { Alert, StyleSheet, View, ScrollView } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { fetchPolls, deletePoll } from '../../../redux/slices/pollsSlice';
import { ColorScheme } from '../../../constants/theme/colors';
import PollCard from '../../Polls/components/PollCard';
import CustomText from '../../../components/common/text';
import SinglePollSkeleton from './SinglePollSkeleton';

interface UserPollsProps {
  userId: string;
  isOtherUser?: boolean;
}

export const UserPolls: FC<UserPollsProps> = ({ userId, isOtherUser = false }) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const { polls, userPolls, status, userPollsStatus } = useAppSelector(state => state.polls);
  const { userDetails } = useAppSelector(state => state.auth);
  const styles = useMemo(() => createStyles(colors), [colors]);

  // Determine which polls to show and which status to check
  const isViewingOwnProfile = !isOtherUser && userDetails?.user_id === userId;
  const pollsToShow = isViewingOwnProfile
    ? polls.filter(poll => poll.creator.user_id === userId)
    : userPolls;
  const currentStatus = isViewingOwnProfile ? status : userPollsStatus;

  useEffect(() => {
    // Only fetch user_polls for other users, not for own profile
    if (!isViewingOwnProfile) {
      console.log('Fetching polls with params:', { action: 'user_polls', user_id: userId });
      dispatch(fetchPolls({ action: 'user_polls', user_id: userId }));
    }
  }, [dispatch, userId, isViewingOwnProfile]);

  if (currentStatus === 'loading') {
    return <SinglePollSkeleton />;
  }

  if (pollsToShow.length === 0) {
    return (
      <View style={styles.container}>
        
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CustomText style={styles.title}>{isOtherUser ? 'Polls' : 'My Polls'}</CustomText>
      {pollsToShow.length > 0 && (
        <PollCard
          key={pollsToShow[0].id}
          poll={pollsToShow[0]}
          onDeletePress={pollId => {
            Alert.alert(
              'Delete Poll',
              'Are you sure you want to delete this poll?',
              [
                {
                  text: 'Cancel',
                  style: 'cancel',
                },
                {
                  text: 'Delete',
                  style: 'destructive',
                  onPress: () => {
                    dispatch(deletePoll(pollId));
                  },
                },
              ]
            );
          }}
        />
      )}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      marginVertical: verticalScale(10),
      paddingHorizontal: moderateScale(16),
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      fontWeight: '500',
      marginBottom: verticalScale(10),
      paddingHorizontal: moderateScale(20),
    },
    
  });
