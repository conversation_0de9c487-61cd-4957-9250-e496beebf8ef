import { FC, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { removeRecentSearch } from '../../../redux/slices/searchSlice';
import { RecentSearch } from '../../../types/playlist';

dayjs.extend(relativeTime);

interface RecentSearchItemProps extends RecentSearch {
  onPress: (item: string) => void;
}

export const RecentSearchItem: FC<RecentSearchItemProps> = ({
  id,
  text,
  time,
  onPress,
}) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const handleOnPress = () => onPress(text);

  const handleRemoveRecentSearch = () => {
    dispatch(removeRecentSearch(id));
  };

  return (
    <Pressable onPress={handleOnPress} style={styles.container}>
      <View style={styles.contentContainer}>
        <CustomText style={styles.text}>{text}</CustomText>
        <CustomText style={styles.timestamp}>
          {dayjs(time).fromNow()}
        </CustomText>
      </View>

      <Pressable onPress={handleRemoveRecentSearch}>
        <Icon name="x" size={moderateScale(20)} color={colors.secondary} />
      </Pressable>
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      padding: moderateScale(10),
      paddingHorizontal: moderateScale(16),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    contentContainer: {
      gap: moderateScale(5),
    },
    text: {
      fontSize: moderateScale(16),
      fontWeight: 'semibold',
      color: colors.foreground,
    },
    timestamp: {
      fontSize: moderateScale(12),
      color: colors.secondary,
    },
  });
