import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { COLORS } from '../../constants/theme/colors';

interface ThemeState {
  theme: 'light' | 'dark';
  colors: typeof COLORS.light | typeof COLORS.dark;
}

const initialState: ThemeState = {
  theme: 'light',
  colors: COLORS.light,
};

export const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
      state.colors = COLORS[action.payload];
    },
    resetToSystemTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
      state.colors = COLORS[action.payload];
    },
  },
});

export const { setTheme, resetToSystemTheme } = themeSlice.actions;
