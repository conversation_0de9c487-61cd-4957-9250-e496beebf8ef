import { FC, useMemo } from 'react';
import { Alert, Pressable, StyleSheet, Text, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Entypo';

import { LikeSymbolIcon, VerifyIcon } from '../../../../assets/svgs';
import { ColorScheme } from '../../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../../hooks/redux';
import useTypeSafeNavigation from '../../../../hooks/useTypeSafeNavigation';
import { postAction, setPostsData } from '../../../../redux/slices/postsSlice';
import { Reaction } from '../../../../types/posts';
import { APP_ROUTES } from '../../../../types/routes';
import { formatViews } from '../../../../utils/posts';
import CustomText from '../../../common/text';

interface PostHeaderProps {
  username: string;
  postedTime: string;
  avatarSrc?: string;
  userId: string;
  views: string;
  reactions: Reaction;
  isVerified: boolean;
  postId: string;
}

export const PostHeader: FC<PostHeaderProps> = ({
  postedTime,
  avatarSrc,
  username,
  userId,
  views,
  reactions,
  isVerified,
  postId,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const { posts } = useAppSelector(state => state.posts);
  const { userDetails } = useAppSelector(state => state.auth);

  const styles = useMemo(() => createStyles(colors), [colors]);
  const navigation = useTypeSafeNavigation();
  const dispatch = useAppDispatch();

  // Check if current user is the post author
  const isPostAuthor = userDetails && userId === userDetails.user_id;

  const handleNavigateToProfile = () => {
    navigation.navigate(APP_ROUTES.OTHER_USER_PROFILE, {
      userId,
      otherUserProfile: true,
    });
  };

  const handleDeletePost = () => {
    Alert.alert(
      'Confirm Delete Post',
      'Are you sure you want to delete this post?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete Post',
          style: 'destructive',
          onPress: async () => {
            try {
              // Optimistically remove from UI first
              dispatch(setPostsData(posts.filter(post => post.id !== postId)));
              
              // Then call the API
              await dispatch(
                postAction({
                  post_id: postId,
                  action: 'delete',
                })
              ).unwrap();
              
              // Success - post already removed from UI
            } catch (error) {
              // If API call failed, restore the post to the UI
              console.error('Failed to delete post:', error);
              Alert.alert('Error', 'Failed to delete post. Please try again.');
              // Restore post by refetching
              // Note: You might want to add a refetch action or restore the specific post
            }
          },
        },
      ],
    );
  };

  return (
    <View style={styles.container}>
      <Pressable onPress={handleNavigateToProfile}>
        <FastImage
          style={styles.avatar}
          source={{
            uri: avatarSrc,
          }}
        />
      </Pressable>
      <View style={styles.subContainer}>
        <View style={styles.insightContainer}>
          <CustomText style={styles.username}>
            {username} {isVerified ? <VerifyIcon width={9} height={9} /> : null}
          </CustomText>
          <Text style={styles.subText}>{postedTime}</Text>
          <View style={styles.separator} />
          <Text style={styles.subText}>Youtube</Text>
          <View style={styles.separator} />
          <Text style={styles.views}>{formatViews(Number(views))} Views</Text>
          <View style={styles.separator} />
          <View style={styles.reactionContainer}>
            <View style={styles.likeContainer}>
              <LikeSymbolIcon
                width={moderateScale(10)}
                height={moderateScale(10)}
                fill={colors.foreground}
              />
            </View>
            <View>
              <CustomText style={styles.reactionText}>
                {reactions?.count ? reactions?.count : '0'}
              </CustomText>
            </View>
          </View>
        </View>
        {isPostAuthor && (
          <View style={styles.deleteContainer}>
            <Pressable onPress={handleDeletePost}>
              <Icon
                name="dots-three-horizontal"
                size={24}
                color={colors.foreground}
              />
            </Pressable>
          </View>
        )}
      </View>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(8),
    },
    subContainer: {
      gap: verticalScale(2),
      justifyContent: 'space-between',
      alignItems: 'center',
      flex: 1,
      flexDirection: 'row',
    },
    avatar: {
      borderRadius: 50,
      width: moderateScale(28),
      height: moderateScale(28),
    },
    username: {
      color: colors.foreground,
      fontSize: moderateScale(9),
      fontWeight: '500',
    },
    subText: {
      color: colors.foreground,
      fontSize: moderateScale(9),
      fontWeight: '400',
      fontFamily: 'Antonio-Regular',
    },
    views: {
      color: colors.foreground,
      fontSize: moderateScale(9),
      fontWeight: '600',
      fontFamily: 'Antonio-Regular',
    },
    separator: {
      height: verticalScale(8),
      width: verticalScale(1),
      backgroundColor: colors.foreground,
      borderRadius: moderateScale(5),
    },
    reactionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(2),
    },
    likeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    reactionIconsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    reactionText: {
      color: colors.foreground,
      fontSize: moderateScale(9),
      fontWeight: '400',
    },
    insightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(2),
    },
    deleteContainer: {
      paddingRight: moderateScale(10),
    },
  });
