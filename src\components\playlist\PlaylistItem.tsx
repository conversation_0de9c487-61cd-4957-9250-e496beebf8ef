import { FC, useCallback, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { playVideo } from '../../redux/slices/miniPlayerSlice';
import { EPostType } from '../../screens/Home/HomeFeed';
import { PlaylistVideos } from '../../types/playlist';
import { formatViews } from '../../utils/posts';
import CustomText from '../common/text';

interface PlaylistItemProps extends PlaylistVideos {
  selectedEpisodeId: string;
  channelName: string;
}

export const PlaylistItem: FC<PlaylistItemProps> = ({
  id,
  video_id,
  playlist_id,
  video_title,
  thumbnail_url,
  views = '1.2M',
  created_at,
  selectedEpisodeId,
  channelName,
}) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(
    () => createStyles(colors, Number(selectedEpisodeId) === id),
    [colors, selectedEpisodeId, id],
  );
  const { videoData } = useAppSelector(state => state.miniPlayer);
  const { isAdVisible } = useAppSelector(state => state.ads);

  const handleSelectPlaylist = useCallback(() => {
    if (isAdVisible) return;

    dispatch(
      playVideo({
        ...videoData,
        id: String(id),
        title: video_title,
        subtitle: channelName,
        videoId: video_id,
        videoViews: views,
        time: created_at,
        type: EPostType.PODCAST_CAROUSEL,
      }),
    );
  }, [
    dispatch,
    playlist_id,
    created_at,
    video_id,
    video_title,
    views,
    channelName,
    id,
    isAdVisible,
  ]);

  return (
    <Pressable style={styles.container} onPress={handleSelectPlaylist}>
      <View style={styles.imageContainer}>
        <FastImage source={{ uri: thumbnail_url }} style={styles.image} />
      </View>
      <View style={styles.content}>
        <CustomText numberOfLines={2} style={styles.title}>
          {video_title}
        </CustomText>
        <CustomText numberOfLines={1} style={styles.subtitle}>
          {channelName}
        </CustomText>
        <View style={styles.insightContainer}>
          <CustomText style={styles.timeAgo}> {created_at}</CustomText>
          <View style={styles.separator} />
          <CustomText style={styles.views}>
            {formatViews(Number(views))} Views
          </CustomText>
        </View>
      </View>
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme, isSelectedEpisode: boolean) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: isSelectedEpisode ? colors.base : colors.background,
      paddingHorizontal: moderateScale(14),
      paddingVertical: verticalScale(5),
    },
    imageContainer: {
      position: 'relative',
    },
    image: {
      width: moderateScale(130),
      height: verticalScale(62),
      borderRadius: moderateScale(6),
    },
    content: {
      flex: 1,
      marginLeft: moderateScale(8),
      justifyContent: 'space-around',
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(13),
    },
    subtitle: {
      color: colors.foreground,
      fontSize: moderateScale(13),
      fontWeight: '500',
    },
    views: {
      color: colors.secondary,
      fontSize: moderateScale(11),
    },
    separator: {
      height: verticalScale(2),
      width: verticalScale(2),
      backgroundColor: colors.secondary,
      borderRadius: moderateScale(5),
      marginHorizontal: moderateScale(4),
    },
    timeAgo: {
      color: colors.secondary,
      fontSize: moderateScale(11),
    },
    insightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    duration: {
      position: 'absolute',
      bottom: verticalScale(5),
      left: moderateScale(5),
      backgroundColor: 'rgba(0,0,0,0.7)',
      color: colors.white,
      fontSize: moderateScale(8),
      paddingVertical: moderateScale(1),
      paddingHorizontal: moderateScale(4),
      borderRadius: moderateScale(3),
      overflow: 'hidden',
    },
  });
