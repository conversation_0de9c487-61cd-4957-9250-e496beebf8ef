import React, { FC, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

interface MiniPlayerSkeletonProps {
  isMiniPlayerVisible: boolean;
}

export const MiniPlayerSkeleton: FC<MiniPlayerSkeletonProps> = ({
  isMiniPlayerVisible,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  if (!isMiniPlayerVisible) {
    return (
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item
          height="100%"
          width={moderateScale(375)}
          borderRadius={moderateScale(5)}
          marginVertical={verticalScale(10)}
        />
      </SkeletonPlaceholder>
    );
  }

  return (
    <View style={styles.container}>
      <SkeletonPlaceholder
        backgroundColor={colors.surface}
        highlightColor={colors.highlightSurface}>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          gap={moderateScale(10)}
          marginVertical={verticalScale(6)}>
          <SkeletonPlaceholder.Item
            height={moderateScale(40)}
            marginVertical={verticalScale(5)}
            width={moderateScale(85)}
            borderRadius={moderateScale(5)}
          />

          <View style={styles.subContainer}>
            <SkeletonPlaceholder.Item
              height={verticalScale(8)}
              width={moderateScale(170)}
              borderRadius={moderateScale(5)}
            />
            <SkeletonPlaceholder.Item
              height={verticalScale(8)}
              width={moderateScale(150)}
              borderRadius={moderateScale(5)}
              marginTop={verticalScale(5)}
            />
            <SkeletonPlaceholder.Item
              height={verticalScale(6)}
              width={moderateScale(80)}
              borderRadius={moderateScale(5)}
              marginTop={verticalScale(5)}
            />
          </View>
          <View style={styles.actionContainer}>
            <SkeletonPlaceholder.Item
              height={moderateScale(30)}
              width={moderateScale(30)}
              borderRadius={moderateScale(10)}
            />
            <SkeletonPlaceholder.Item
              height={moderateScale(30)}
              width={moderateScale(30)}
              borderRadius={moderateScale(10)}
            />
          </View>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      alignItems: 'center',
    },
    subContainer: {
      justifyContent: 'center',
    },
    actionContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      gap: moderateScale(10),
    },
  });
