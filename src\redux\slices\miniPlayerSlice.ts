import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { EPostType } from '../../screens/Home/HomeFeed';
import { Reaction } from '../../types/posts';
import { getItemFromAS, setItemToAS, STORAGE_KEYS } from '../../utils/storage';

import { fetchAd, showAd } from './adSlice';

interface IVideoData {
  type: EPostType;
  id?: string;
  videoId?: string;
  playlistId?: string;
  channelId?: string;
  title?: string;
  subtitle?: string;
  videoViews?: string;
  reaction?: Reaction;
  time?: string;
  list?: number;
}

interface MiniPlayerState {
  videoData: IVideoData | null;
  isPlaying: boolean;
  showMiniPlayer: boolean;
  timestamp: number;
  isMiniPlayer: boolean;
  isFetchNewPlaylist: boolean;
  videoPlayCount: number;
  showAd: boolean;
}

const initialState: MiniPlayerState = {
  videoData: null,
  isPlaying: false,
  showMiniPlayer: false,
  timestamp: 0,
  isMiniPlayer: false,
  isFetchNewPlaylist: false,
  videoPlayCount: 0,
  showAd: false,
};

export const loadVideoPlayCount = createAsyncThunk(
  'miniPlayer/loadVideoPlayCount',
  async () => {
    const count = await getItemFromAS(STORAGE_KEYS.VIDEO_PLAY_COUNT);

    return count ? parseInt(count, 10) : 0;
  },
);

export const checkAdDisplay = createAsyncThunk(
  'miniPlayer/checkAdDisplay',
  async (_, { dispatch }) => {
    const threshold = await getItemFromAS(STORAGE_KEYS.AD_THRESHOLD);
    const count = await getItemFromAS(STORAGE_KEYS.VIDEO_PLAY_COUNT);
    const adLimit = threshold ? JSON.parse(threshold) : 5;
    const countNumber = count ? parseInt(count, 10) : 0;

    const shouldShowAd = countNumber >= adLimit;

    if (shouldShowAd) {
      dispatch(showAd(true));
      const response = await dispatch(fetchAd());

      // @ts-ignore
      if (response?.payload?.data?.video) {
        dispatch(togglePlay(false));
        dispatch(miniPlayerSlice.actions.pauseVideo());
      } else {
        return false;
      }
    }

    return shouldShowAd;
  },
);

export const playVideo = createAsyncThunk(
  'miniPlayer/playVideo',
  async (videoData: IVideoData, { dispatch }) => {
    dispatch(miniPlayerSlice.actions.incrementPlayCount());

    const shouldShowAd = await dispatch(checkAdDisplay());
    dispatch(miniPlayerSlice.actions.setVideoData(videoData));

    if (!shouldShowAd.payload) {
      dispatch(miniPlayerSlice.actions.togglePlay(true));
    }
  },
);

export const miniPlayerSlice = createSlice({
  name: 'miniPlayer',
  initialState,
  reducers: {
    setVideoData: (state, action: PayloadAction<IVideoData>) => {
      state.videoData = action.payload;
    },
    incrementPlayCount: state => {
      state.videoPlayCount += 1;
      setItemToAS(
        STORAGE_KEYS.VIDEO_PLAY_COUNT,
        state.videoPlayCount.toString(),
      );
    },
    resetVideoPlayCount: state => {
      state.videoPlayCount = 0;
      state.showAd = false;
      setItemToAS(STORAGE_KEYS.VIDEO_PLAY_COUNT, '0');
    },
    stopVideo: state => {
      state.videoData = null;
      state.isPlaying = false;
      state.showMiniPlayer = false;
    },
    togglePlay: (state, action: PayloadAction<boolean>) => {
      state.isPlaying = action.payload;
    },
    pauseVideo: state => {
      state.isPlaying = false;
    },
    setShowMiniPlayer: (state, action: PayloadAction<boolean>) => {
      state.showMiniPlayer = action.payload;
    },
    setVideoTimestamp: (state, action: PayloadAction<number>) => {
      state.timestamp = action.payload;
    },
    setIsMiniPlayer: (state, action: PayloadAction<boolean>) => {
      state.isMiniPlayer = action.payload;
    },
    setIsFetchNewPlaylist: (state, action: PayloadAction<boolean>) => {
      state.isFetchNewPlaylist = action.payload;
    },
  },
  extraReducers: builder => {
    builder.addCase(checkAdDisplay.fulfilled, (state, action) => {
      state.showAd = action.payload;
    });
    builder.addCase(loadVideoPlayCount.fulfilled, (state, action) => {
      state.videoPlayCount = action.payload;
    });
  },
});

export const {
  stopVideo,
  togglePlay,
  setShowMiniPlayer,
  setVideoTimestamp,
  setIsMiniPlayer,
  setIsFetchNewPlaylist,
  resetVideoPlayCount,
  incrementPlayCount,
} = miniPlayerSlice.actions;

export default miniPlayerSlice.reducer;
