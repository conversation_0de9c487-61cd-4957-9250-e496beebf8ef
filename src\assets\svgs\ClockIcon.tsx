import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

interface ClockIconProps extends SvgProps {
  color?: string;
}

const ClockIcon: React.FC<ClockIconProps> = ({
  color = '#444444',
  ...props
}) => (
  <Svg width={14} height={14} viewBox="0 0 14 14" fill="none" {...props}>
    <Path
      d="M7 4.08333V7H9.91667M7 12.25C4.1005 12.25 1.75 9.89949 1.75 7C1.75 4.1005 4.1005 1.75 7 1.75C9.89949 1.75 12.25 4.1005 12.25 7C12.25 9.89949 9.89949 12.25 7 12.25Z"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

export default ClockIcon;
