import React, { FC, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Svg, { Rect } from 'react-native-svg';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

const AnimatedRect = Animated.createAnimatedComponent(Rect);

interface AnimatedBorderButtonProps {
  children: React.ReactNode;
  onPress?: () => void;
}

export const AnimatedButton: FC<AnimatedBorderButtonProps> = ({
  children,
  onPress,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const borderAnimation = useSharedValue(100);
  const opacityAnimation = useSharedValue(0);

  const animatedRectProps = useAnimatedProps(() => ({
    strokeDashoffset: borderAnimation.value,
    opacity: opacityAnimation.value,
  }));

  const handlePress = () => {
    onPress?.();

    borderAnimation.value = withSequence(
      withTiming(0, { duration: 1500 }),
      withTiming(100, { duration: 0 }),
    );

    opacityAnimation.value = withSequence(
      withTiming(1, { duration: 300 }),
      withTiming(0, { duration: 1200 }),
    );
  };

  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <View style={styles.subContainer}>
        <Svg style={styles.animatedBorder}>
          <AnimatedRect
            x="2"
            y="2"
            width="91"
            height="32"
            rx="25"
            ry="25"
            stroke="red"
            strokeWidth="1"
            strokeDasharray="120"
            animatedProps={animatedRectProps}
            fill="none"
          />
        </Svg>
        {children}
      </View>
    </Pressable>
  );
};

const createStyles = (color: ColorScheme) =>
  StyleSheet.create({
    container: {
      justifyContent: 'center',
      borderRadius: moderateScale(50),
      backgroundColor: color.base,
      marginBottom: 0,
      marginHorizontal: 0,
      width: moderateScale(86),
    },
    subContainer: {
      height: verticalScale(28),
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
    },
    grayBorder: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: moderateScale(50),
      borderWidth: 2,
      borderColor: color.secondary,
    },
    animatedBorder: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
  });
