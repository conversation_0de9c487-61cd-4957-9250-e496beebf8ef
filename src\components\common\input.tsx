import React, { useMemo, useState } from 'react';
import { FieldErrors } from 'react-hook-form';
import {
  StyleProp,
  StyleSheet,
  TextInput,
  TextInputProps,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { ErrorMessage } from '@hookform/error-message';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

import CustomText from './text';

interface InputProps extends TextInputProps {
  label?: string;
  name?: string;
  errors?: FieldErrors;
  disabled?: boolean;
  inputContainerStyle?: StyleProp<ViewStyle>;
}

const Input: React.FC<InputProps> = ({
  label,
  name,
  errors,
  disabled,
  inputContainerStyle,
  ...rest
}) => {
  const { colors } = useAppSelector(state => state.theme);

  const [isPasswordHidden, setPasswordHidden] = useState(
    !!rest.secureTextEntry,
  );

  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={[styles.container, inputContainerStyle]}>
      {label ? (
        <CustomText style={styles.inputLabel}>{label}</CustomText>
      ) : null}
      <View>
        <TextInput
          {...rest}
          editable={!disabled}
          style={[
            styles.inputText,
            disabled && styles.disabledInput,
            rest.style,
          ]}
          placeholderTextColor={`${colors.secondary}80`}
          secureTextEntry={isPasswordHidden && !!rest.secureTextEntry}
        />
        {rest.secureTextEntry ? (
          <TouchableWithoutFeedback
            onPress={() => !disabled && setPasswordHidden(!isPasswordHidden)}>
            <Icon
              name={isPasswordHidden ? 'eye' : 'eye-off'}
              size={moderateScale(16)}
              color={colors.secondary}
              style={styles.eyeIcon}
            />
          </TouchableWithoutFeedback>
        ) : null}
      </View>
      {errors ? (
        <ErrorMessage
          errors={errors}
          name={String(name)}
          render={({ message }) => (
            <CustomText style={styles.errorMsg}>{message}</CustomText>
          )}
        />
      ) : null}
    </View>
  );
};

export default Input;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      marginVertical: verticalScale(7),
    },
    inputLabel: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      fontWeight: '400',
      marginBottom: moderateScale(10),
    },
    inputText: {
      backgroundColor: colors.base,
      color: colors.foreground,
      fontSize: moderateScale(14),
      borderRadius: moderateScale(100),
      paddingHorizontal: moderateScale(20),
      height: moderateScale(46),
    },
    disabledInput: {
      backgroundColor: `${colors.base}70`,
      color: `${colors.foreground}70`,
    },
    errorMsg: {
      color: '#ff0000',
      fontSize: moderateScale(12),
      marginTop: verticalScale(5),
      marginLeft: moderateScale(5),
    },
    eyeIcon: {
      position: 'absolute',
      right: moderateScale(12),
      alignSelf: 'center',
      top: '50%',
      transform: [{ translateY: -moderateScale(8) }],
    },
  });
