import { Post } from './posts';

export interface GetNotificationsResponse {
  api_status: number;
  list: Notifications[];
}

export interface Notifications {
  id: string;
  notifier_id: string;
  recipient_id: string;
  post_id: string;
  reply_id: string;
  comment_id: string;
  page_id: string;
  group_id: string;
  group_chat_id: string;
  event_id: string;
  thread_id: string;
  blog_id: string;
  story_id: string;
  seen_pop: string;
  type: string;
  text: string;
  url: string;
  full_link: string;
  seen: string;
  sent_push: string;
  admin: string;
  time: string;
  notifier: Notifier;
  ajax_url: string;
  time2: string;
  post_data: Post;
}

export interface Notifier {
  user_id: string;
  username: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  avatar: string;
  cover: string;
  background_image: string;
  background_image_status: string;
  relationship_id: string;
  address: string;
  working: string;
  working_link: string;
  about: string;
  school: string;
  gender: string;
  birthday: string;
  country_id: string;
  website: string;
  facebook: string;
  google: string;
  twitter: string;
  linkedin: string;
  youtube: string;
  vk: string;
  instagram: string;
  qq: string | null;
  wechat: string | null;
  discord: string | null;
  mailru: string | null;
  okru: string;
  language: string;
  email_code: string;
  src: string;
  ip_address: string;
  follow_privacy: string;
  friend_privacy: string;
  post_privacy: string;
  message_privacy: string;
  confirm_followers: string;
  show_activities_privacy: string;
  birth_privacy: string;
  visit_privacy: string;
  verified: string;
  lastseen: string;
  showlastseen: string;
  emailNotification: string;
  e_liked: string;
  e_wondered: string;
  e_shared: string;
  e_followed: string;
  e_commented: string;
  e_visited: string;
  e_liked_page: string;
  e_mentioned: string;
  e_joined_group: string;
  e_accepted: string;
  e_profile_wall_post: string;
  e_sentme_msg: string;
  e_last_notif: string;
  notification_settings: string;
  status: string;
  active: string;
  admin: string;
  type: string;
  registered: string;
  start_up: string;
  start_up_info: string;
  startup_follow: string;
  startup_image: string;
  last_email_sent: string;
  phone_number: string;
  sms_code: string;
  is_pro: string;
  pro_time: string;
  pro_type: string;
  pro_remainder: string;
  joined: string;
  css_file: string;
  timezone: string;
  referrer: string;
  ref_user_id: string;
  ref_level: string | null;
  balance: string;
  paypal_email: string;
  notifications_sound: string;
  order_posts_by: string;
  social_login: string;
  android_m_device_id: string;
  ios_m_device_id: string;
  android_n_device_id: string;
  ios_n_device_id: string;
  web_device_id: string;
  wallet: string;
  lat: string;
  lng: string;
  last_location_update: string;
  share_my_location: string;
  last_data_update: string;
  details: {
    post_count: number;
    album_count: string;
    following_count: number;
    followers_count: number;
    groups_count: number;
    likes_count: number;
    mutual_friends_count: number;
  };
  sidebar_data: string;
  last_avatar_mod: string;
  last_cover_mod: string;
  points: string;
  daily_points: string;
  converted_points: string;
  point_day_expire: string;
  last_follow_id: string;
  share_my_data: string;
  last_login_data: string | null;
  two_factor: string;
  two_factor_hash: string;
  new_email: string;
  two_factor_verified: string;
  new_phone: string;
  info_file: string;
  city: string;
  state: string;
  zip: string;
  school_completed: string;
  weather_unit: string;
  paystack_ref: string;
  code_sent: string;
  time_code_sent: string;
  permission: string | null;
  skills: string | null;
  languages: string | null;
  currently_working: string;
  banned: string;
  banned_reason: string;
  likes_count: string;
  last_count: string;
  oneId: string;
  color: string;
  is_facebook: string;
  is_google: string;
  is_twitter: string;
  is_linkedin: string;
  is_youtube: string;
  is_vk: string;
  is_instagram: string;
  is_qq: string | null;
  is_wechat: string | null;
  is_discord: string | null;
  is_mailru: string | null;
  is_okru: string;
  title: string;
  avatar_post_id: string;
  cover_post_id: string;
  avatar_org: string;
  cover_org: string;
  cover_full: string;
  avatar_full: string;
  id: string;
  user_platform: string;
  url: string;
  name: string;
  API_notification_settings: {
    e_liked: number;
    e_shared: number;
    e_wondered: number;
    e_commented: number;
    e_followed: number;
    e_accepted: number;
    e_mentioned: number;
    e_joined_group: number;
    e_liked_page: number;
    e_visited: number;
    e_profile_wall_post: number;
    e_memory: number;
  };
  is_notify_stopped: string;
  following_data: string[];
  followers_data: string;
  mutual_friends_data: string[];
  likes_data: string;
  groups_data: string;
  album_data: string;
  lastseen_unix_time: string;
  lastseen_status: string;
  is_reported: boolean;
  is_story_muted: boolean;
  is_following_me: boolean;
  is_reported_user: boolean;
  is_open_to_work: boolean;
  is_providing_service: boolean;
  providing_service: string;
  open_to_work_data: string;
  formated_langs: string[];
}
