import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { adAction } from '../../services/api/adService';
import { Ad } from '../../types/ads';

export const fetchAd = createAsyncThunk('ads/fetchAd', async () => {
  const ad = await adAction({ type: 'random' });

  return ad;
});

interface AdState {
  ad: Ad | null;
  isAdVisible: boolean;
  resumeVideo: boolean;
}

export const adSlice = createSlice({
  name: 'ads',
  initialState: {
    ad: null,
    isAdVisible: false,
    resumeVideo: false,
  } as AdState,
  reducers: {
    showAd: (state, action) => {
      state.isAdVisible = action.payload;
    },
    hideAd(state) {
      state.isAdVisible = false;
      state.ad = null;
      state.resumeVideo = true;
    },
    setResumeVideo(state, action) {
      state.resumeVideo = action.payload;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchAd.fulfilled, (state, action) => {
      state.ad = action.payload;
    });
  },
});

export const { showAd, hideAd, setResumeVideo } = adSlice.actions;
export default adSlice.reducer;
