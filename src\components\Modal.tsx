import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import Modal, { ModalProps } from 'react-native-modal';

interface Props extends Partial<ModalProps> {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  containerStyle?: StyleProp<ViewStyle>;
  children: React.ReactNode;
}

const CustomModal: React.FC<Props> = ({
  isVisible,
  setIsVisible,
  containerStyle,
  children,
  ...rest
}) => (
  <Modal
    isVisible={isVisible}
    onBackdropPress={() => setIsVisible(false)}
    style={containerStyle}
    {...rest}>
    <>{children}</>
  </Modal>
);

export default CustomModal;
