import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const UserIcon = (props: SvgProps) => (
  <Svg width={14} height={16} viewBox="0 0 14 16" fill="none" {...props}>
    <Path
      d="M13 14.75V13.25C13 12.4544 12.6839 11.6913 12.1213 11.1287C11.5587 10.5661 10.7956 10.25 10 10.25H4C3.20435 10.25 2.44129 10.5661 1.87868 11.1287C1.31607 11.6913 1 12.4544 1 13.25V14.75M10 4.25C10 5.90685 8.65685 7.25 7 7.25C5.34315 7.25 4 5.90685 4 4.25C4 2.59315 5.34315 1.25 7 1.25C8.65685 1.25 10 2.59315 10 4.25Z"
      stroke="#C41208"
      strokeWidth={1.25}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
