import { FC, useCallback, useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import {
  playVideo,
  setIsFetchNewPlaylist,
  setShowMiniPlayer,
} from '../../../redux/slices/miniPlayerSlice';
import { EPostType } from '../../../screens/Home/HomeFeed';
import { HighlightedEpisode, Podcast } from '../../../types/podcasts';
import CustomText from '../../common/text';
export interface CardProps extends HighlightedEpisode, Podcast {}

export const Card: FC<CardProps> = ({
  id,
  image,
  title,
  url,
  vidId,
  small,
  list,
  views,
  time,
}) => {
  const dispatch = useAppDispatch();
  const navigation = useTypeSafeNavigation();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors, vidId), [colors, vidId]);
  const { videoData } = useAppSelector(state => state.miniPlayer);
  const handleNavigate = useCallback(() => {
    if (vidId && list) {
      dispatch(
        playVideo({
          id: String(id),
          title: title,
          subtitle: 'Highlighted Episodes',
          videoId: vidId,
          videoViews: String(views),
          time: time,
          list: list,
          type: EPostType.HIGHLIGHTED_EPISODES,
        }),
      );
      dispatch(setIsFetchNewPlaylist(true));
      dispatch(setShowMiniPlayer(true));

      return;
    }

    const playlistId = url?.split('/')?.pop();
    if (playlistId! !== videoData?.playlistId) {
      dispatch(
        playVideo({
          playlistId: playlistId,
          subtitle: title,
          type: EPostType.PODCAST_CAROUSEL,
        }),
      );
      dispatch(setShowMiniPlayer(true));
    }
  }, [navigation, url, videoData?.playlistId]);

  return (
    <Pressable onPress={handleNavigate} style={styles.container}>
      <View style={styles.playerContainer}>
        <FastImage
          source={{ uri: vidId ? small : image }}
          style={styles.coverImage}
        />
      </View>
      <View style={styles.footerContainer}>
        <View style={styles.titleContainer}>
          <CustomText numberOfLines={2} style={styles.title}>
            {title}
          </CustomText>
        </View>
      </View>
    </Pressable>
  );
};

const createStyles = (colors: ColorScheme, vidId?: string) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      paddingTop: moderateScale(10),
      borderRadius: moderateScale(10),
      width: vidId ? moderateScale(152) : moderateScale(162),
    },
    titleContainer: {
      flex: 1,
      justifyContent: 'center',
    },
    title: {
      fontWeight: '500',
      color: colors.foreground,
    },
    playerContainer: {
      width: vidId ? moderateScale(145) : moderateScale(150),
      height: vidId ? moderateScale(82) : moderateScale(150),
      borderRadius: moderateScale(10),
      overflow: 'hidden',
      marginHorizontal: 'auto',
    },
    footerContainer: {
      width: '100%',
      paddingHorizontal: moderateScale(6),
      marginTop: verticalScale(8),
      flexDirection: 'row',
      gap: moderateScale(5),
    },
    playButton: {
      height: moderateScale(26),
      width: moderateScale(26),
      backgroundColor: colors.base,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: moderateScale(50),
    },
    webView: { borderRadius: 10 },
    coverImage: { width: '100%', height: '100%' },
  });
