import React, { FC, useMemo, useState } from 'react';
import { Platform, Pressable, StyleSheet, View } from 'react-native';
import {
  RenderItemParams,
  ScaleDecorator,
} from 'react-native-draggable-flatlist';
import FastImage from 'react-native-fast-image';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Toast from 'react-native-toast-message';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { DeleteIcon } from '../../../assets/svgs';
import Input from '../../../components/common/input';
import CustomText from '../../../components/common/text';
import ToggleButton from '../../../components/ToggleButton';
import { ColorScheme } from '../../../constants/theme/colors';
import { TOAST_TITLE, TOAST_TYPE } from '../../../constants/toast';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import {
  deleteUserFeaturedCards,
  FeaturedCardValues,
} from '../../../redux/slices/userSlice';
import { openGallery } from '../../../utils/media';
import { IUserData } from '../UpdateProfile';

export interface FeaturedCardProps {
  setUserData: React.Dispatch<React.SetStateAction<IUserData>>;
}

export const FeaturedCard: FC<
  RenderItemParams<FeaturedCardValues> & FeaturedCardProps
> = ({ item, isActive, drag, setUserData }) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [imageUri, setImageUri] = useState(item.image);
  const [isEnabled, setIsEnabled] = useState(Boolean(item.enabled));

  const handleValueChange = (key: string, value: string) => {
    setUserData((prev: IUserData) => ({
      ...prev,
      featuredCards: prev.featuredCards.map(card => {
        if (card.id === item.id) {
          return {
            ...card,
            [key]: value,
          };
        }

        return card;
      }),
    }));
  };

  const pickFeatureImage = async () => {
    const response = await openGallery();
    if (!response) return;
    handleValueChange('image', response);
    setImageUri(response);
  };

  const toggleEnabled = () => {
    setIsEnabled(prev => !prev);
    handleValueChange('enabled', isEnabled ? '0' : '1');
  };

  const handleDeleteCard = (card_id: number) => {
    if (item.isNew) {
      Toast.show({
        type: TOAST_TYPE.ERROR,
        text1: TOAST_TITLE.PROFILE,
        text2: 'cannot delete the empty card',
      });

      return;
    }
    dispatch(deleteUserFeaturedCards({ card_id }));
  };

  return (
    <ScaleDecorator>
      <View style={styles.container}>
        <View style={styles.subContainer}>
          <View>
            <Pressable
              style={styles.dragIcon}
              onLongPress={drag}
              disabled={isActive}>
              <MaterialIcons
                name="drag-indicator"
                size={moderateScale(24)}
                color={colors.secondary}
              />
            </Pressable>
          </View>
          <View style={styles.contentContainer}>
            <View style={styles.imageContainer}>
              {imageUri ? (
                <FastImage
                  source={{
                    uri: imageUri,
                  }}
                  style={styles.previewImage}
                  fallback={Platform.OS === 'android'}
                />
              ) : null}
              <Pressable style={styles.uploadImage} onPress={pickFeatureImage}>
                <EvilIcons
                  name="image"
                  size={moderateScale(20)}
                  color={colors.foreground}
                />
                <CustomText style={styles.uploadImageText}>
                  Add Image
                </CustomText>
              </Pressable>
            </View>
            <Input
              placeholder="Title"
              value={item.title}
              onChangeText={value => handleValueChange('title', value)}
              style={[styles.title, styles.input]}
            />
            <Input
              placeholder="Link"
              value={item.link}
              name="link"
              style={[styles.link, styles.input]}
              onChangeText={value => handleValueChange('link', value)}
            />
          </View>
          <View style={styles.actionContainer}>
            <ToggleButton
              isEnabled={isEnabled}
              toggleSwitch={toggleEnabled}
              style={styles.toggleButton}
            />
            <Pressable onPress={() => handleDeleteCard(item.id as number)}>
              <DeleteIcon stroke={colors.foreground} />
            </Pressable>
          </View>
        </View>
      </View>
    </ScaleDecorator>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      borderRadius: moderateScale(8),
      paddingVertical: verticalScale(20),
      paddingHorizontal: moderateScale(16),
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3,
      marginHorizontal: moderateScale(20),
    },
    subContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: moderateScale(14),
    },
    dragIcon: {
      paddingVertical: moderateScale(10),
    },
    contentContainer: {
      flex: 1,
    },
    uploadImage: {
      backgroundColor: colors.surface,
      paddingHorizontal: moderateScale(8),
      paddingVertical: moderateScale(6),
      borderRadius: moderateScale(6),
      marginRight: 'auto',
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(4),
      marginBottom: verticalScale(6),
    },
    uploadImageText: {
      color: colors.foreground,
      fontSize: moderateScale(12),
    },
    title: {
      fontWeight: '500',
      fontSize: moderateScale(14),
    },
    link: {
      color: colors.secondary,
      fontSize: moderateScale(14),
    },
    input: {
      height: verticalScale(25),
      paddingVertical: 0,
    },
    actionContainer: {
      alignItems: 'flex-end',
      gap: moderateScale(14),
    },
    toggleButton: {
      transform: [{ scale: 0.6 }],
      marginRight: moderateScale(-10),
    },
    previewImage: {
      width: moderateScale(25),
      height: moderateScale(25),
      borderRadius: moderateScale(8),
    },
    imageContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(10),
    },
  });
