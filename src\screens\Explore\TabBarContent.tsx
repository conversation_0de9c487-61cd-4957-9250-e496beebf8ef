import React, { useEffect } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';

import { OfflineView } from '../../components/OfflineView';
import { TabBarContentSkeleton } from '../../components/skeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import { fetchChannels } from '../../redux/slices/channelSlice';

import ChannelCard from './ChannelCard';

interface Props {
  id: number;
}

const TabBarContent: React.FC<Props> = ({ id }) => {
  const dispatch = useAppDispatch();
  const { isConnected } = useConnectionStatus();

  const { colors } = useAppSelector(state => state.theme);
  const styles = createStyles(colors);
  const { channels, status } = useAppSelector(state => state.channels) ?? {};

  useEffect(() => {
    dispatch(fetchChannels({ id }));
  }, [id]);
  if (!isConnected) return <OfflineView />;
  if (status === 'loading') return <TabBarContentSkeleton />;

  return (
    <View style={styles.container}>
      <FlatList
        data={channels[id.toString()]}
        keyExtractor={item => String(item.id)}
        showsVerticalScrollIndicator={false}
        numColumns={2}
        columnWrapperStyle={styles.row}
        contentContainerStyle={{
          marginTop: moderateScale(10),
        }}
        renderItem={({ item }) => (
          <View style={styles.cardContainer}>
            <ChannelCard
              categoryID={id}
              channelID={item.id}
              title={item.name}
              description={item.description}
              image={item.image}
              icon={item.icon}
              isLiked={item.is_liked}
              url={item.url}
            />
          </View>
        )}
      />
    </View>
  );
};

export default TabBarContent;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(6),
    },
    row: {
      justifyContent: 'space-between',
    },
    cardContainer: {
      flex: 1,
      marginHorizontal: moderateScale(8),
      marginBottom: moderateScale(12),
    },
  });
