import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { COMMON_ERROR } from '../../constants/common';
import { IUserData } from '../../screens/Profile/UpdateProfile';
import {
  createUserFeaturedCardsService,
  deleteUserFeaturedCardsService,
  getUserFeaturedCardsService,
  getUsersCountService,
  getVerifiedUsersService,
  updateUserData,
  updateUserFeaturedCardsService,
} from '../../services/api/userService';
import { Userdata, UsersCount } from '../../types/user';
import { appendImageToFormData } from '../../utils/media';

export interface FeaturedCardValues {
  card_id?: number;
  id?: number;
  user_id?: number;
  title: string;
  link: string;
  image: string;
  enabled: number;
  sort: number;
  isNew?: boolean;
}

interface UserState {
  featuredCards: FeaturedCardValues[];
  verifiedUsers: Userdata[];
  verifiedUsersStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  usersCount: UsersCount;
  updatingStatus?: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  verifiedUsersPage: number;
  verifiedUsersHasMore: boolean;
  loadingMoreVerifiedUsers: boolean;
}

const initialState: UserState = {
  featuredCards: [],
  verifiedUsers: [],
  verifiedUsersStatus: 'idle',
  usersCount: {
    total_users: 0,
    active_users: 0,
    verified_users: 0,
  },
  status: 'idle',
  error: null,
  verifiedUsersPage: 1,
  verifiedUsersHasMore: true,
  loadingMoreVerifiedUsers: false,
};

export const getVerifiedUsers = createAsyncThunk<
  { users: Userdata[]; page: number; hasMore: boolean },
  { limit: number; page: number },
  { state: { user: UserState } }
>('user/getVerifiedUsers', async ({ limit, page }) => {
  const users = await getVerifiedUsersService({ limit, page });
  const hasMore = users.length === limit;

  return { users, page, hasMore };
});

export const loadMoreVerifiedUsers = createAsyncThunk<
  { users: Userdata[]; page: number; hasMore: boolean },
  { limit: number; page: number },
  { state: { user: UserState } }
>('user/loadMoreVerifiedUsers', async ({ limit, page }) => {
  const users = await getVerifiedUsersService({ limit, page });
  const hasMore = users.length === limit;

  return { users, page, hasMore };
});

export const getUsersCount = createAsyncThunk(
  'user/getUsersCount',
  async () => {
    const usersCount = await getUsersCountService();

    return usersCount;
  },
);

export const getUserFeaturedCards = createAsyncThunk<
  FeaturedCardValues[],
  { user_id?: string }
>(
  'user/getUserFeaturedCards',

  async (payload, { rejectWithValue }) => {
    try {
      const response = await getUserFeaturedCardsService(payload);

      return response.sort((a, b) => a.sort - b.sort);
    } catch (error) {
      return rejectWithValue('Failed to fetch podcasts');
    }
  },
);

export const updateUserFeaturedCards = createAsyncThunk<
  boolean,
  FeaturedCardValues[]
>('user/updateUserFeaturedCards', (payload, { rejectWithValue }) => {
  try {
    payload.forEach(async (card, index) => {
      if (card.isNew) {
        delete card.isNew;
        await createUserFeaturedCardsService({
          ...card,
          sort: index + 1,
        });

        return;
      }
      await updateUserFeaturedCardsService({
        ...card,
        card_id: card.id,
        sort: index + 1,
      });
    });

    return true;
  } catch (error) {
    return rejectWithValue('Failed to update featured cards');
  }
});

export const updateUserProfile = createAsyncThunk<boolean, IUserData>(
  'user/updateUserProfile',
  async (payload, { rejectWithValue, dispatch }) => {
    try {
      const formData = new FormData();
      formData.append('about', payload.bio);
      formData.append('title', payload.profileTitle);
      appendImageToFormData(formData, 'avatar', payload.avatar);
      await updateUserData(formData, true);
      dispatch(updateUserFeaturedCards(payload.featuredCards));

      return true;
    } catch (error) {
      return rejectWithValue('Failed to update featured cards');
    }
  },
);

export const deleteUserFeaturedCards = createAsyncThunk<
  boolean,
  { card_id: number }
>(
  'user/deleteUserFeaturedCards',
  async (payload, { rejectWithValue, dispatch }) => {
    try {
      await deleteUserFeaturedCardsService(payload);
      dispatch(getUserFeaturedCards({}));

      return true;
    } catch (error) {
      return rejectWithValue('Failed to update featured cards');
    }
  },
);

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    resetVerifiedUsers: (state: UserState) => {
      state.verifiedUsers = [];
      state.verifiedUsersPage = 1;
      state.verifiedUsersHasMore = true;
      state.verifiedUsersStatus = 'idle';
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      // Get verified users
      .addCase(getVerifiedUsers.pending, (state: UserState) => {
        state.verifiedUsersStatus = 'loading';
      })
      .addCase(
        getVerifiedUsers.fulfilled,
        (
          state: UserState,
          action: PayloadAction<{
            users: Userdata[];
            page: number;
            hasMore: boolean;
          }>,
        ) => {
          if (action.payload.page === 1) {
            state.verifiedUsers = action.payload.users;
          } else {
            state.verifiedUsers = [
              ...state.verifiedUsers,
              ...action.payload.users,
            ];
          }
          state.verifiedUsersPage = action.payload.page;
          state.verifiedUsersHasMore = action.payload.hasMore;
          state.verifiedUsersStatus = 'succeeded';
        },
      )
      .addCase(getVerifiedUsers.rejected, (state: UserState, action) => {
        state.verifiedUsersStatus = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Load more verified users
      .addCase(loadMoreVerifiedUsers.pending, (state: UserState) => {
        state.loadingMoreVerifiedUsers = true;
      })
      .addCase(
        loadMoreVerifiedUsers.fulfilled,
        (
          state: UserState,
          action: PayloadAction<{
            users: Userdata[];
            page: number;
            hasMore: boolean;
          }>,
        ) => {
          if (action.payload.page === 1) {
            state.verifiedUsers = action.payload.users;
          } else {
            state.verifiedUsers = [
              ...state.verifiedUsers,
              ...action.payload.users,
            ];
          }
          state.verifiedUsersPage = action.payload.page;
          state.verifiedUsersHasMore = action.payload.hasMore;
          state.verifiedUsersStatus = 'succeeded';
          state.loadingMoreVerifiedUsers = false;
        },
      )
      .addCase(loadMoreVerifiedUsers.rejected, (state: UserState, action) => {
        state.loadingMoreVerifiedUsers = false;
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Get user featured cards
      .addCase(getUserFeaturedCards.pending, state => {
        state.status = 'loading';
      })
      .addCase(
        getUserFeaturedCards.fulfilled,
        (state, action: PayloadAction<FeaturedCardValues[]>) => {
          state.featuredCards = action.payload;
          state.status = 'succeeded';
        },
      )
      .addCase(getUserFeaturedCards.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Update user featured cards
      .addCase(updateUserFeaturedCards.fulfilled, state => {
        state.status = 'succeeded';
      })
      .addCase(updateUserFeaturedCards.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Update user profile
      .addCase(updateUserProfile.fulfilled, state => {
        state.updatingStatus = 'succeeded';
      })
      .addCase(updateUserProfile.pending, state => {
        state.updatingStatus = 'loading';
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.updatingStatus = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Get users count
      .addCase(getUsersCount.fulfilled, (state, action) => {
        state.usersCount = action.payload;
      });
  },
});

export const { resetVerifiedUsers } = userSlice.actions;
