import React, { useEffect, useMemo } from 'react';
import { Platform, Pressable, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { setIsToastVisible } from '../redux/slices/toastSlice';

import CustomText from './common/text';
import CustomModal from './Modal';

interface Props {
  mainTitle: string;
  btnTitle: string;
  onPress: () => void;
}

const Tooltip: React.FC<Props> = ({ mainTitle, btnTitle, onPress }) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const dispatch = useAppDispatch();
  const { isVisible } = useAppSelector(state => state.toast);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        dispatch(setIsToastVisible(false));
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isVisible, setIsToastVisible]);

  return (
    <CustomModal
      containerStyle={styles.modalStyles}
      isVisible={isVisible}
      setIsVisible={setIsToastVisible}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={500}
      animationOutTiming={500}
      backdropOpacity={0}
      hideModalContentWhileAnimating={true}>
      <View style={styles.container}>
        <CustomText style={styles.successText}>{mainTitle}</CustomText>
        <Pressable onPress={onPress}>
          <CustomText style={styles.btnText}>{btnTitle}</CustomText>
        </Pressable>
      </View>
    </CustomModal>
  );
};

export default Tooltip;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    modalStyles: {
      justifyContent: 'flex-end',
      margin: 0,
      marginBottom:
        Platform.OS === 'ios' ? verticalScale(80) : verticalScale(60),
      marginHorizontal: moderateScale(14),
    },
    container: {
      backgroundColor: colors.background,
      height: verticalScale(48),
      borderRadius: moderateScale(8),
      paddingVertical: verticalScale(8),
      paddingHorizontal: moderateScale(16),
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3,
    },
    successText: {
      color: colors.secondary,
      fontSize: moderateScale(12),
    },
    btnText: {
      color: colors.accent,
      fontSize: moderateScale(12),
      fontWeight: '500',
    },
  });
