import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useAppSelector } from '../../../hooks/redux';

const PollsSkeleton = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      {[...Array(3)].map((_, idx) => (
        <View key={idx} style={styles.card}>
          <SkeletonPlaceholder
            backgroundColor={colors.base}
            highlightColor={colors.highlightSurface}
          >
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                flexDirection="row"
                alignItems="center"
                marginBottom={verticalScale(8)}
              >
                <SkeletonPlaceholder.Item
                  width={moderateScale(32)}
                  height={moderateScale(32)}
                  borderRadius={moderateScale(16)}
                  marginRight={moderateScale(8)}
                />
                <SkeletonPlaceholder.Item
                  width={moderateScale(80)}
                  height={moderateScale(16)}
                  borderRadius={moderateScale(8)}
                />
              </SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                width={'80%'}
                height={moderateScale(18)}
                borderRadius={moderateScale(8)}
                marginBottom={verticalScale(10)}
              />
              <SkeletonPlaceholder.Item
                width={'100%'}
                height={moderateScale(16)}
                borderRadius={moderateScale(8)}
                marginBottom={verticalScale(8)}
              />
              <SkeletonPlaceholder.Item
                width={'100%'}
                height={moderateScale(16)}
                borderRadius={moderateScale(8)}
                marginBottom={verticalScale(8)}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder>
        </View>
      ))}
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      padding: moderateScale(8),
      backgroundColor: colors.background,
      flex: 1,
    },
    card: {
      backgroundColor: colors.surface,
      borderRadius: moderateScale(16),
      padding: moderateScale(12),
      marginBottom: verticalScale(16),
    },
  });

export default PollsSkeleton; 