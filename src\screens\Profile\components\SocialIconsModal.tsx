import React, { useEffect, useMemo } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Dimensions, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/FontAwesome6';

import CustomButton from '../../../components/common/button';
import Input from '../../../components/common/input';
import CustomText from '../../../components/common/text';
import CustomModal from '../../../components/Modal';
import ToggleButton from '../../../components/ToggleButton';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import { updateUserData } from '../../../services/api/userService';
import { convertSocialMediaData } from '../../../utils/profile';

interface Props {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
}

export type SocialMediaValues = {
  url: string;
  isVisible: boolean;
};
export interface ISocialIcons {
  instagram: SocialMediaValues;
  facebook: SocialMediaValues;
  twitter: SocialMediaValues;
  tiktok: SocialMediaValues;
  youtube: SocialMediaValues;
}

const INITIAL_VALUES: ISocialIcons = {
  instagram: { url: '', isVisible: false },
  facebook: { url: '', isVisible: false },
  twitter: { url: '', isVisible: false },
  tiktok: { url: '', isVisible: false },
  youtube: { url: '', isVisible: false },
};

const SOCIAL_MEDIA_FIELDS = [
  {
    name: 'instagram',
    placeholder: 'Instagram',
    icon: 'instagram',
  },
  {
    name: 'facebook',
    placeholder: 'Facebook',
    icon: 'facebook',
  },
  {
    name: 'twitter',
    placeholder: 'Twitter',
    icon: 'x-twitter',
  },
  { name: 'tiktok', placeholder: 'Tiktok', icon: 'tiktok' },
  { name: 'youtube', placeholder: 'Youtube', icon: 'youtube' },
];

const SocialIconsModal: React.FC<Props> = ({ isVisible, setIsVisible }) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { userDetails } = useAppSelector(state => state.auth);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<ISocialIcons>({
    mode: 'onSubmit',
    defaultValues: INITIAL_VALUES,
  });

  useEffect(() => {
    if (userDetails) {
      const socialMedia: ISocialIcons = {
        instagram: {
          url: userDetails.instagram,
          isVisible: userDetails.is_instagram === '1',
        },
        facebook: {
          url: userDetails.facebook,
          isVisible: userDetails.is_facebook === '1',
        },
        twitter: {
          url: userDetails.twitter,
          isVisible: userDetails.is_twitter === '1',
        },
        tiktok: {
          url: userDetails.tiktok,
          isVisible: userDetails.is_tiktok === '1',
        },
        youtube: {
          url: userDetails.youtube,
          isVisible: userDetails.is_youtube === '1',
        },
      };
      SOCIAL_MEDIA_FIELDS.forEach(field => {
        const value = socialMedia[field.name as keyof ISocialIcons];
        setValue(field.name as keyof ISocialIcons, value);
      });
    }
  }, [userDetails]);

  const onSubmit: SubmitHandler<ISocialIcons> = async data => {
    const payload = convertSocialMediaData(data);
    await updateUserData(payload);
    setIsVisible(false);
  };

  return (
    <CustomModal
      containerStyle={styles.modalStyles}
      isVisible={isVisible}
      setIsVisible={setIsVisible}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      hideModalContentWhileAnimating={true}>
      <View style={styles.container}>
        <CustomText style={styles.title}>Edit Social Links</CustomText>
        <CustomButton
          title="Save My Social Links"
          style={styles.button}
          onPress={handleSubmit(onSubmit)}
        />
        <View>
          {SOCIAL_MEDIA_FIELDS.map(field => (
            <Controller
              key={field.name}
              control={control}
              name={field.name as keyof ISocialIcons}
              render={({ field: { onChange, value, name } }) => (
                <View style={styles.fieldWrapper}>
                  <View style={styles.iconContainer}>
                    <Icon
                      name={field.icon}
                      size={
                        field.name === 'youtube'
                          ? moderateScale(28)
                          : moderateScale(32)
                      }
                      color={colors.secondary}
                    />
                  </View>
                  <View style={styles.inputField}>
                    <Input
                      inputContainerStyle={styles.inputContainer}
                      style={styles.input}
                      name={name}
                      value={value.url as keyof ISocialIcons}
                      placeholder={field.placeholder}
                      onChangeText={_value =>
                        onChange({ url: _value, isVisible: value.isVisible })
                      }
                      errors={errors}
                      autoCapitalize="none"
                    />
                  </View>
                  <ToggleButton
                    isEnabled={value.isVisible}
                    toggleSwitch={() =>
                      onChange({
                        url: value.url,
                        isVisible: !value.isVisible,
                      })
                    }
                  />
                </View>
              )}
            />
          ))}
        </View>
        <View>
          <CustomText style={styles.toggleText}>
            Click toggle to hide or display
          </CustomText>
        </View>
      </View>
    </CustomModal>
  );
};

export default SocialIconsModal;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    modalStyles: {
      justifyContent: 'flex-end',
      margin: 0,
    },
    container: {
      maxHeight: Dimensions.get('window').height * 0.8,
      backgroundColor: colors.background,
      padding: moderateScale(20),
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
    },
    title: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      fontWeight: '500',
      textAlign: 'center',
    },
    button: {
      marginVertical: verticalScale(16),
    },
    fieldWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: verticalScale(3),
      gap: moderateScale(8),
    },
    iconContainer: {
      width: moderateScale(35),
    },
    inputContainer: {
      marginVertical: verticalScale(4),
    },
    inputField: { flex: 1 },
    input: {
      paddingVertical: 0,
      height: verticalScale(30),
      paddingHorizontal: moderateScale(12),
    },
    toggleText: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      textAlign: 'center',
      marginVertical: verticalScale(20),
    },
  });
