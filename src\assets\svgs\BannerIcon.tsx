import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const BannerIcon: React.FC<SvgProps> = props => {
  return (
    <Svg width={22} height={22} fill="none" viewBox="0 0 24 24" {...props}>
      <Path
        stroke={props.stroke}
        strokeLinecap="round"
        strokeWidth={1.5}
        d="M9 6V5a3 3 0 1 1 6 0v1M9.17 15a3.001 3.001 0 0 0 5.66 0"
      />
      <Path
        stroke={props.stroke}
        strokeLinecap="round"
        strokeWidth={1.5}
        d="M20.224 12.526c-.586-3.121-.878-4.682-1.99-5.604C17.125 6 15.537 6 12.36 6h-.72c-3.176 0-4.764 0-5.875.922-1.11.922-1.403 2.483-1.989 5.604-.823 4.389-1.234 6.583-.034 8.029C4.942 22 7.174 22 11.639 22h.722c4.465 0 6.698 0 7.897-1.445.696-.84.85-1.93.696-3.555"
      />
    </Svg>
  );
};
