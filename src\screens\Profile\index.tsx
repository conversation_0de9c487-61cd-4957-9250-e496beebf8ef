import React, { useMemo } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import {
  DeleteIcon,
  LogoutIcon,
  MoonIcon,
  NotificationsIcon,
  PasswordIcon,
  SessionsIcon,
  SettingsIcon,
  SunIcon,
  UserIcon,
  VerifyIcon,
} from '../../assets/svgs';
import CustomText from '../../components/common/text';
import { OfflineView } from '../../components/OfflineView';
import { MANAGE_SESSIONS_TEXT } from '../../constants/manageSessions';
import { NOTIFICATION_TEXT } from '../../constants/notificationSettings';
import { ColorScheme, THEME_MODE } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import { APP_ROUTES } from '../../types/routes';

import PodcastBanner from './components/PodcastBanner';
import ProfileHeader from './components/ProfileHeader';
import SettingsItem from './components/SettingsItem';


const Profile = () => {
  const { isConnected } = useConnectionStatus();
  const { colors, theme } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { userDetails } = useAppSelector(state => state.auth);

  const settingsItems = useMemo(
    () => [
      {
        title: `Switch to ${theme === THEME_MODE.dark ? 'Light' : 'Dark'} Mode`,
        isToggle: true,
        icon: theme === THEME_MODE.dark ? <SunIcon /> : <MoonIcon />,
      },
      {
        title: 'Update My Profile',
        icon: <UserIcon />,
        route: APP_ROUTES.UPDATE_PROFILE,
      },
      {
        title: 'Verify My Profile',
        icon: <VerifyIcon />,
        isVerification: true,
      },
      {
        title: 'General Settings',
        icon: <SettingsIcon />,
        route: APP_ROUTES.GENERAL_SETTINGS,
      },
      {
        title: NOTIFICATION_TEXT.routeName,
        icon: <NotificationsIcon />,
        route: APP_ROUTES.NOTIFICATION_SETTINGS,
      },
      {
        title: 'Passwords',
        icon: <PasswordIcon />,
        route: APP_ROUTES.CHANGE_PASSWORD,
      },
      {
        title: MANAGE_SESSIONS_TEXT.routeName,
        icon: <SessionsIcon />,
        route: APP_ROUTES.MANAGE_SESSIONS,
      },
      {
        title: 'Delete Account',
        icon: <DeleteIcon stroke={colors.accent} />,
        route: APP_ROUTES.DELETE_ACCOUNT,
      },
      { title: 'Logout', icon: <LogoutIcon />, isLogout: true },
    ],
    [theme, colors],
  );

  if (!isConnected) return <OfflineView />;

  console.log('Profile Screen - userDetails:', userDetails);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ProfileHeader userDetails={userDetails} />
      <PodcastBanner />
      
      <CustomText style={styles.settings}>Settings</CustomText>
      <View style={{ marginVertical: verticalScale(20) }}>
        {settingsItems.map((item, index) => (
          <SettingsItem
            key={index}
            title={item.title}
            icon={item.icon}
            isToggle={item.isToggle}
            route={item.route}
            isLogout={item.isLogout}
            isVerification={item.isVerification}
          />
        ))}
      </View>
    </ScrollView>
  );
};

export default Profile;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: moderateScale(20),
    },
    settings: {
      color: colors.foreground,
      fontSize: moderateScale(18),
      fontWeight: '500',
    },
    separator: {
      borderBottomWidth: 0.2,
      borderColor: colors.secondary,
    },
  });
