import { FC, useMemo } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import { EPostType } from '../../../screens/Home/HomeFeed';
import { HighlightedEpisode, Podcast } from '../../../types/podcasts';
import CustomText from '../../common/text';

import { Card, CardProps } from './Card';

interface HighlightedPodcastsProps {
  data: HighlightedEpisode[] | Podcast[];
  type: EPostType;
}

export const HighlightedPodcasts: FC<HighlightedPodcastsProps> = ({
  data,
  type,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  if (!data?.length) {
    return null;
  }

  return (
    <View style={styles.container}>
      <CustomText style={styles.title}>
        HIGHLIGHTED{' '}
        {type === EPostType.PODCAST_CAROUSEL ? 'PODCAST' : 'EPISODES'}
      </CustomText>
      <View>
        <FlatList
          horizontal
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          data={(data as CardProps[]) || []}
          renderItem={({ item }) => <Card {...(item as CardProps)} />}
          initialNumToRender={8}
        />
      </View>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      gap: verticalScale(4),
    },
    title: {
      paddingHorizontal: moderateScale(14),
      color: colors.foreground,
      fontWeight: 'bold',
      fontSize: moderateScale(12),
    },
  });
