import * as React from 'react';
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const ComedyIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      clipPath="url(#a)">
      <Path d="M2.619.625a1.125 1.125 0 0 0-1.494 1.062V8.17a8.367 8.367 0 0 0 .756 3.675c1.228 2.519 3.613 4.35 6.724 5.521.255.096.536.096.79 0 3.112-1.168 5.496-3 6.724-5.521a8.365 8.365 0 0 0 .756-3.675V1.687A1.126 1.126 0 0 0 15.38.625a18.926 18.926 0 0 1-12.762 0Z" />
      <Path d="M5.625 9.844A3.244 3.244 0 0 0 9 12.938a3.244 3.244 0 0 0 3.375-3.094M6.75 6.469a1.406 1.406 0 0 0-2.813 0M14.063 6.469a1.406 1.406 0 0 0-2.813 0" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path d="M0 0h18v18H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
