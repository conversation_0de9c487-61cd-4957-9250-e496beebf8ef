import React, { useEffect, useMemo, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';

import ButtonLoader from '../../../components/ButtonLoader';
import CustomText from '../../../components/common/text';
import { AUTH_TAB } from '../../../constants/auth';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import {
  AUTH_ROUTES,
  AuthStackNavigatorParamList,
} from '../../../types/routes';

interface Props {
  isInputsFilled?: boolean;
  onPressLogin?: () => void;
  isLoading?: boolean;
}

const AuthTab: React.FC<Props> = ({
  isInputsFilled,
  onPressLogin,
  isLoading,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');

  const navigation =
    useNavigation<NavigationProp<AuthStackNavigatorParamList>>();

  const styles = useMemo(() => createStyles(colors), [colors]);

  const route = useRoute();

  useEffect(() => {
    setActiveTab(route.name === AUTH_ROUTES.SIGN_IN ? 'login' : 'signup');
  }, [route.name]);

  const handleLoginPress = () => {
    if (isInputsFilled) {
      onPressLogin?.();
    } else {
      navigation.navigate(AUTH_ROUTES.SIGN_IN);
    }
  };

  return (
    <View style={styles.container}>
      <Pressable
        style={[
          styles.tab,
          activeTab === 'login' ? styles.active : styles.inactive,
          isInputsFilled ? styles.loginBtn : {},
        ]}
        onPress={handleLoginPress}
        disabled={isLoading}>
        {isLoading ? (
          <View style={styles.loaderContainer}>
            <ButtonLoader />
          </View>
        ) : (
          <CustomText
            style={[
              styles.title,
              isInputsFilled ? { color: colors.white } : {},
            ]}>
            {AUTH_TAB.login}
          </CustomText>
        )}
      </Pressable>

      <Pressable
        style={[
          styles.tab,
          activeTab === 'signup' ? styles.active : styles.inactive,
        ]}
        onPress={() => {
          navigation.navigate(AUTH_ROUTES.SIGN_UP);
        }}>
        <CustomText style={styles.title}>{AUTH_TAB.signup}</CustomText>
      </Pressable>
    </View>
  );
};

export default AuthTab;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: moderateScale(30),
      padding: moderateScale(4),
      backgroundColor: colors.base,
    },
    tab: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: moderateScale(6),
      borderRadius: moderateScale(30),
      minHeight: moderateScale(28),
    },
    title: {
      fontSize: moderateScale(14),
      fontWeight: '500',
      color: colors.foreground,
      textAlign: 'center',
    },
    inactive: {
      backgroundColor: colors.base,
    },
    active: {
      backgroundColor: colors.background,
    },
    loginBtn: {
      backgroundColor: colors.accent,
    },
    loaderContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
    },
  });
