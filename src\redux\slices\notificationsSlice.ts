import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { COMMON_ERROR } from '../../constants/common';
import {
  actionNotificationService,
  getNotificationsService,
} from '../../services/api/notificationsService';
import { Notifications } from '../../types/notifications';

interface NotificationsState {
  notifications: Notifications[];
  unreadNotificationsCount: number;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  updatingStatus?: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: NotificationsState = {
  notifications: [],
  unreadNotificationsCount: 0,
  status: 'idle',
  error: null,
};

export const getNotifications = createAsyncThunk(
  'notifications/getNotifications',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getNotificationsService();

      return response;
    } catch (error) {
      return rejectWithValue('Failed to fetch notifications');
    }
  },
);

export const deleteNotification = createAsyncThunk<void, string>(
  'notifications/deleteNotification',
  async (id, { rejectWithValue, dispatch, getState }) => {
    try {
      await actionNotificationService({ id, type: 'delete' });

      const { notifications } = (
        getState() as { notifications: NotificationsState }
      ).notifications;
      const updatedNotifications = notifications.filter(
        (item: Notifications) => item.id !== id,
      );
      dispatch(
        notificationsSlice.actions.setNotifications(updatedNotifications),
      );
    } catch (error) {
      return rejectWithValue('Failed to delete notification');
    }
  },
);

export const seenNotification = createAsyncThunk<void, string>(
  'notifications/deleteNotification',
  async (ids, { rejectWithValue, dispatch }) => {
    try {
      await actionNotificationService({ ids, type: 'seen' });
      dispatch(getNotifications());
    } catch (error) {
      return rejectWithValue('Failed to delete notification');
    }
  },
);

export const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setNotifications(state, action: PayloadAction<Notifications[]>) {
      state.notifications = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(getNotifications.pending, state => {
        state.status = 'loading';
      })
      .addCase(
        getNotifications.fulfilled,
        (state, action: PayloadAction<Notifications[]>) => {
          state.notifications = action.payload;
          state.unreadNotificationsCount = action.payload?.filter(
            notification => Number(notification.seen) <= 0,
          ).length;
          state.status = 'succeeded';
        },
      )
      .addCase(getNotifications.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      });
  },
});

export const {} = notificationsSlice.actions;
