import React, { useMemo } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { verticalScale } from 'react-native-size-matters';

import { OfflineView } from '../../components/OfflineView';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import FavoriteCard from '../Saved/FavoriteCard';

const ITEMS_PER_ROW = 5;

const FavoriteChannels = () => {
  const { isConnected } = useConnectionStatus();
  const { likedChannels } = useAppSelector(state => state.channels) ?? {};
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  if (!isConnected) return <OfflineView />;

  return (
    <View style={styles.container}>
      <FlatList
        data={likedChannels}
        numColumns={ITEMS_PER_ROW}
        keyExtractor={item => item.id.toString()}
        renderItem={({ item }) => (
          <FavoriteCard {...item} ITEMS_PER_ROW={ITEMS_PER_ROW} />
        )}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
      />
    </View>
  );
};

export default FavoriteChannels;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    contentContainerStyle: {
      flexGrow: 1,
      rowGap: verticalScale(12),
      marginHorizontal: 'auto',
      paddingTop: verticalScale(16),
    },
  });
