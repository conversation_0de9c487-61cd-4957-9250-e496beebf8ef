import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { COMMON_ERROR } from '../../constants/common';
import {
  fetchRecentSearchesService,
  removeRecentSearchService,
  searchQueryService,
} from '../../services/api/searchService';
import { PlaylistVideos, RecentSearch } from '../../types/playlist';
import { Publisher } from '../../types/posts';

export enum ESearchType {
  PLAYLIST = 'playlist',
  PUBLISHER = 'publisher',
  GROUPS = 'groups',
  CHANNELS = 'channels',
}

interface searchState {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  results: PlaylistVideos[] | Publisher[] | any[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  recentSearches: RecentSearch[];
  error: string | null;
}

const initialState: searchState = {
  results: [],
  status: 'idle',
  recentSearches: [],
  error: null,
};

export const searchQuery = createAsyncThunk<
  PlaylistVideos[] | Publisher[],
  string
>('search/searchQuery', async (query, { rejectWithValue }) => {
  try {
    const results = await searchQueryService({ search_key: query });
    const users = results?.users.map(user => ({
      ...user,
      type: ESearchType.PUBLISHER,
    }));
    const pages = results?.pages.map(page => ({
      ...page,
      type: ESearchType.PLAYLIST,
    }));
    const groups = results?.groups.map(group => ({
      ...group,
      type: ESearchType.GROUPS,
    }));
    const channels = results?.channels.map(group => ({
      ...group,
      type: ESearchType.CHANNELS,
    }));
    const playlists = results?.playlists.map(group => ({
      ...group,
      type: ESearchType.PLAYLIST,
    }));

    return [...users, ...pages, ...groups, ...channels, ...playlists].map(
      item => {
        return {
          ...item,
          key: Math.random(),
        };
      },
    );
  } catch (error) {
    return rejectWithValue('Failed to fetch search results');
  }
});

export const fetchRecentSearches = createAsyncThunk<RecentSearch[]>(
  'search/fetchRecentSearches',
  async (_, { rejectWithValue }) => {
    try {
      const recentSearches = await fetchRecentSearchesService();

      return recentSearches.map(item => ({
        ...item,
        key: Math.random(),
      }));
    } catch (error) {
      return rejectWithValue('Failed to fetch search results');
    }
  },
);

export const removeRecentSearch = createAsyncThunk<RecentSearch[], number>(
  'search/removeRecentSearch',
  (id, { rejectWithValue, getState }) => {
    try {
      const isRemoved = removeRecentSearchService(String(id));
      if (!isRemoved) {
        return rejectWithValue('Failed to remove search');
      }
      const state = getState() as { search: searchState };
      const recentSearches = state.search.recentSearches.filter(
        item => item.id !== id,
      );

      return recentSearches;
    } catch (error) {
      return rejectWithValue('Failed to remove search');
    }
  },
);

export const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    clearSearchResults: state => {
      state.results = [];
    },
    clearRecentSearches: state => {
      state.results = [];
    },
  },
  extraReducers: builder => {
    builder

      // Handle search query results
      .addCase(searchQuery.pending, state => {
        state.status = 'loading';
      })
      .addCase(searchQuery.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.results = action.payload;
      })
      .addCase(searchQuery.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Handle recent searches
      .addCase(fetchRecentSearches.pending, state => {
        state.status = 'loading';
      })
      .addCase(fetchRecentSearches.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.recentSearches = action.payload;
      })
      .addCase(fetchRecentSearches.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? COMMON_ERROR;
      })

      // Handle remove recent search
      .addCase(removeRecentSearch.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.recentSearches = action.payload;
      });
  },
});

export const { clearRecentSearches, clearSearchResults } = searchSlice.actions;
