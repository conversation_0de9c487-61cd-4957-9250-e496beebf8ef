import { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/FontAwesome6';

import CustomText from '../../components/common/text';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const MembersCounts = () => {
  const { usersCount } = useAppSelector(state => state.user);
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  return (
    <View style={styles.container}>
      <Icon
        name="user-large"
        size={moderateScale(10)}
        color={colors.foreground}
      />
      <CustomText style={styles.text}>{usersCount.total_users}</CustomText>
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: moderateScale(5),
      alignSelf: 'flex-end',
      paddingBottom: verticalScale(5),
    },
    text: {
      color: colors.foreground,
      fontSize: moderateScale(10),
    },
  });
