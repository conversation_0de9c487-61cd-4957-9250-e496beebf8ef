import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { COMMON_ERROR } from '../../constants/common';
import { Poll, CreatePollRequest, VoteRequest, PollsResponse } from '../../types/polls';
import {
  fetchPollsService,
  createPollService,
  voteOnPollService,
  deletePollService,
  updatePollService,
} from '../../services/api/pollsService';
import Toast from 'react-native-toast-message';
import { TOAST_TITLE, TOAST_TYPE } from '../../constants/toast';

// Utility function to check if a poll is expired
export function isPollExpired(poll: Poll): boolean {
  const created = new Date(poll.createdAt);
  const now = new Date();
  const expires = new Date(created.getTime() + poll.duration * 24 * 60 * 60 * 1000);
  return now >= expires;
}

interface PollsState {
  polls: Poll[];
  userPolls: Poll[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  userPollsStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  createPollStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  voteStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  hasMore: boolean;
  nextPage: number;
}

const initialState: PollsState = {
  polls: [],
  userPolls: [],
  status: 'idle',
  userPollsStatus: 'idle',
  createPollStatus: 'idle',
  voteStatus: 'idle',
  error: null,
  hasMore: true,
  nextPage: 1,
};

// Fetch polls using the actual API
export const fetchPolls = createAsyncThunk<
  { polls: Poll[]; hasMore: boolean },
  { action?: string; page?: number; user_id?: string; refresh?: boolean }
>('polls/fetchPolls', async (payload, { rejectWithValue, getState }) => {
  try {
    const state = getState() as any;
    const { nextPage } = state.polls;
    
    const polls = await fetchPollsService({
      action: payload.action || 'all',
      page: payload.refresh ? 1 : (payload.page || nextPage || 1),
      user_id: payload.user_id,
    });
    
    // Determine if there are more polls based on the returned count
    // Assuming each page returns up to 10 polls, adjust this based on your API
    const hasMore = polls.length >= 10;
    
    return {
      polls,
      hasMore,
    };
  } catch (error) {
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch polls');
  }
});

// Create poll using the actual API
export const createPoll = createAsyncThunk<
  Poll,
  CreatePollRequest
>('polls/createPoll', async (payload, { rejectWithValue }) => {
  try {
    const poll = await createPollService(payload);
    
    if (!poll) {
      // Service already showed the error toast with proper API error message
      return rejectWithValue('Poll creation failed');
    }
    
    return poll;
  } catch (error) {
    return rejectWithValue(error instanceof Error ? error.message : 'Unknown error occurred');
  }
});

// Vote on poll using the actual API
export const voteOnPoll = createAsyncThunk<
  Poll,
  VoteRequest
>('polls/voteOnPoll', async (payload, { rejectWithValue }) => {
  try {
    const poll = await voteOnPollService(payload);
    
    if (!poll) {
      throw new Error('Failed to vote on poll');
    }
    
    return poll;
  } catch (error) {
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to vote on poll');
  }
});

// Delete poll using the actual API
export const deletePoll = createAsyncThunk<
  string,
  string
>('polls/deletePoll', async (pollId, { rejectWithValue }) => {
  try {
    const success = await deletePollService(pollId);
    
    if (!success) {
      throw new Error('Failed to delete poll');
    }
    
    return pollId;
  } catch (error) {
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete poll');
  }
});

// Update poll using the actual API
export const updatePoll = createAsyncThunk<
  Poll,
  { id: string; question: string; options: string[]; duration: number }
>('polls/updatePoll', async (payload, { rejectWithValue }) => {
  try {
    const poll = await updatePollService(payload);
    
    if (!poll) {
      throw new Error('Failed to update poll');
    }
    
    return poll;
  } catch (error) {
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to update poll');
  }
});

export const pollsSlice = createSlice({
  name: 'polls',
  initialState,
  reducers: {
    resetPollsState: (state) => {
      state.polls = [];
      state.userPolls = [];
      state.status = 'idle';
      state.userPollsStatus = 'idle';
      state.createPollStatus = 'idle';
      state.voteStatus = 'idle';
      state.error = null;
      state.hasMore = true;
      state.nextPage = 1;
    },
    clearPollsError: (state) => {
      state.error = null;
    },
    clearCreatePollStatus: (state) => {
      state.createPollStatus = 'idle';
    },
  },
  extraReducers: builder => {
    builder
      // Fetch polls
      .addCase(fetchPolls.pending, (state, action) => {
        const isUserPolls = action.meta.arg.action === 'user_polls';
        if (isUserPolls) {
          state.userPollsStatus = 'loading';
        } else {
          state.status = 'loading';
        }
        state.error = null;
      })
      .addCase(fetchPolls.fulfilled, (state, action) => {
        const isUserPolls = action.meta.arg.action === 'user_polls';

        if (isUserPolls) {
          state.userPollsStatus = 'succeeded';
          state.userPolls = action.payload.polls;
        } else {
          state.status = 'succeeded';
          // If it's a refresh, replace the polls
          if (action.meta.arg.refresh) {
            state.polls = action.payload.polls;
            state.nextPage = 2; // Next page after refresh
          } else {
            // Otherwise, append to existing polls
            state.polls = [...state.polls, ...action.payload.polls];
            state.nextPage = state.nextPage + 1; // Increment page for next load
          }
          state.hasMore = action.payload.hasMore;
        }

        state.error = null;
      })
      .addCase(fetchPolls.rejected, (state, action) => {
        const isUserPolls = action.meta.arg.action === 'user_polls';
        if (isUserPolls) {
          state.userPollsStatus = 'failed';
        } else {
          state.status = 'failed';
        }
        state.error = action.payload as string || COMMON_ERROR;
      })
      
      // Create poll
      .addCase(createPoll.pending, (state) => {
        state.createPollStatus = 'loading';
        state.error = null;
      })
      .addCase(createPoll.fulfilled, (state, action) => {
        state.createPollStatus = 'succeeded';
        state.error = null;
        // Add new poll to the beginning of the list
        state.polls.unshift(action.payload);
        // Show success toast
        Toast.show({
          type: TOAST_TYPE.SUCCESS,
          text1: TOAST_TITLE.POLL_CREATED,
        });
      })
      .addCase(createPoll.rejected, (state, action) => {
        state.createPollStatus = 'failed';
        state.error = action.payload as string || COMMON_ERROR;
        // Toast removed - API service already shows proper error message
      })
      
      // Vote on poll
      .addCase(voteOnPoll.pending, (state) => {
        state.voteStatus = 'loading';
        state.error = null;
      })
      .addCase(voteOnPoll.fulfilled, (state, action) => {
        state.voteStatus = 'succeeded';
        state.error = null;
        // Vote successful - we'll refresh polls to get updated data
        // The minimal poll object just confirms the vote went through
        
        Toast.show({
          type: TOAST_TYPE.SUCCESS,
          text1: TOAST_TITLE.VOTE_RECORDED,
        });
      })
      .addCase(voteOnPoll.rejected, (state, action) => {
        state.voteStatus = 'failed';
        state.error = action.payload as string || COMMON_ERROR;
        // Toast removed - API service already shows proper error message
      })
      
      // Delete poll
      .addCase(deletePoll.pending, (state) => {
        //state.status = 'loading';
        state.error = null;
      })
      .addCase(deletePoll.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.error = null;
        // Remove the poll from both lists
        state.polls = state.polls.filter(poll => poll.id !== action.payload);
        state.userPolls = state.userPolls.filter(poll => poll.id !== action.payload);
        // Show success toast
        Toast.show({
          type: TOAST_TYPE.SUCCESS,
          text1: TOAST_TITLE.POLL_DELETED,
        });
      })
      .addCase(deletePoll.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string || COMMON_ERROR;
        // Toast removed - API service already shows proper error message
      })

      // Update poll
      // .addCase(updatePoll.pending, (state) => {
      //   state.createPollStatus = 'loading';
      //   state.error = null;
      // })
      // .addCase(updatePoll.fulfilled, (state, action) => {
      //   state.createPollStatus = 'succeeded';
      //   const idx = state.polls.findIndex(p => p.id === action.payload.id);
      //   if (idx !== -1) {
      //     state.polls[idx] = action.payload;
      //   }
      //   // Show success toast
      //   Toast.show({
      //     type: TOAST_TYPE.SUCCESS,
      //     text1: TOAST_TITLE.POLL_UPDATED,
      //   });
      // })
      // .addCase(updatePoll.rejected, (state, action) => {
      //   state.createPollStatus = 'failed';
      //   state.error = action.payload as string || COMMON_ERROR;
      // });
  },
});

export const { resetPollsState, clearPollsError, clearCreatePollStatus } = pollsSlice.actions;
export default pollsSlice.reducer; 