import React, { useMemo } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { NavigationProp, useNavigation } from '@react-navigation/native';

import { DeleteAccountImage } from '../../assets/svgs';
import CustomButton from '../../components/common/button';
import CustomText from '../../components/common/text';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import useTypeSafeNavigation from '../../hooks/useTypeSafeNavigation';
import { clearUser } from '../../redux/slices/authSlice';
import { deleteUserAccount } from '../../services/api/userService';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '../../types/routes';
import { clearAS } from '../../utils/storage';

const DeleteAccount = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const navigation = useTypeSafeNavigation();
  const navigationAuth =
    useNavigation<NavigationProp<AuthStackNavigatorParamList>>();
  const dispatch = useAppDispatch();

  const deleteAccount = async (password: string) => {
    const response = await deleteUserAccount({ password });
    if (response) {
      clearAS();
      dispatch(clearUser());
      navigationAuth.reset({
        index: 0,
        routes: [{ name: AUTH_ROUTES.SIGN_IN }],
      });
    }
  };

  const handleDeleteAccount = () => {
    Alert.prompt(
      'Are you sure you want to delete your account?',
      'Please enter your password to confirm deletion.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: password => {
            if (password) deleteAccount(password);
          },
        },
      ],
      'secure-text',
    );
  };

  const goBack = () => navigation.goBack();

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}>
      <DeleteAccountImage />
      <CustomText style={styles.mainText}>
        Are you sure you want to want to delete your account, and leave our
        network?
      </CustomText>
      <View style={styles.btnWrapper}>
        <CustomButton
          style={styles.button}
          variant="secondary"
          title="No, I’ll Think"
          onPress={goBack}
        />
        <CustomButton
          style={styles.button}
          title="Delete Account"
          onPress={handleDeleteAccount}
        />
      </View>
    </ScrollView>
  );
};

export default DeleteAccount;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: moderateScale(20),
      paddingTop: verticalScale(50),
    },
    contentContainer: { alignItems: 'center' },
    mainText: {
      color: colors.foreground,
      fontWeight: '500',
      fontSize: moderateScale(18),
      textAlign: 'center',
      paddingHorizontal: moderateScale(20),
    },
    btnWrapper: {
      flexDirection: 'row',
      gap: '10%',
      marginTop: verticalScale(30),
    },
    button: {
      width: '49%',
    },
  });
