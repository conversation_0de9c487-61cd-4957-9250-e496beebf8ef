import React, { FC, Fragment, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import { ColorScheme } from '../../constants/theme/colors';
import { useAppSelector } from '../../hooks/redux';

export const ChartSkeleton: FC = () => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const data = Array.from({ length: 9 }).map((_, index) => index);

  return (
    <View style={styles.container}>
      {data.map(item => (
        <Fragment key={item}>
          <SkeletonPlaceholder
            backgroundColor={colors.surface}
            highlightColor={colors.highlightSurface}>
            <SkeletonPlaceholder.Item
              flexDirection="row"
              justifyContent="space-between"
              marginVertical={verticalScale(10)}>
              <SkeletonPlaceholder.Item
                height={moderateScale(50)}
                width={moderateScale(50)}
                borderRadius={moderateScale(5)}
              />
              <View>
                <SkeletonPlaceholder.Item
                  height={moderateScale(10)}
                  width={moderateScale(5)}
                  borderRadius={moderateScale(5)}
                />
              </View>
              <View style={styles.subContainer}>
                <SkeletonPlaceholder.Item
                  height={verticalScale(10)}
                  width={moderateScale(190)}
                  borderRadius={moderateScale(5)}
                />
                <SkeletonPlaceholder.Item
                  height={verticalScale(6)}
                  width={moderateScale(170)}
                  borderRadius={moderateScale(5)}
                  marginTop={verticalScale(5)}
                />
                <SkeletonPlaceholder.Item
                  height={verticalScale(6)}
                  width={moderateScale(140)}
                  borderRadius={moderateScale(5)}
                  marginTop={verticalScale(5)}
                />
              </View>
              <View style={styles.subContainer}>
                <SkeletonPlaceholder.Item
                  height={moderateScale(22)}
                  width={moderateScale(55)}
                  borderRadius={moderateScale(50)}
                />
              </View>
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder>
          <SkeletonPlaceholder
            backgroundColor={colors.surface}
            highlightColor={colors.highlightSurface}>
            <SkeletonPlaceholder.Item
              flexDirection="row"
              justifyContent="space-between"
              height={verticalScale(1)}
            />
          </SkeletonPlaceholder>
        </Fragment>
      ))}
    </View>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(20),
    },
    subContainer: {
      justifyContent: 'center',
    },
  });
