import { useCallback, useEffect, useMemo } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
} from 'react-native';
import { verticalScale } from 'react-native-size-matters';

import CustomText from '../../components/common/text';
import { HighlightedPodcasts } from '../../components/highlighted';
import { OfflineView } from '../../components/OfflineView';
import { PostCard } from '../../components/posts/card';
import { HomeFeedSkeleton } from '../../components/skeleton';
import { ColorScheme } from '../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import {
  fetchHighlightedEpisode,
  fetchPodcasts,
  fetchPosts,
  loadMorePosts,
  setPostsRefreshStatus,
} from '../../redux/slices/postsSlice';
import {
  EpisodeCarouselType,
  HighlightedEpisode,
  Podcast,
} from '../../types/podcasts';
import { Post } from '../../types/posts';

export enum EPostType {
  POST = 'post',
  PODCAST_CAROUSEL = 'podcastCarousel',
  HIGHLIGHTED_EPISODES = 'highlightedEpisodes',
}

interface IPostsWithPodcasts extends Post {
  type?: EPostType;
  list: Podcast[] | HighlightedEpisode[];
}

const viewabilityConfig = {
  itemVisiblePercentThreshold: 50,
};

const HomeFeed = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useConnectionStatus();
  const {
    hasMore,
    after_post_id,
    posts,
    podcasts,
    highlightedEpisodes,
    postStatus,
    highEpisodesStatus,
    podcastsStatus,
    postRefreshStatus,
    isLoadMore,
  } = useAppSelector(state => state.posts);
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  useEffect(() => {
    dispatch(fetchPosts({ type: 'get_news_feed', limit: 10 }));
    dispatch(fetchPodcasts({ type: 'highlighted' }));
    dispatch(
      fetchHighlightedEpisode({
        slider_id: '1',
        type: EpisodeCarouselType.carousel1,
      }),
    );
    dispatch(
      fetchHighlightedEpisode({
        slider_id: '2',
        type: EpisodeCarouselType.carousel2,
      }),
    );
    dispatch(
      fetchHighlightedEpisode({
        slider_id: '3',
        type: EpisodeCarouselType.carousel3,
      }),
    );
  }, [dispatch]);

  const postsWithPodcasts = useMemo(() => {
    return [
      ...posts.slice(0, 3),
      {
        id: 'episodesCarousel1',
        type: EPostType.HIGHLIGHTED_EPISODES,
        list: highlightedEpisodes[
          EpisodeCarouselType.carousel1
        ].episodes?.slice(0, 6),
      },
      {
        id: 'episodesCarousel12',
        type: EPostType.HIGHLIGHTED_EPISODES,
        list: highlightedEpisodes[
          EpisodeCarouselType.carousel2
        ].episodes?.slice(0, 6),
      },
      {
        id: 'episodesCarousel13',
        type: EPostType.HIGHLIGHTED_EPISODES,
        list: highlightedEpisodes[
          EpisodeCarouselType.carousel3
        ].episodes?.slice(0, 6),
      },

      ...posts.slice(3, 6),
      {
        id: 'podcastCarousel1',
        type: EPostType.PODCAST_CAROUSEL,
        list: podcasts,
      },
      ...posts.slice(6),
    ];
  }, [posts, podcasts, highlightedEpisodes]) as IPostsWithPodcasts[];

  const handleLoadMorePosts = useCallback(() => {
    if (hasMore && !isLoadMore) {
      dispatch(
        loadMorePosts({ type: 'get_news_feed', after_post_id, limit: 5 }),
      );
    }
  }, [hasMore, isLoadMore, after_post_id, dispatch]);

  const renderFooter = useCallback(() => {
    if (isLoadMore && hasMore) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color={colors.secondary} />
        </View>
      );
    }
  }, [hasMore, isLoadMore]);

  const handleRefresh = useCallback(async () => {
    dispatch(setPostsRefreshStatus(true));
    await dispatch(fetchPosts({ type: 'get_news_feed', limit: 10 }));
    dispatch(setPostsRefreshStatus(false));
  }, [dispatch]);

  if (
    (postStatus === 'loading' ||
      highEpisodesStatus === 'loading' ||
      podcastsStatus === 'loading') &&
    !postRefreshStatus
  ) {
    return <HomeFeedSkeleton />;
  }

  if (!isConnected) return <OfflineView />;

  if (postStatus === 'failed')
    return <CustomText>Error loading posts.</CustomText>;

  return (
    <View style={styles.container}>
      <FlatList
        data={postsWithPodcasts}
        keyExtractor={item => item.id}
        viewabilityConfig={viewabilityConfig}
        onEndReached={handleLoadMorePosts}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={postRefreshStatus}
            onRefresh={handleRefresh}
            tintColor={colors.secondary}
          />
        }
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        renderItem={({ item }) => {
          if (
            item?.type === EPostType.PODCAST_CAROUSEL ||
            item?.type === EPostType.HIGHLIGHTED_EPISODES
          ) {
            return <HighlightedPodcasts data={item.list} type={item.type} />;
          }

          return <PostCard {...item} />;
        }}
      />
    </View>
  );
};

export default HomeFeed;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    footerContainer: {
      padding: 10,
    },
    separator: {
      height: verticalScale(4),
      marginVertical: verticalScale(7),
      backgroundColor: colors.separator,
    },
  });
