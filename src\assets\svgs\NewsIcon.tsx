import * as React from 'react';
import Svg, { ClipP<PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const NewsIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.25}
      clipPath="url(#a)">
      <Path d="M2.75 7.313a6.75 6.75 0 1 0 13.5 0 6.75 6.75 0 0 0-13.5 0ZM1.063 17.438h16.875M9.5 14.063v3.374" />
      <Path d="M9.5.563a8.891 8.891 0 0 0-2.813 6.75 8.891 8.891 0 0 0 2.813 6.75M9.5.563a8.89 8.89 0 0 1 2.813 6.75 8.89 8.89 0 0 1-2.813 6.75M3.135 5.063h12.728M3.135 9.563h12.73" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path d="M.5 0h18v18H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
