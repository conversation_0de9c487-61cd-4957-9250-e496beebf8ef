import { StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import { ColorScheme } from '../../constants/theme/colors';

export const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: moderateScale(20),
    },
    heading: {
      color: colors.foreground,
      fontSize: moderateScale(16),
      marginBottom: verticalScale(17),
    },
    buttonWrapper: {
      marginTop: moderateScale(23),
    },
    checkbox: { marginBottom: moderateScale(17) },
  });
