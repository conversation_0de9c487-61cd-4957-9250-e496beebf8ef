import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const SessionsIcon = (props: SvgProps) => (
  <Svg width={16} height={14} viewBox="0 0 16 14" fill="none" {...props}>
    <Path
      d="M3.5 3.62515V3.40015C3.5 2.56007 3.5 2.13972 3.66349 1.81885C3.8073 1.5366 4.0366 1.3073 4.31885 1.16349C4.63972 1 5.06007 1 5.90015 1H12.3501C13.1902 1 13.6097 1 13.9305 1.16349C14.2128 1.3073 14.4429 1.5366 14.5867 1.81885C14.75 2.1394 14.75 2.55925 14.75 3.39768V8.35232C14.75 9.19075 14.75 9.60999 14.5867 9.93055C14.4429 10.2128 14.2129 10.4429 13.9307 10.5867C13.6101 10.75 13.1908 10.75 12.3523 10.75H6.875M1.25 10.6001V6.40015C1.25 5.56007 1.25 5.13972 1.41349 4.81885C1.5573 4.5366 1.7866 4.3073 2.06885 4.16349C2.38972 4 2.81007 4 3.65015 4H4.10015C4.94023 4 5.35968 4 5.68055 4.16349C5.96279 4.3073 6.19286 4.5366 6.33667 4.81885C6.5 5.1394 6.5 5.55925 6.5 6.39768V10.6023C6.5 11.4407 6.5 11.86 6.33667 12.1805C6.19286 12.4628 5.96279 12.6929 5.68055 12.8367C5.35999 13 4.94075 13 4.10232 13H3.64768C2.80925 13 2.3894 13 2.06885 12.8367C1.7866 12.6929 1.5573 12.4628 1.41349 12.1805C1.25 11.8597 1.25 11.4402 1.25 10.6001Z"
      stroke="#C41208"
      strokeWidth={1.25}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
