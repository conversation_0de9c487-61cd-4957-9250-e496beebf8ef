import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, SvgProps } from 'react-native-svg';

export const LikeSymbolIcon: React.FC<SvgProps> = ({ ...props }) => {
  return (
    <Svg id="Layer_1" x="0px" y="0px" viewBox="0 0 640 640" {...props}>
      <G>
        <Path d="M536.8,416c10.1,31,3,57.5-20.7,79.8c1.4,10.5,0.6,21-2.8,31.2c-6.3,18.8-19.3,31.9-36.5,40.9 c-18.5,9.8-38.6,14.3-59.3,16.2c-20,1.9-40,1.2-60,0.2c-19.6-0.9-39.3-0.5-58.9-1.1c-31.8-0.9-63.4-3.5-94.9-7.4 c-8.6-1.1-17.2-2-26.3-3c-9.4,6.5-19.9,8.5-31.2,8.4c-20.9-0.2-41.8,0.1-62.8-0.1c-23.4-0.2-41.8-15.9-45.7-38.8 c-0.5-3-0.5-6.1-0.5-9.1c0-58.8,0-117.5,0-176.3c0-24.6,14.9-42.6,39-47c3-0.5,6.1-0.5,9.1-0.5c19.8,0,39.6,0.1,59.3-0.1 c9.7-0.1,19,0.8,27.9,5.5c1.4-2.8,2.7-5.4,4.1-7.9c6-10.7,13.2-20.4,22.5-28.8c14.4-12.9,24.7-29.1,34.3-45.7 c20.6-35.4,37.2-72.7,51.9-110.9c0.8-2.2,0.7-5,0.5-7.5c-0.9-11.6-0.8-23.2,2.8-34.4c5.5-17.5,17.7-25,35.9-22.8 c24.3,2.9,43.2,14.1,54.5,36.2c7.9,15.4,9.2,32.2,7.8,49.2c-1.5,18.7-6.5,36.5-12.2,54.3c-5.6,17.5-10.9,35-12.2,53.5 c-0.1,1.1-0.1,2.3-0.1,3.4c0,0.5,0.2,1,0.3,1.9c6.4-1.3,12.7-2.5,19-3.8c23.3-4.8,46.9-7.2,70.7-6.9c18.6,0.3,36.9,2.5,54,10.6 c30.3,14.3,42.7,42.6,32.6,74.5c-0.5,1.5-0.9,2.9-1.3,4.4C557.1,361.7,557.1,389,536.8,416z" />
      </G>
    </Svg>
  );
};
