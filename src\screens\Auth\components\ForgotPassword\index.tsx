import React, { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { View } from 'react-native';

import CustomButton from '../../../../components/common/button';
import CustomText from '../../../../components/common/text';
import CustomModal from '../../../../components/Modal';
import { FORGOT_PASSWORD } from '../../../../constants/auth';
import { useAppSelector } from '../../../../hooks/redux';
import {
  forgotPassword,
  setNewPassword,
} from '../../../../services/authServervice';

import StepContent from './Stepper';
import createStyles from './styles';

interface Props {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
}

export interface FormValues {
  username: string;
  verificationCode: string;
  newPassword: string;
  confirmPassword: string;
}

const ForgetPasswordModalStepper: React.FC<Props> = ({
  isVisible,
  setIsVisible,
}) => {
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [step, setStep] = useState(1);

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
    reset,
  } = useForm<FormValues>({
    mode: 'onSubmit',
    defaultValues: {
      username: '',
      verificationCode: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const { buttonTitle, description, title } = useMemo(() => {
    const dataByStep: {
      [key: number]: {
        title: string;
        description: string;
        buttonTitle: string;
      };
    } = {
      1: {
        title: FORGOT_PASSWORD.forgotPassword,
        description: FORGOT_PASSWORD.forgotDescription,
        buttonTitle: 'Send Email',
      },
      2: {
        title: FORGOT_PASSWORD.forgotPassword,
        description: FORGOT_PASSWORD.codeDescription,
        buttonTitle: 'Continue',
      },
      3: {
        title: FORGOT_PASSWORD.resetPassword,
        description: FORGOT_PASSWORD.resetPasswordDescription,
        buttonTitle: 'Reset',
      },
    };

    return dataByStep[step] || {};
  }, [step]);

  const submitStep = async (data: FormValues) => {
    try {
      switch (step) {
        case 1:
          const responseStep1 = await forgotPassword({
            email: data.username,
            type: 'send',
          });
          if (responseStep1) setStep(step + 1);
          break;
        case 2:
          const responseStep2 = await forgotPassword({
            email: data.username,
            type: 'verify',
            otp: data.verificationCode,
          });
          if (responseStep2) setStep(step + 1);
          break;
        case 3:
          await setNewPassword({
            email: data.username,
            otp: data.verificationCode,
            password: data.newPassword,
          });
          setIsVisible(false);
          break;
        default:
          setIsVisible(false);
          setStep(1);
          break;
      }
    } catch (error) {
      console.log('error:', error);
    }
  };

  const onBackToLogin = () => {
    setIsVisible(false);
    reset();
    setStep(1);
  };

  return (
    <CustomModal
      containerStyle={styles.modalStyles}
      isVisible={isVisible}
      setIsVisible={setIsVisible}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      avoidKeyboard={true}
      hideModalContentWhileAnimating={true}>
      <View style={styles.container}>
        <CustomText style={styles.title}>{title}</CustomText>
        <CustomText style={styles.description}>{description}</CustomText>
        <StepContent
          key={step}
          step={step}
          control={control}
          errors={errors}
          getValues={getValues}
        />
        <View style={styles.btnsWrapper}>
          <CustomButton
            title="Back to Login"
            variant="secondary"
            style={styles.btnStyle}
            onPress={onBackToLogin}
          />
          <CustomButton
            title={buttonTitle as string}
            style={styles.btnStyle}
            onPress={handleSubmit(submitStep)}
          />
        </View>
      </View>
    </CustomModal>
  );
};

export default ForgetPasswordModalStepper;
