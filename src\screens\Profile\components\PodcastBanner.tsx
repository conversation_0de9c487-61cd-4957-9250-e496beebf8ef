import React, { useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

import PodcastMic from '../../../assets/svgs/PodcastMic';
import CustomText from '../../../components/common/text';
import { ColorScheme } from '../../../constants/theme/colors';
import { useAppSelector } from '../../../hooks/redux';
import useTypeSafeNavigation from '../../../hooks/useTypeSafeNavigation';
import { APP_ROUTES } from '../../../types/routes';

const PodcastBanner = () => {
  const navigation = useTypeSafeNavigation();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const handleNavigate = () => {
    navigation.navigate(APP_ROUTES.UPLOAD_PODCAST);
  };

  return (
    <Pressable onPress={handleNavigate} style={styles.container}>
      <PodcastMic />
      <View style={{ gap: verticalScale(5) }}>
        <CustomText style={styles.ownPodcast}>
          Add your own podcast episodes
        </CustomText>
        <CustomText style={styles.moreInfo}>
          Click here for more info
        </CustomText>
      </View>
    </Pressable>
  );
};

export default PodcastBanner;

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.surface,
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(10),
      borderRadius: moderateScale(8),
      padding: moderateScale(10),
      marginVertical: verticalScale(25),
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 3,
    },
    ownPodcast: {
      color: colors.foreground,
      fontSize: moderateScale(15),
      fontWeight: '500',
    },
    moreInfo: {
      color: colors.secondary,
      fontSize: moderateScale(14),
      fontWeight: '400',
    },
  });
