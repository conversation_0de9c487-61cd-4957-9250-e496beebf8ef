import { FC, useMemo } from 'react';
import { StyleSheet } from 'react-native';
import {
  Menu,
  MenuOption,
  MenuOptions,
  MenuTrigger,
} from 'react-native-popup-menu';

import { KebabMenuIcon } from '../../../../assets/svgs';
import { ColorScheme } from '../../../../constants/theme/colors';
import { useAppDispatch, useAppSelector } from '../../../../hooks/redux';
import { hidePost } from '../../../../redux/slices/postsSlice';

interface KebabMenuProps {
  id: string;
}

export const KebabMenu: FC<KebabMenuProps> = ({ id }) => {
  const dispatch = useAppDispatch();
  const { colors } = useAppSelector(state => state.theme);
  const styles = useMemo(() => createStyles(colors), [colors]);

  const handleHidePost = async () => {
    await dispatch(hidePost(id));
  };

  return (
    <Menu>
      <MenuTrigger style={styles.triggerButton}>
        <KebabMenuIcon fill={colors.foreground} />
      </MenuTrigger>
      <MenuOptions
        customStyles={{
          optionsContainer: styles.optionsContainer,
        }}>
        <MenuOption
          customStyles={{
            optionText: styles.optionText,
          }}
          onSelect={handleHidePost}
          text="Hide this video"
        />
      </MenuOptions>
    </Menu>
  );
};

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    triggerButton: {
      padding: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.background,
    },
    optionsContainer: {
      backgroundColor: colors.background,
      borderRadius: 10,
      padding: 10,
      top: 20,
      marginTop: 40,
    },
    optionText: {
      color: colors.foreground,
    },
  });
