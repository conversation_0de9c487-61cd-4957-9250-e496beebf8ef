import { CPN, REST_SUB_URL } from '../../constants/common/api';
import { Playlist } from '../../types/playlist';

import { API } from './ApiInstance';

export const fetchPlaylistService = async (payload: {
  id: string;
  channel_id?: string;
}): Promise<Playlist | null> => {
  const response = await API.Post(`${CPN}${REST_SUB_URL.PLAYLIST}`, payload);
  // @ts-ignore
  if (response.status) return response?.data as Playlist[];

  return null;
};
